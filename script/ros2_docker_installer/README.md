# ROS2 Foxy Docker 安装器

这是一个完整的 ROS2 Foxy Docker 环境安装和管理工具集，支持多种 Linux 发行版。

## 📁 文件说明

### 核心脚本

- **`install_ros2_docker_ubuntu2004.sh`** - 主安装脚本

  - 自动安装 Docker 和依赖
  - 下载 ROS2 Foxy 镜像
  - 创建完整的开发环境
  - 配置便捷管理脚本

- **`quick_start_ros2_foxy.sh`** - 快速启动脚本

  - 快速启动 ROS2 环境
  - 环境状态检查
  - 容器管理功能

- **`test_mine_mapping.sh`** - 映射测试脚本
  - 测试宿主机/mine 目录映射
  - 验证双向文件同步
  - 环境完整性检查

### 文档

- **`ROS2_Foxy_Docker_安装指南.md`** - 详细使用文档
- **`README.md`** - 本文档

## 🚀 快速开始

### 方式 1: 一键安装（推荐新手）

```bash
# 从主目录
./ros2_foxy_installer.sh --one-click

# 或直接运行
cd ros2_docker_installer
./one_click_install.sh
```

### 方式 2: 交互式向导

```bash
# 从主目录
./ros2_foxy_installer.sh

# 或直接运行
cd ros2_docker_installer
./setup_wizard.sh
```

### 方式 3: 直接安装

```bash
cd ros2_docker_installer
./install_ros2_docker_ubuntu2004.sh
```

### 系统检查

```bash
./ros2_foxy_installer.sh --check
```

### 问题修复

```bash
./ros2_foxy_installer.sh --fix-docker
```

## ✨ 主要特性

### 🐳 Docker 环境

- 自动安装最新 Docker CE
- 配置用户权限和服务
- 支持 X11 图形界面转发

### 🤖 ROS2 Foxy

- 完整的 ROS2 Foxy 桌面版
- 预配置开发环境
- 示例代码和包

### 📂 目录映射

- **宿主机 `/mine` ↔ 容器 `/mine`**
- 实时双向文件同步
- 保持文件权限

### 🛠️ 开发工具

- 便捷启动/停止脚本
- 环境状态监控
- 自动化测试工具

## 📋 系统要求

### 支持的操作系统

- **Ubuntu** 18.04+ (推荐 20.04 LTS)
- **Debian** 10+ (Buster 及以上)
- **Arch Linux** (滚动发布)
- **Manjaro** (所有版本)

### 硬件要求

- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接
- **权限**: sudo 权限（仅安装时需要）

## 🔧 安装后的环境

### 工作空间结构

```
~/ros2_docker_ws/
├── docker-compose.yml      # Docker配置
├── start_ros2.sh          # 启动脚本
├── stop_ros2.sh           # 停止脚本
├── clean_ros2.sh          # 清理脚本
├── src/hello_ros2/        # 示例ROS2包
├── launch/                # Launch文件
├── config/                # 配置文件
└── logs/                  # 日志文件
```

### 容器内映射

```
/ros2_ws/                  # ROS2工作空间
/mine/                     # 宿主机/mine目录映射
/tmp/.X11-unix/            # X11显示支持
```

## 📖 使用示例

### 基本 ROS2 操作

```bash
# 启动容器
./quick_start_ros2_foxy.sh

# 在容器内
colcon build
source install/setup.bash
ros2 run hello_ros2 talker
```

### 访问宿主机文件

```bash
# 在容器内访问宿主机/mine目录
cd /mine
ls -la

# 编辑宿主机文件
vim /mine/note/your_file.py

# 运行宿主机脚本
python3 /mine/note/your_script.py
```

### 环境管理

```bash
# 查看环境状态
./quick_start_ros2_foxy.sh --status

# 清理并重启
./quick_start_ros2_foxy.sh --clean

# 查看容器日志
./quick_start_ros2_foxy.sh --logs
```

## 🔍 故障排除

### 常见问题

1. **Docker 权限问题**

   ```bash
   sudo usermod -aG docker $USER
   # 重新登录
   ```

2. **X11 转发问题**

   ```bash
   xhost +local:docker
   ```

3. **容器无法启动**

   ```bash
   sudo systemctl start docker
   ./quick_start_ros2_foxy.sh --status
   ```

4. **映射测试失败**
   ```bash
   # 确保/mine目录存在
   sudo mkdir -p /mine
   sudo chown $USER:$USER /mine
   ```

### 系统特定说明

#### Arch Linux / Manjaro

```bash
# 确保系统是最新的
sudo pacman -Syu

# 如果使用AUR，可以安装docker-compose-v2
yay -S docker-compose-v2
```

#### Ubuntu / Debian

```bash
# 更新包列表
sudo apt update && sudo apt upgrade

# 安装额外的依赖
sudo apt install curl wget gnupg
```

### 获取帮助

```bash
# 查看脚本帮助
./quick_start_ros2_foxy.sh --help

# 运行完整测试
./test_mine_mapping.sh

# 查看详细文档
cat ROS2_Foxy_Docker_安装指南.md
```

## 🔄 更新和维护

### 更新 Docker 镜像

```bash
docker pull osrf/ros:foxy-desktop-full
```

### 清理旧容器

```bash
cd ~/ros2_docker_ws
./clean_ros2.sh
```

### 备份工作空间

```bash
tar -czf ros2_workspace_backup.tar.gz ~/ros2_docker_ws
```

## 📝 版本信息

- **ROS2 版本**: Foxy Fitzroy (LTS)
- **Docker 镜像**: osrf/ros:foxy-desktop-full
- **支持平台**: Linux x86_64
- **创建日期**: 2025-01-19
- **维护状态**: 活跃维护

## 🤝 贡献和反馈

如果遇到问题或有改进建议，请：

1. 检查系统要求是否满足
2. 运行测试脚本诊断问题
3. 查看详细文档和故障排除指南
4. 记录错误信息和系统环境

---

**开始你的 ROS2 开发之旅！** 🚀
