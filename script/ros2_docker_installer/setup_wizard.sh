#!/bin/bash

# ROS2 Foxy Docker 安装向导
# 提供交互式安装和管理界面

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 显示欢迎信息
show_welcome() {
    clear
    log_header "╔══════════════════════════════════════════════════════════════╗"
    log_header "║                ROS2 Foxy Docker 安装向导                     ║"
    log_header "║                                                              ║"
    log_header "║  这个向导将帮助你安装和管理ROS2 Foxy Docker开发环境          ║"
    log_header "║                                                              ║"
    log_header "║  特性:                                                       ║"
    log_header "║  • 完整的ROS2 Foxy桌面版环境                                 ║"
    log_header "║  • 自动Docker安装和配置                                      ║"
    log_header "║  • 宿主机/mine目录映射                                       ║"
    log_header "║  • 便捷的管理工具                                           ║"
    log_header "╚══════════════════════════════════════════════════════════════╝"
    echo
}

# 显示主菜单
show_menu() {
    echo -e "${BOLD}请选择操作:${NC}"
    echo
    echo "1) 🚀 完整安装 ROS2 Foxy Docker环境"
    echo "2) ⚡ 快速启动 ROS2环境"
    echo "3) 🔍 检查环境状态"
    echo "4) 🐳 检查Docker环境"
    echo "5) 🌐 配置Docker镜像源"
    echo "6) 📦 下载ROS2镜像"
    echo "7) 🧪 测试目录映射"
    echo "8) 🛠️  管理容器"
    echo "9) 🏗️  Arch/Manjaro兼容性测试"
    echo "10) 📖 查看文档"
    echo "11) ❓ 帮助和故障排除"
    echo "12) 🚪 退出"
    echo
    echo -n "请输入选择 [1-12]: "
}

# 检查文件是否存在
check_files() {
    local missing_files=()

    if [[ ! -f "$SCRIPT_DIR/install_ros2_docker_ubuntu2004.sh" ]]; then
        missing_files+=("install_ros2_docker_ubuntu2004.sh")
    fi

    if [[ ! -f "$SCRIPT_DIR/quick_start_ros2_foxy.sh" ]]; then
        missing_files+=("quick_start_ros2_foxy.sh")
    fi

    if [[ ! -f "$SCRIPT_DIR/test_mine_mapping.sh" ]]; then
        missing_files+=("test_mine_mapping.sh")
    fi

    if [[ ! -f "$SCRIPT_DIR/test_arch_support.sh" ]]; then
        missing_files+=("test_arch_support.sh")
    fi

    if [[ ! -f "$SCRIPT_DIR/check_docker_env.sh" ]]; then
        missing_files+=("check_docker_env.sh")
    fi

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        echo
        log_info "请确保所有文件都在同一目录下"
        return 1
    fi

    return 0
}

# 智能安装前检查
pre_install_check() {
    log_info "执行安装前检查..."

    # 检查系统兼容性
    if [[ -f "$SCRIPT_DIR/check_system.sh" ]]; then
        log_info "运行系统兼容性检查..."
        if ! "$SCRIPT_DIR/check_system.sh" | tail -5 | grep -q "完全支持\|支持"; then
            log_warning "系统兼容性检查发现问题"
            read -p "是否继续安装? [y/N]: " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                return 1
            fi
        fi
    fi

    # 检查磁盘空间
    local available_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $available_gb -lt 10 ]]; then
        log_warning "磁盘空间不足: ${available_gb}GB 可用"
        log_info "建议至少有10GB可用空间"
        read -p "是否继续安装? [y/N]: " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi

    # 检查网络连接
    if ! ping -c 1 google.com &> /dev/null; then
        log_warning "网络连接可能有问题"
        log_info "安装过程需要下载Docker镜像"
        read -p "是否继续安装? [y/N]: " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi

    return 0
}

# 完整安装
full_install() {
    log_header "=== 完整安装 ROS2 Foxy Docker环境 ==="
    echo

    # 显示安装信息
    log_info "安装内容:"
    echo "  🔧 检查并更新系统"
    echo "  🐳 安装Docker和依赖"
    echo "  📦 下载ROS2 Foxy镜像 (~2GB)"
    echo "  📁 创建工作空间和示例代码"
    echo "  🛠️  配置便捷管理脚本"
    echo "  🔗 映射宿主机/mine目录"
    echo

    # 显示系统信息
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        log_info "当前系统: $NAME $VERSION"
    fi

    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    log_info "系统资源: ${mem_gb}GB内存, ${disk_gb}GB可用磁盘"
    echo

    # 安装前检查
    if ! pre_install_check; then
        log_info "安装已取消"
        return
    fi

    echo
    read -p "确认开始安装? [y/N]: " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "🚀 开始安装..."
        echo
        cd "$SCRIPT_DIR"

        # 运行安装脚本
        if ./install_ros2_docker_ubuntu2004.sh; then
            echo
            log_success "✅ 安装完成！"
            echo
            log_info "下一步:"
            echo "  1. 如果有Docker权限问题，运行: newgrp docker"
            echo "  2. 启动ROS2环境: cd ~/ros2_docker_ws && ./start_ros2.sh"
            echo "  3. 查看快速指南: cat ~/ROS2_Foxy_快速启动.md"
            echo
            read -p "是否现在启动ROS2环境? [y/N]: " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                quick_start
            fi
        else
            echo
            log_error "❌ 安装失败"
            log_info "请查看错误信息，或运行故障排除工具"
        fi
    else
        log_info "安装已取消"
    fi
}

# 智能快速启动
quick_start() {
    log_header "=== 快速启动 ROS2环境 ==="
    echo

    # 检查工作空间是否存在
    if [[ ! -d ~/ros2_docker_ws ]]; then
        log_error "ROS2工作空间不存在"
        log_info "请先运行完整安装"
        read -p "按回车键继续..."
        return
    fi

    # 检查Docker状态
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        log_info "请先运行完整安装"
        read -p "按回车键继续..."
        return
    fi

    # 检查Docker服务
    if ! systemctl is-active docker &> /dev/null; then
        log_warning "Docker服务未运行，正在启动..."
        sudo systemctl start docker
        sleep 3
    fi

    # 检查Docker权限
    if ! docker info &> /dev/null; then
        log_warning "Docker权限问题"
        log_info "尝试解决方案:"
        echo "  1. 运行 newgrp docker"
        echo "  2. 重新登录系统"
        echo "  3. 使用sudo运行"
        echo
        read -p "选择解决方案 [1-3]: " -n 1 -r
        echo

        case $REPLY in
            1)
                log_info "激活docker组权限..."
                exec newgrp docker -c "$SCRIPT_DIR/quick_start_ros2_foxy.sh"
                return
                ;;
            2)
                log_info "请重新登录后再试"
                return
                ;;
            3)
                log_info "使用sudo启动..."
                ;;
            *)
                log_info "取消启动"
                return
                ;;
        esac
    fi

    cd "$SCRIPT_DIR"
    ./quick_start_ros2_foxy.sh
}

# 检查状态
check_status() {
    log_header "=== 环境状态检查 ==="
    echo

    cd "$SCRIPT_DIR"
    ./quick_start_ros2_foxy.sh --status

    echo
    read -p "按回车键继续..."
}

# 检查Docker环境
check_docker_env() {
    log_header "=== Docker环境检查 ==="
    echo

    cd "$SCRIPT_DIR"
    ./check_docker_env.sh

    echo
    read -p "按回车键继续..."
}

# 配置Docker镜像源
configure_docker_mirrors() {
    log_header "=== 配置Docker镜像源 ==="
    echo

    log_info "选择镜像源配置方案："
    echo "1) 自动配置（推荐）"
    echo "2) 阿里云镜像源"
    echo "3) 腾讯云镜像源"
    echo "4) DockerProxy镜像源"
    echo "5) 多个镜像源"
    echo "6) 交互式选择镜像源"
    echo "7) 自定义镜像源URL"
    echo "8) 返回主菜单"
    echo
    echo -n "请选择 [1-8]: "

    read -n 1 -r choice
    echo
    echo

    cd "$SCRIPT_DIR"

    case $choice in
        1)
            ./fix_docker.sh --configure-mirrors
            ;;
        2)
            ./download_ros2_image.sh --aliyun --test
            ;;
        3)
            ./download_ros2_image.sh --tencent --test
            ;;
        4)
            ./download_ros2_image.sh --dockerproxy --test
            ;;
        5)
            ./download_ros2_image.sh --multiple --test
            ;;
        6)
            ./download_ros2_image.sh --interactive
            ;;
        7)
            echo
            read -p "请输入自定义镜像源URL: " custom_url
            if [[ -n "$custom_url" ]]; then
                ./download_ros2_image.sh --custom "$custom_url"
            else
                log_error "URL不能为空"
            fi
            ;;
        8)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo
    read -p "按回车键继续..."
}

# 下载ROS2镜像
download_ros2_image() {
    log_header "=== 下载ROS2镜像 ==="
    echo

    cd "$SCRIPT_DIR"
    ./download_ros2_image.sh --auto

    echo
    read -p "按回车键继续..."
}

# 测试映射
test_mapping() {
    log_header "=== 测试目录映射 ==="
    echo

    cd "$SCRIPT_DIR"
    ./test_mine_mapping.sh

    echo
    read -p "按回车键继续..."
}

# Arch/Manjaro兼容性测试
test_arch_support() {
    log_header "=== Arch/Manjaro 兼容性测试 ==="
    echo

    cd "$SCRIPT_DIR"

    echo "1) 基本兼容性检查"
    echo "2) 完整Docker功能测试"
    echo "3) 返回主菜单"
    echo
    echo -n "请选择 [1-3]: "

    read -n 1 -r choice
    echo
    echo

    case $choice in
        1)
            ./test_arch_support.sh
            ;;
        2)
            ./test_arch_support.sh --test-docker
            ;;
        3)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo
    read -p "按回车键继续..."
}

# 管理容器
manage_containers() {
    log_header "=== 容器管理 ==="
    echo

    echo "1) 查看容器状态"
    echo "2) 启动容器"
    echo "3) 停止容器"
    echo "4) 重启容器"
    echo "5) 查看容器日志"
    echo "6) 清理环境"
    echo "7) 返回主菜单"
    echo
    echo -n "请选择操作 [1-7]: "

    read -n 1 -r choice
    echo
    echo

    cd "$SCRIPT_DIR"

    case $choice in
        1)
            ./quick_start_ros2_foxy.sh --status
            ;;
        2)
            ./quick_start_ros2_foxy.sh
            ;;
        3)
            if [[ -d ~/ros2_docker_ws ]]; then
                cd ~/ros2_docker_ws
                ./stop_ros2.sh
            else
                log_error "ROS2工作空间不存在"
            fi
            ;;
        4)
            ./quick_start_ros2_foxy.sh --clean
            ;;
        5)
            ./quick_start_ros2_foxy.sh --logs
            ;;
        6)
            if [[ -d ~/ros2_docker_ws ]]; then
                cd ~/ros2_docker_ws
                ./clean_ros2.sh
            else
                log_error "ROS2工作空间不存在"
            fi
            ;;
        7)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo
    read -p "按回车键继续..."
}

# 查看文档
view_docs() {
    log_header "=== 文档和说明 ==="
    echo

    echo "1) 查看README"
    echo "2) 查看详细安装指南"
    echo "3) 查看脚本帮助"
    echo "4) 返回主菜单"
    echo
    echo -n "请选择 [1-4]: "

    read -n 1 -r choice
    echo
    echo

    case $choice in
        1)
            if [[ -f "$SCRIPT_DIR/README.md" ]]; then
                less "$SCRIPT_DIR/README.md"
            else
                log_error "README.md文件不存在"
            fi
            ;;
        2)
            if [[ -f "$SCRIPT_DIR/ROS2_Foxy_Docker_安装指南.md" ]]; then
                less "$SCRIPT_DIR/ROS2_Foxy_Docker_安装指南.md"
            else
                log_error "安装指南文件不存在"
            fi
            ;;
        3)
            cd "$SCRIPT_DIR"
            ./quick_start_ros2_foxy.sh --help
            ;;
        4)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo
    read -p "按回车键继续..."
}

# 帮助和故障排除
show_help() {
    log_header "=== 帮助和故障排除 ==="
    echo

    echo -e "${BOLD}常见问题:${NC}"
    echo
    echo "1. Docker权限问题:"
    echo "   sudo usermod -aG docker \$USER"
    echo "   然后重新登录"
    echo
    echo "2. X11转发问题:"
    echo "   xhost +local:docker"
    echo
    echo "3. 容器无法启动:"
    echo "   sudo systemctl start docker"
    echo
    echo "4. /mine目录映射问题:"
    echo "   sudo mkdir -p /mine"
    echo "   sudo chown \$USER:\$USER /mine"
    echo
    echo -e "${BOLD}获取更多帮助:${NC}"
    echo "• 运行测试脚本: ./test_mine_mapping.sh"
    echo "• 查看容器状态: ./quick_start_ros2_foxy.sh --status"
    echo "• 查看详细文档: cat ROS2_Foxy_Docker_安装指南.md"
    echo

    read -p "按回车键继续..."
}

# 主循环
main() {
    # 检查必要文件
    if ! check_files; then
        exit 1
    fi

    while true; do
        show_welcome
        show_menu

        read -n 1 -r choice
        echo
        echo

        case $choice in
            1)
                full_install
                ;;
            2)
                quick_start
                ;;
            3)
                check_status
                ;;
            4)
                check_docker_env
                ;;
            5)
                configure_docker_mirrors
                ;;
            6)
                download_ros2_image
                ;;
            7)
                test_mapping
                ;;
            8)
                manage_containers
                ;;
            9)
                test_arch_support
                ;;
            10)
                view_docs
                ;;
            11)
                show_help
                ;;
            12)
                log_info "感谢使用ROS2 Foxy Docker安装向导！"
                exit 0
                ;;
            *)
                log_error "无效选择，请输入1-12"
                sleep 2
                ;;
        esac
    done
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
