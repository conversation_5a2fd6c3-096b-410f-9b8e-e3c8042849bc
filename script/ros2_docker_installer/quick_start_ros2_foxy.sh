#!/bin/bash

# ROS2 Foxy 快速启动脚本
# 用于快速启动已安装的ROS2 Foxy Docker环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查工作空间是否存在
check_workspace() {
    if [[ ! -d ~/ros2_docker_ws ]]; then
        log_error "ROS2工作空间不存在！"
        log_info "请先运行完整安装脚本: ./install_ros2_docker_ubuntu2004.sh"
        exit 1
    fi
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行！"
        log_info "启动Docker服务..."
        sudo systemctl start docker
        sleep 3
        if ! docker info > /dev/null 2>&1; then
            log_error "无法启动Docker服务"
            exit 1
        fi
    fi
    log_success "Docker服务正常运行"
}

# 检查ROS2 Foxy镜像是否存在
check_foxy_image() {
    if ! docker images | grep -q "osrf/ros.*foxy"; then
        log_warning "ROS2 Foxy镜像不存在，正在下载..."
        docker pull osrf/ros:foxy-desktop-full
        log_success "ROS2 Foxy镜像下载完成"
    else
        log_success "ROS2 Foxy镜像已存在"
    fi
}

# 启动ROS2 Foxy环境
start_foxy() {
    log_info "启动ROS2 Foxy环境..."

    cd ~/ros2_docker_ws

    # 允许X11转发
    xhost +local:docker 2>/dev/null || true

    # 检查容器是否已经运行
    if docker ps | grep -q "ros2-foxy-container"; then
        log_info "ROS2 Foxy容器已在运行"
        log_info "进入容器..."
        docker exec -it ros2-foxy-container bash
    else
        # 启动容器
        docker-compose up -d ros2-foxy

        # 等待容器启动
        sleep 3

        # 进入容器
        log_success "ROS2 Foxy容器启动成功"
        log_info "进入容器..."
        docker exec -it ros2-foxy-container bash
    fi
}

# 显示使用帮助
show_help() {
    echo "ROS2 Foxy 快速启动脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -s, --status   显示ROS2环境状态"
    echo "  -c, --clean    清理并重启环境"
    echo "  -l, --logs     查看容器日志"
    echo
    echo "示例:"
    echo "  $0              # 启动ROS2 Foxy环境"
    echo "  $0 --status     # 查看环境状态"
    echo "  $0 --clean      # 清理并重启"
}

# 显示环境状态
show_status() {
    log_info "ROS2 Foxy环境状态:"
    echo

    # 检查工作空间
    if [[ -d ~/ros2_docker_ws ]]; then
        echo "✓ 工作空间: ~/ros2_docker_ws"
        echo "✓ 宿主机映射: /mine -> 容器/mine"
    else
        echo "✗ 工作空间: 不存在"
    fi

    # 检查Docker
    if docker info > /dev/null 2>&1; then
        echo "✓ Docker: 运行中"
    else
        echo "✗ Docker: 未运行"
    fi

    # 检查镜像
    if docker images | grep -q "osrf/ros.*foxy"; then
        echo "✓ ROS2 Foxy镜像: 已安装"
        docker images | grep "osrf/ros.*foxy" | head -1
    else
        echo "✗ ROS2 Foxy镜像: 未安装"
    fi

    # 检查容器状态
    if docker ps | grep -q "ros2-foxy-container"; then
        echo "✓ ROS2 Foxy容器: 运行中"
    elif docker ps -a | grep -q "ros2-foxy-container"; then
        echo "○ ROS2 Foxy容器: 已停止"
    else
        echo "✗ ROS2 Foxy容器: 不存在"
    fi
}

# 清理并重启环境
clean_restart() {
    log_info "清理并重启ROS2 Foxy环境..."

    cd ~/ros2_docker_ws

    # 停止容器
    docker-compose down 2>/dev/null || true

    # 清理
    docker system prune -f

    # 重新启动
    start_foxy
}

# 查看容器日志
show_logs() {
    log_info "ROS2 Foxy容器日志:"
    if docker ps -a | grep -q "ros2-foxy-container"; then
        docker logs ros2-foxy-container
    else
        log_error "ROS2 Foxy容器不存在"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--status)
            show_status
            ;;
        -c|--clean)
            check_workspace
            check_docker
            clean_restart
            ;;
        -l|--logs)
            show_logs
            ;;
        "")
            check_workspace
            check_docker
            check_foxy_image
            start_foxy
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
