#!/bin/bash

# Docker镜像源快速设置脚本
# 支持自定义镜像源设置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 设置镜像源
set_mirror() {
    local mirror_url="$1"
    
    log_info "设置Docker镜像源: $mirror_url"
    
    # 验证URL格式
    if [[ ! "$mirror_url" =~ ^https?:// ]]; then
        log_error "无效的URL格式，必须以http://或https://开头"
        return 1
    fi
    
    # 停止Docker服务
    log_info "停止Docker服务..."
    sudo systemctl stop docker
    sudo pkill -f docker 2>/dev/null || true
    sleep 2
    
    # 备份原配置
    if [[ -f /etc/docker/daemon.json ]]; then
        sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%s)
        log_info "已备份原配置"
    fi
    
    # 写入新配置
    log_info "写入镜像源配置..."
    sudo tee /etc/docker/daemon.json > /dev/null << EOF
{
  "registry-mirrors": [
    "$mirror_url"
  ],
  "dns": ["8.8.8.8", "8.8.4.4"],
  "max-concurrent-downloads": 3
}
EOF
    
    # 验证配置文件
    if ! python3 -m json.tool /etc/docker/daemon.json > /dev/null 2>&1; then
        log_error "配置文件JSON格式错误"
        return 1
    fi
    
    log_info "配置文件内容："
    cat /etc/docker/daemon.json
    
    # 重启Docker
    log_info "重启Docker服务..."
    sudo systemctl daemon-reload
    sudo systemctl start docker
    
    # 等待Docker启动
    local retry=0
    while ! docker info &> /dev/null && [[ $retry -lt 10 ]]; do
        sleep 2
        ((retry++))
        echo -n "."
    done
    echo
    
    if docker info &> /dev/null; then
        log_success "Docker服务启动成功"
        
        # 验证镜像源配置
        if docker info | grep -q "Registry Mirrors"; then
            log_success "✅ 镜像源配置已生效"
            echo "当前镜像源："
            docker info | grep -A 5 "Registry Mirrors"
        else
            log_error "❌ 镜像源配置未生效"
            return 1
        fi
    else
        log_error "Docker服务启动失败"
        return 1
    fi
}

# 测试镜像源
test_mirror() {
    log_info "测试镜像源连接..."
    
    if timeout 30 docker pull hello-world &> /dev/null; then
        log_success "✅ 镜像源连接正常"
        docker rmi hello-world &> /dev/null || true
        return 0
    else
        log_error "❌ 镜像源连接失败"
        return 1
    fi
}

# 显示当前配置
show_current_config() {
    log_header "=== 当前Docker配置 ==="
    
    if [[ -f /etc/docker/daemon.json ]]; then
        echo "配置文件内容："
        cat /etc/docker/daemon.json
        echo
        
        if docker info &> /dev/null; then
            echo "Docker信息中的镜像源："
            docker info | grep -A 10 "Registry Mirrors" || echo "未找到镜像源配置"
        else
            log_warning "Docker服务未运行"
        fi
    else
        log_info "未找到Docker配置文件"
    fi
}

# 显示帮助
show_help() {
    echo "Docker镜像源设置工具"
    echo
    echo "用法: $0 [选项] [镜像源URL]"
    echo
    echo "选项:"
    echo "  --set URL       设置指定的镜像源"
    echo "  --test          测试当前镜像源"
    echo "  --show          显示当前配置"
    echo "  --reset         重置为默认配置"
    echo "  --help          显示此帮助"
    echo
    echo "常用镜像源:"
    echo "  阿里云:    https://registry.cn-hangzhou.aliyuncs.com"
    echo "  腾讯云:    https://mirror.ccs.tencentyun.com"
    echo "  DockerProxy: https://dockerproxy.com"
    echo "  DaoCloud:  https://docker.m.daocloud.io"
    echo "  中科大:    https://docker.mirrors.ustc.edu.cn"
    echo "  网易:      https://hub-mirror.c.163.com"
    echo
    echo "示例:"
    echo "  $0 --set https://dockerproxy.com"
    echo "  $0 --test"
    echo "  $0 --show"
}

# 重置配置
reset_config() {
    log_info "重置Docker配置为默认..."
    
    sudo systemctl stop docker
    
    if [[ -f /etc/docker/daemon.json ]]; then
        sudo mv /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%s)
        log_info "已备份并移除配置文件"
    fi
    
    sudo systemctl start docker
    sleep 5
    
    if docker info &> /dev/null; then
        log_success "✅ Docker已重置为默认配置"
    else
        log_error "❌ Docker重置失败"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        --set)
            if [[ -n "$2" ]]; then
                if set_mirror "$2"; then
                    echo
                    log_info "测试新镜像源..."
                    test_mirror
                else
                    log_error "镜像源设置失败"
                    exit 1
                fi
            else
                log_error "请提供镜像源URL"
                echo "用法: $0 --set https://your-mirror.com"
                exit 1
            fi
            ;;
        --test)
            test_mirror
            ;;
        --show)
            show_current_config
            ;;
        --reset)
            reset_config
            ;;
        --help|-h|"")
            show_help
            ;;
        *)
            # 如果第一个参数是URL，直接设置
            if [[ "$1" =~ ^https?:// ]]; then
                set_mirror "$1"
                echo
                test_mirror
            else
                log_error "未知选项: $1"
                show_help
                exit 1
            fi
            ;;
    esac
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
