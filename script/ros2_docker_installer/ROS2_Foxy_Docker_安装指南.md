# ROS2 Foxy Docker 安装和使用指南

## 概述

这是一个完整的 ROS2 Foxy Docker 安装脚本，支持多种 Linux 发行版，可以快速部署 ROS2 Foxy 开发环境。

## 支持的系统

- **Ubuntu** 18.04+ (推荐 20.04 LTS)
- **Debian** 10+ (<PERSON> 及以上)
- **Arch Linux** (滚动发布)
- **Manjaro** (所有版本)

## 文件说明

- `install_ros2_docker_ubuntu2004.sh` - 完整安装脚本
- `quick_start_ros2_foxy.sh` - 快速启动脚本
- `ROS2_Foxy_Docker_安装指南.md` - 本文档

## 快速开始

### 1. 运行完整安装

```bash
# 下载并运行安装脚本
./install_ros2_docker_ubuntu2004.sh
```

安装脚本将自动完成以下操作：

- 检查系统环境
- 安装 Docker 和相关依赖
- 下载 ROS2 Foxy Docker 镜像
- 创建工作空间和示例代码
- 配置便捷启动脚本

### 2. 快速启动 ROS2 环境

```bash
# 使用快速启动脚本
./quick_start_ros2_foxy.sh

# 或者手动启动
cd ~/ros2_docker_ws
./start_ros2.sh
```

### 3. 测试 ROS2 环境

在容器内运行以下命令测试：

```bash
# 构建示例包
colcon build
source install/setup.bash

# 终端1：启动发布者
ros2 run hello_ros2 talker

# 终端2：启动订阅者（需要新开终端）
ros2 run hello_ros2 listener
```

## 详细功能

### 安装脚本功能

1. **系统检查**

   - 检查用户权限
   - 验证系统版本
   - 更新系统包

2. **Docker 安装**

   - 卸载旧版本 Docker
   - 安装最新 Docker CE
   - 配置用户权限
   - 验证安装

3. **ROS2 环境配置**

   - 下载 ROS2 Foxy 镜像
   - 创建工作空间
   - 生成 Docker Compose 配置
   - 创建示例 ROS2 包

4. **便捷工具**
   - 启动脚本
   - 停止脚本
   - 清理脚本
   - 使用文档

### 快速启动脚本功能

```bash
# 显示帮助
./quick_start_ros2_foxy.sh --help

# 查看环境状态
./quick_start_ros2_foxy.sh --status

# 清理并重启
./quick_start_ros2_foxy.sh --clean

# 查看容器日志
./quick_start_ros2_foxy.sh --logs
```

## 工作空间结构

```
~/ros2_docker_ws/
├── docker-compose.yml      # Docker Compose配置
├── start_ros2.sh          # 启动脚本
├── stop_ros2.sh           # 停止脚本
├── clean_ros2.sh          # 清理脚本
├── README.md              # 详细使用说明
├── src/                   # ROS2源码包
│   └── hello_ros2/        # 示例包
├── launch/                # Launch文件
├── config/                # 配置文件
└── logs/                  # 日志文件

容器内目录映射:
├── /ros2_ws/              # ROS2工作空间
├── /mine/                 # 宿主机/mine目录映射
└── /tmp/.X11-unix/        # X11显示支持
```

## 常用命令

### Docker 相关

```bash
# 查看运行中的容器
docker ps

# 进入ROS2容器
docker exec -it ros2-foxy-container bash

# 查看容器日志
docker logs ros2-foxy-container

# 停止容器
docker-compose down
```

### ROS2 相关

```bash
# 查看ROS2版本
ros2 --version

# 列出所有话题
ros2 topic list

# 查看节点信息
ros2 node list

# 运行示例
ros2 run demo_nodes_cpp talker
ros2 run demo_nodes_py listener
```

### 访问宿主机文件

```bash
# 宿主机的/mine目录已映射到容器的/mine目录
# 在容器内可以直接访问宿主机文件

# 查看宿主机/mine目录内容
ls /mine

# 进入你的项目目录（如果在/mine/note下）
cd /mine/note

# 编辑宿主机文件
vim /mine/note/your_file.py

# 在容器内运行宿主机的脚本
python3 /mine/note/your_script.py
```

## 系统特定说明

### Arch Linux / Manjaro 用户

```bash
# 安装前确保系统是最新的
sudo pacman -Syu

# 启用Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 如果需要docker-compose v2
sudo pacman -S docker-compose

# 或者使用AUR版本
yay -S docker-compose-v2
```

### Ubuntu / Debian 用户

```bash
# 更新包列表
sudo apt update && sudo apt upgrade

# 确保有必要的依赖
sudo apt install curl wget gnupg lsb-release

# 如果遇到GPG密钥问题
sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys [KEY_ID]
```

## 故障排除

### 1. Docker 权限问题

```bash
# 将用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或重启系统
```

### 2. X11 转发问题

```bash
# 允许Docker访问X11
xhost +local:docker

# 检查DISPLAY变量
echo $DISPLAY
```

### 3. 容器无法启动

```bash
# 检查Docker服务
sudo systemctl status docker
sudo systemctl start docker

# 检查镜像是否存在
docker images | grep foxy
```

### 4. 网络连接问题

```bash
# 使用国内镜像源
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
EOF
sudo systemctl restart docker
```

## 开发指南

### 创建新的 ROS2 包

```bash
# 进入容器
docker exec -it ros2-foxy-container bash

# 创建Python包
cd /ros2_ws/src
ros2 pkg create --build-type ament_python my_python_pkg

# 创建C++包
ros2 pkg create --build-type ament_cmake my_cpp_pkg

# 构建包
cd /ros2_ws
colcon build --packages-select my_python_pkg
```

### 使用 Launch 文件

```bash
# 创建launch文件
mkdir -p ~/ros2_docker_ws/launch
cat > ~/ros2_docker_ws/launch/example.launch.py << 'EOF'
from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='hello_ros2',
            executable='talker',
            name='talker'
        ),
        Node(
            package='hello_ros2',
            executable='listener',
            name='listener'
        )
    ])
EOF

# 运行launch文件
ros2 launch /ros2_ws/launch/example.launch.py
```

## 系统要求

- **操作系统**: Linux (推荐 Ubuntu 18.04+)
- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接（用于下载镜像）

## 支持和反馈

如果遇到问题，请检查：

1. 系统是否满足最低要求
2. Docker 服务是否正常运行
3. 网络连接是否稳定
4. 用户是否有足够权限

## 版本信息

- **ROS2 版本**: Foxy Fitzroy
- **Docker 镜像**: osrf/ros:foxy-desktop-full
- **支持平台**: Linux x86_64
- **更新日期**: 2025-01-19
