#!/bin/bash

# Docker环境检测脚本
# 智能检测现有Docker环境状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 检测Docker安装状态
check_docker_installation() {
    log_header "=== Docker安装状态检测 ==="
    echo
    
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version 2>/dev/null || echo "获取版本失败")
        log_success "Docker已安装: $docker_version"
        
        # 检查安装路径
        local docker_path=$(which docker)
        log_info "安装路径: $docker_path"
        
        return 0
    else
        log_error "Docker未安装"
        return 1
    fi
}

# 检测Docker Compose
check_docker_compose() {
    log_info "检测Docker Compose..."
    
    # 检查新版本 docker compose
    if docker compose version &> /dev/null 2>&1; then
        local compose_version=$(docker compose version --short 2>/dev/null || echo "未知版本")
        log_success "Docker Compose (Plugin)已安装: $compose_version"
        return 0
    # 检查旧版本 docker-compose
    elif command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version 2>/dev/null || echo "未知版本")
        log_success "Docker Compose (Standalone)已安装: $compose_version"
        return 0
    else
        log_warning "Docker Compose未安装"
        return 1
    fi
}

# 检测Docker服务状态
check_docker_service() {
    log_info "检测Docker服务状态..."
    
    if systemctl is-active docker &> /dev/null; then
        log_success "Docker服务正在运行"
        
        # 检查启动状态
        if systemctl is-enabled docker &> /dev/null; then
            log_success "Docker服务已设置为开机自启"
        else
            log_warning "Docker服务未设置开机自启"
        fi
        
        return 0
    else
        log_error "Docker服务未运行"
        
        # 尝试启动
        log_info "尝试启动Docker服务..."
        if sudo systemctl start docker; then
            log_success "Docker服务启动成功"
            return 0
        else
            log_error "Docker服务启动失败"
            return 1
        fi
    fi
}

# 检测Docker权限
check_docker_permissions() {
    log_info "检测Docker权限..."
    
    # 检查用户是否在docker组中
    if groups $USER | grep -q docker; then
        log_success "用户 $USER 在docker组中"
    else
        log_warning "用户 $USER 不在docker组中"
    fi
    
    # 测试Docker命令权限
    if docker info &> /dev/null; then
        log_success "Docker命令权限正常"
        return 0
    else
        log_warning "Docker命令权限有问题"
        
        # 测试sudo权限
        if sudo docker info &> /dev/null; then
            log_warning "需要sudo权限运行Docker"
            return 1
        else
            log_error "即使使用sudo也无法运行Docker"
            return 2
        fi
    fi
}

# 检测Docker网络连接
check_docker_network() {
    log_info "检测Docker网络功能..."
    
    # 检查Docker daemon连接
    if docker info &> /dev/null || sudo docker info &> /dev/null; then
        log_success "Docker daemon连接正常"
    else
        log_error "无法连接Docker daemon"
        return 1
    fi
    
    # 测试镜像拉取（使用小镜像）
    log_info "测试镜像拉取功能..."
    local docker_cmd="docker"
    if ! docker info &> /dev/null; then
        docker_cmd="sudo docker"
    fi
    
    if timeout 30 $docker_cmd pull hello-world &> /dev/null; then
        log_success "镜像拉取功能正常"
        # 清理测试镜像
        $docker_cmd rmi hello-world &> /dev/null || true
        return 0
    else
        log_warning "镜像拉取可能有问题（网络或权限）"
        return 1
    fi
}

# 检测Docker存储
check_docker_storage() {
    log_info "检测Docker存储状态..."
    
    local docker_cmd="docker"
    if ! docker info &> /dev/null; then
        docker_cmd="sudo docker"
    fi
    
    # 获取Docker根目录
    local docker_root=$($docker_cmd info --format '{{.DockerRootDir}}' 2>/dev/null || echo "/var/lib/docker")
    log_info "Docker根目录: $docker_root"
    
    # 检查磁盘空间
    local available_gb=$(df -BG "$docker_root" 2>/dev/null | awk 'NR==2{print $4}' | sed 's/G//' || echo "0")
    if [[ $available_gb -gt 10 ]]; then
        log_success "存储空间充足: ${available_gb}GB"
    else
        log_warning "存储空间可能不足: ${available_gb}GB"
    fi
    
    # 显示镜像和容器信息
    local images_count=$($docker_cmd images -q | wc -l 2>/dev/null || echo "0")
    local containers_count=$($docker_cmd ps -aq | wc -l 2>/dev/null || echo "0")
    
    log_info "现有镜像数量: $images_count"
    log_info "现有容器数量: $containers_count"
}

# 生成环境报告
generate_report() {
    echo
    log_header "=== Docker环境检测报告 ==="
    echo
    
    local docker_installed=false
    local compose_installed=false
    local service_running=false
    local permissions_ok=false
    local network_ok=false
    
    # 检查各项状态
    if command -v docker &> /dev/null; then
        docker_installed=true
    fi
    
    if docker compose version &> /dev/null 2>&1 || command -v docker-compose &> /dev/null; then
        compose_installed=true
    fi
    
    if systemctl is-active docker &> /dev/null; then
        service_running=true
    fi
    
    if docker info &> /dev/null; then
        permissions_ok=true
    fi
    
    # 显示状态
    echo "Docker环境状态:"
    if [[ "$docker_installed" == "true" ]]; then
        echo "  ✅ Docker已安装"
    else
        echo "  ❌ Docker未安装"
    fi
    
    if [[ "$compose_installed" == "true" ]]; then
        echo "  ✅ Docker Compose可用"
    else
        echo "  ❌ Docker Compose不可用"
    fi
    
    if [[ "$service_running" == "true" ]]; then
        echo "  ✅ Docker服务运行中"
    else
        echo "  ❌ Docker服务未运行"
    fi
    
    if [[ "$permissions_ok" == "true" ]]; then
        echo "  ✅ Docker权限正常"
    else
        echo "  ❌ Docker权限有问题"
    fi
    
    echo
    
    # 给出建议
    if [[ "$docker_installed" == "true" && "$service_running" == "true" && "$permissions_ok" == "true" ]]; then
        log_success "🎉 Docker环境完全就绪！"
        echo "  可以直接使用现有Docker环境安装ROS2"
        echo "  建议运行: ./one_click_install.sh"
    elif [[ "$docker_installed" == "true" ]]; then
        log_warning "⚠️  Docker已安装但需要配置"
        echo "  建议运行: ./fix_docker.sh"
    else
        log_info "📦 需要安装Docker"
        echo "  建议运行: ./install_ros2_docker_ubuntu2004.sh"
    fi
}

# 主函数
main() {
    log_header "Docker环境智能检测"
    echo
    
    # 执行各项检测
    check_docker_installation
    echo
    
    if command -v docker &> /dev/null; then
        check_docker_compose
        echo
        
        check_docker_service
        echo
        
        check_docker_permissions
        echo
        
        check_docker_network
        echo
        
        check_docker_storage
    fi
    
    # 生成报告
    generate_report
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
