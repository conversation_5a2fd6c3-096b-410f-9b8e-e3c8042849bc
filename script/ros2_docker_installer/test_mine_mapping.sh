#!/bin/bash

# 测试/mine目录映射的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查宿主机/mine目录
check_host_mine() {
    log_info "检查宿主机/mine目录..."
    
    if [[ -d /mine ]]; then
        log_success "宿主机/mine目录存在"
        echo "  目录内容:"
        ls -la /mine | head -10
        if [[ $(ls -la /mine | wc -l) -gt 11 ]]; then
            echo "  ... (更多文件)"
        fi
    else
        log_error "宿主机/mine目录不存在！"
        log_info "请确保/mine目录存在，或修改映射路径"
        return 1
    fi
}

# 创建测试文件
create_test_file() {
    log_info "创建测试文件..."
    
    local test_file="/mine/ros2_docker_test_$(date +%s).txt"
    echo "这是一个测试文件，用于验证Docker容器映射" > "$test_file"
    echo "创建时间: $(date)" >> "$test_file"
    echo "宿主机用户: $(whoami)" >> "$test_file"
    echo "宿主机主机名: $(hostname)" >> "$test_file"
    
    if [[ -f "$test_file" ]]; then
        log_success "测试文件创建成功: $test_file"
        echo "$test_file"
    else
        log_error "测试文件创建失败"
        return 1
    fi
}

# 检查Docker环境
check_docker_env() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        return 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行"
        return 1
    fi
    
    log_success "Docker环境正常"
}

# 检查ROS2工作空间
check_ros2_workspace() {
    log_info "检查ROS2工作空间..."
    
    if [[ -d ~/ros2_docker_ws ]]; then
        log_success "ROS2工作空间存在"
        if [[ -f ~/ros2_docker_ws/docker-compose.yml ]]; then
            log_success "Docker Compose配置文件存在"
        else
            log_error "Docker Compose配置文件不存在"
            return 1
        fi
    else
        log_error "ROS2工作空间不存在，请先运行安装脚本"
        return 1
    fi
}

# 测试容器内映射
test_container_mapping() {
    local test_file="$1"
    
    log_info "测试容器内/mine目录映射..."
    
    cd ~/ros2_docker_ws
    
    # 启动容器（如果未运行）
    if ! docker ps | grep -q "ros2-foxy-container"; then
        log_info "启动ROS2 Foxy容器..."
        docker-compose up -d ros2-foxy
        sleep 5
    fi
    
    # 在容器内测试映射
    log_info "在容器内检查/mine目录..."
    docker exec ros2-foxy-container bash -c "
        echo '=== 容器内/mine目录测试 ==='
        if [[ -d /mine ]]; then
            echo '✓ /mine目录存在'
            echo '目录内容:'
            ls -la /mine | head -5
            
            if [[ -f '$test_file' ]]; then
                echo '✓ 测试文件在容器内可见'
                echo '文件内容:'
                cat '$test_file'
                
                # 在容器内修改文件
                echo '容器内修改时间: \$(date)' >> '$test_file'
                echo '容器内用户: \$(whoami)' >> '$test_file'
                echo '✓ 在容器内成功修改文件'
            else
                echo '✗ 测试文件在容器内不可见'
                exit 1
            fi
        else
            echo '✗ /mine目录在容器内不存在'
            exit 1
        fi
    "
    
    if [[ $? -eq 0 ]]; then
        log_success "容器内映射测试成功"
    else
        log_error "容器内映射测试失败"
        return 1
    fi
}

# 验证双向同步
verify_sync() {
    local test_file="$1"
    
    log_info "验证宿主机和容器的双向同步..."
    
    if [[ -f "$test_file" ]]; then
        log_info "检查宿主机上的文件修改..."
        echo "文件最终内容:"
        cat "$test_file"
        
        if grep -q "容器内修改时间" "$test_file"; then
            log_success "双向同步验证成功！"
        else
            log_error "双向同步验证失败"
            return 1
        fi
    else
        log_error "测试文件在宿主机上消失了"
        return 1
    fi
}

# 清理测试文件
cleanup() {
    local test_file="$1"
    
    if [[ -n "$test_file" && -f "$test_file" ]]; then
        log_info "清理测试文件..."
        rm -f "$test_file"
        log_success "测试文件已清理"
    fi
}

# 主函数
main() {
    log_info "开始测试/mine目录映射..."
    echo
    
    # 检查环境
    check_host_mine || exit 1
    check_docker_env || exit 1
    check_ros2_workspace || exit 1
    
    # 创建测试文件
    test_file=$(create_test_file) || exit 1
    
    # 测试映射
    test_container_mapping "$test_file" || {
        cleanup "$test_file"
        exit 1
    }
    
    # 验证同步
    verify_sync "$test_file" || {
        cleanup "$test_file"
        exit 1
    }
    
    # 清理
    cleanup "$test_file"
    
    echo
    log_success "所有测试通过！/mine目录映射工作正常"
    echo
    log_info "现在你可以在容器内访问宿主机的/mine目录了"
    log_info "使用命令: docker exec -it ros2-foxy-container bash"
    log_info "然后在容器内: cd /mine"
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
