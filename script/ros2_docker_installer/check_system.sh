#!/bin/bash

# 系统兼容性快速检查脚本
# 快速检查系统是否支持ROS2 Docker安装

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 检测系统信息
detect_system() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        DISTRO_ID="$ID"
        DISTRO_NAME="$NAME"
        DISTRO_VERSION="$VERSION"
    else
        DISTRO_ID="unknown"
        DISTRO_NAME="Unknown"
        DISTRO_VERSION="Unknown"
    fi
}

# 主检查函数
main() {
    log_header "ROS2 Foxy Docker 系统兼容性检查"
    echo
    
    # 检测系统
    detect_system
    
    echo "系统信息:"
    echo "  发行版: $DISTRO_NAME"
    echo "  版本: $DISTRO_VERSION"
    echo "  内核: $(uname -r)"
    echo "  架构: $(uname -m)"
    echo
    
    # 检查系统支持
    case "$DISTRO_ID" in
        ubuntu)
            log_success "Ubuntu系统 - 完全支持"
            echo "  推荐版本: 20.04 LTS"
            echo "  包管理器: apt"
            ;;
        debian)
            log_success "Debian系统 - 完全支持"
            echo "  推荐版本: 10+ (Buster及以上)"
            echo "  包管理器: apt"
            ;;
        arch)
            log_success "Arch Linux - 完全支持"
            echo "  滚动发布版本"
            echo "  包管理器: pacman"
            echo "  建议运行: ./test_arch_support.sh"
            ;;
        manjaro)
            log_success "Manjaro - 完全支持"
            echo "  基于Arch Linux"
            echo "  包管理器: pacman"
            echo "  建议运行: ./test_arch_support.sh"
            ;;
        fedora)
            log_warning "Fedora - 部分支持"
            echo "  可能需要手动调整"
            echo "  包管理器: dnf"
            ;;
        centos|rhel)
            log_warning "CentOS/RHEL - 部分支持"
            echo "  可能需要手动调整"
            echo "  包管理器: yum/dnf"
            ;;
        opensuse*)
            log_warning "openSUSE - 部分支持"
            echo "  可能需要手动调整"
            echo "  包管理器: zypper"
            ;;
        *)
            log_warning "未知系统 - 可能支持"
            echo "  系统ID: $DISTRO_ID"
            echo "  建议手动检查Docker支持"
            ;;
    esac
    
    echo
    
    # 检查架构
    case "$(uname -m)" in
        x86_64)
            log_success "x86_64架构 - 完全支持"
            ;;
        aarch64|arm64)
            log_success "ARM64架构 - 支持"
            echo "  注意: 某些Docker镜像可能不可用"
            ;;
        armv7l)
            log_warning "ARM32架构 - 有限支持"
            echo "  注意: 性能可能受限"
            ;;
        *)
            log_error "不支持的架构: $(uname -m)"
            ;;
    esac
    
    echo
    
    # 检查内存
    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $mem_gb -ge 4 ]]; then
        log_success "内存: ${mem_gb}GB - 充足"
    elif [[ $mem_gb -ge 2 ]]; then
        log_warning "内存: ${mem_gb}GB - 可用但可能不足"
        echo "  建议: 至少4GB RAM"
    else
        log_error "内存: ${mem_gb}GB - 不足"
        echo "  最低要求: 4GB RAM"
    fi
    
    # 检查磁盘空间
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $disk_gb -ge 10 ]]; then
        log_success "磁盘空间: ${disk_gb}GB 可用 - 充足"
    elif [[ $disk_gb -ge 5 ]]; then
        log_warning "磁盘空间: ${disk_gb}GB 可用 - 可能不足"
        echo "  建议: 至少10GB可用空间"
    else
        log_error "磁盘空间: ${disk_gb}GB 可用 - 不足"
        echo "  最低要求: 10GB可用空间"
    fi
    
    echo
    
    # 检查网络
    if ping -c 1 google.com &> /dev/null; then
        log_success "网络连接 - 正常"
    else
        log_error "网络连接 - 失败"
        echo "  需要稳定的互联网连接下载Docker镜像"
    fi
    
    echo
    
    # 总结和建议
    log_header "总结和建议:"
    
    case "$DISTRO_ID" in
        ubuntu|debian)
            echo "✓ 你的系统完全支持ROS2 Foxy Docker安装"
            echo "✓ 可以直接运行: ./setup_wizard.sh"
            ;;
        arch|manjaro)
            echo "✓ 你的系统完全支持ROS2 Foxy Docker安装"
            echo "✓ 建议先运行: ./test_arch_support.sh"
            echo "✓ 然后运行: ./setup_wizard.sh"
            ;;
        *)
            echo "! 你的系统可能支持，但未经完全测试"
            echo "! 建议先备份重要数据"
            echo "! 可以尝试运行: ./setup_wizard.sh"
            ;;
    esac
    
    echo
    echo "下一步:"
    echo "1. 运行 ./setup_wizard.sh 开始安装"
    echo "2. 或查看 README.md 了解详细信息"
    echo "3. 如遇问题，查看故障排除指南"
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
