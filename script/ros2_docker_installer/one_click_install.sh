#!/bin/bash

# ROS2 Foxy Docker 一键安装脚本
# 智能检测问题并自动修复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 显示欢迎信息
show_welcome() {
    clear
    log_header "╔══════════════════════════════════════════════════════════════╗"
    log_header "║              ROS2 Foxy Docker 一键安装                       ║"
    log_header "║                                                              ║"
    log_header "║  🚀 自动检测系统环境                                         ║"
    log_header "║  🔧 智能解决常见问题                                         ║"
    log_header "║  📦 完整安装ROS2 Foxy环境                                    ║"
    log_header "║  🎯 开箱即用                                                 ║"
    log_header "╚══════════════════════════════════════════════════════════════╝"
    echo
}

# 智能系统检测
smart_system_check() {
    log_info "🔍 智能系统检测中..."

    # 检测系统类型
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        log_success "系统: $NAME $VERSION"

        case "$ID" in
            ubuntu|debian|arch|manjaro)
                log_success "✅ 系统完全支持"
                ;;
            *)
                log_warning "⚠️  系统可能支持，但未完全测试"
                ;;
        esac
    fi

    # 检查资源
    local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')

    if [[ $mem_gb -ge 4 ]]; then
        log_success "✅ 内存充足: ${mem_gb}GB"
    else
        log_warning "⚠️  内存可能不足: ${mem_gb}GB (建议4GB+)"
    fi

    if [[ $disk_gb -ge 10 ]]; then
        log_success "✅ 磁盘空间充足: ${disk_gb}GB"
    else
        log_warning "⚠️  磁盘空间可能不足: ${disk_gb}GB (建议10GB+)"
    fi

    # 检查网络
    if ping -c 2 -W 5 8.8.8.8 &> /dev/null; then
        if ping -c 2 -W 5 google.com &> /dev/null; then
            log_success "✅ 网络连接正常"
        else
            log_warning "⚠️  DNS解析可能有问题"
        fi
    else
        log_warning "⚠️  网络连接可能有问题"
    fi

    echo
}

# 智能Docker检测和修复
smart_docker_check() {
    log_info "🐳 Docker环境检测..."

    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version 2>/dev/null || echo "未知版本")
        log_success "✅ Docker已安装: $docker_version"

        # 检查Docker Compose
        if docker compose version &> /dev/null 2>&1; then
            local compose_version=$(docker compose version --short 2>/dev/null || echo "未知版本")
            log_success "✅ Docker Compose已安装: $compose_version"
        elif command -v docker-compose &> /dev/null; then
            local compose_version=$(docker-compose --version 2>/dev/null || echo "未知版本")
            log_success "✅ Docker Compose已安装: $compose_version"
        else
            log_warning "⚠️  Docker Compose未安装，将在安装过程中处理"
        fi

        # 检查服务状态
        if systemctl is-active docker &> /dev/null; then
            log_success "✅ Docker服务运行中"
        else
            log_warning "⚠️  Docker服务未运行，正在启动..."
            sudo systemctl start docker
            sleep 3
        fi

        # 检查权限
        if docker info &> /dev/null; then
            log_success "✅ Docker权限正常"
            export DOCKER_READY=true
            return 0
        else
            log_warning "⚠️  Docker权限问题，正在修复..."

            # 添加用户到docker组
            if ! groups $USER | grep -q docker; then
                sudo usermod -aG docker $USER
                log_info "已添加用户到docker组"
            fi

            # 尝试激活权限
            if newgrp docker -c "docker info" &> /dev/null; then
                log_success "✅ Docker权限已修复"
                export DOCKER_FIXED=true
                return 0
            else
                log_warning "⚠️  需要重新登录以激活Docker权限"
                return 1
            fi
        fi
    else
        log_info "📦 Docker未安装，将在安装过程中处理"
        return 0
    fi
}

# 智能安装流程
smart_install() {
    log_header "🚀 开始智能安装流程"
    echo

    # 运行主安装脚本
    cd "$SCRIPT_DIR"
    if ./install_ros2_docker_ubuntu2004.sh; then
        log_success "✅ 主安装流程完成"
        return 0
    else
        log_error "❌ 安装过程中出现问题"
        return 1
    fi
}

# 安装后智能验证
post_install_smart_check() {
    log_info "🔍 安装后验证..."

    # 检查工作空间
    if [[ -d ~/ros2_docker_ws ]]; then
        log_success "✅ ROS2工作空间创建成功"
    else
        log_error "❌ ROS2工作空间创建失败"
        return 1
    fi

    # 检查Docker功能
    local docker_cmd="docker"
    if [[ "$DOCKER_FIXED" == "true" ]]; then
        docker_cmd="newgrp docker -c docker"
    fi

    if eval "$docker_cmd info" &> /dev/null; then
        log_success "✅ Docker功能正常"

        # 测试镜像拉取
        if eval "$docker_cmd images | grep -q foxy"; then
            log_success "✅ ROS2镜像已准备就绪"
        else
            log_warning "⚠️  ROS2镜像可能未完全下载"
            log_info "可以稍后手动下载: docker pull osrf/ros:foxy-desktop-full"
        fi
    else
        log_warning "⚠️  Docker权限仍有问题"
        log_info "建议重新登录后再试"
    fi

    return 0
}

# 显示完成信息
show_completion() {
    echo
    log_header "🎉 安装完成！"
    echo

    log_info "📋 安装总结:"
    echo "  ✅ ROS2 Foxy Docker环境已就绪"
    echo "  ✅ 工作空间: ~/ros2_docker_ws"
    echo "  ✅ 快速指南: ~/ROS2_Foxy_快速启动.md"
    echo

    log_info "🚀 立即开始:"
    if [[ "$DOCKER_FIXED" == "true" ]]; then
        echo "  newgrp docker -c 'cd ~/ros2_docker_ws && ./start_ros2.sh'"
    else
        echo "  cd ~/ros2_docker_ws && ./start_ros2.sh"
    fi
    echo

    log_info "🛠️  如有问题:"
    echo "  • Docker问题: ./fix_docker.sh"
    echo "  • 测试映射: ./test_mine_mapping.sh"
    echo "  • 完整向导: ./setup_wizard.sh"
    echo

    # 询问是否立即启动
    read -p "是否现在启动ROS2环境? [y/N]: " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "🚀 启动ROS2环境..."

        if [[ "$DOCKER_FIXED" == "true" ]]; then
            exec newgrp docker -c "cd ~/ros2_docker_ws && ./start_ros2.sh"
        else
            cd ~/ros2_docker_ws && ./start_ros2.sh
        fi
    fi
}

# 错误处理
handle_error() {
    echo
    log_error "❌ 安装过程中出现错误"
    echo

    log_info "🔧 故障排除建议:"
    echo "  1. 检查网络连接"
    echo "  2. 确保有足够的磁盘空间"
    echo "  3. 运行: ./fix_docker.sh"
    echo "  4. 查看详细日志"
    echo "  5. 使用完整向导: ./setup_wizard.sh"
    echo

    log_info "📞 获取帮助:"
    echo "  • 运行系统检查: ./check_system.sh"
    echo "  • 查看文档: cat README.md"
    echo
}

# 主函数
main() {
    # 错误处理
    trap handle_error ERR

    # 显示欢迎信息
    show_welcome

    # 智能系统检测
    smart_system_check

    # Docker检测
    local docker_status=""
    if smart_docker_check; then
        if [[ "$DOCKER_READY" == "true" ]]; then
            docker_status="ready"
            log_info "🎯 Docker环境就绪，将跳过Docker安装步骤"

            # 验证镜像源配置
            if docker info | grep -q "Registry Mirrors"; then
                log_info "✅ Docker镜像源已配置"
            else
                log_warning "⚠️  Docker镜像源未配置，将在安装过程中配置"
            fi
        elif [[ "$DOCKER_FIXED" == "true" ]]; then
            docker_status="fixed"
            log_info "🔧 Docker权限已修复"
        fi
        echo
    else
        log_warning "Docker权限问题将在安装过程中处理"
        echo
    fi

    # 确认安装
    log_info "准备开始一键安装..."
    echo
    read -p "按回车键开始安装，或Ctrl+C取消: "
    echo

    # 智能安装
    if smart_install; then
        # 安装后验证
        post_install_smart_check

        # 显示完成信息
        show_completion
    else
        handle_error
        exit 1
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
