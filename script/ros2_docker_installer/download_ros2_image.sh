#!/bin/bash

# ROS2 Foxy 镜像专用下载脚本
# 智能配置镜像源并下载ROS2镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 配置Docker镜像源
configure_mirrors() {
    local mirror_set="$1"

    case "$mirror_set" in
        "aliyun")
            log_info "配置阿里云镜像源..."
            sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com"
  ],
  "dns": ["*******", "*******"],
  "max-concurrent-downloads": 3
}
EOF
            ;;
        "tencent")
            log_info "配置腾讯云镜像源..."
            sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ],
  "dns": ["*******", "************"],
  "max-concurrent-downloads": 3
}
EOF
            ;;
        "dockerproxy")
            log_info "配置DockerProxy镜像源..."
            sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://dockerproxy.com"
  ],
  "dns": ["*******", "*******"],
  "max-concurrent-downloads": 1
}
EOF
            ;;
        "multiple")
            log_info "配置多个镜像源..."
            sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://dockerproxy.com",
    "https://docker.m.daocloud.io",
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ],
  "dns": ["*******", "*******", "************"],
  "max-concurrent-downloads": 3,
  "max-download-attempts": 5
}
EOF
            ;;
    esac

    # 重启Docker
    sudo systemctl stop docker
    sudo systemctl daemon-reload
    sudo systemctl start docker
    sleep 5
}

# 测试镜像源
test_mirror() {
    log_info "测试镜像源连接..."
    if timeout 30 docker pull hello-world &> /dev/null; then
        log_success "✅ 镜像源连接正常"
        docker rmi hello-world &> /dev/null || true
        return 0
    else
        log_warning "❌ 镜像源连接失败"
        return 1
    fi
}

# 下载ROS2镜像
download_ros2_image() {
    log_info "开始下载ROS2 Foxy镜像..."
    log_info "镜像大小约2GB，请耐心等待..."
    echo

    # 显示下载进度
    if docker pull osrf/ros:foxy-desktop-full; then
        echo
        log_success "✅ ROS2 Foxy镜像下载成功！"

        # 显示镜像信息
        log_info "镜像信息："
        docker images | grep foxy
        return 0
    else
        echo
        log_error "❌ ROS2 镜像下载失败"
        return 1
    fi
}

# 智能下载流程
smart_download() {
    log_header "🚀 ROS2 Foxy 镜像智能下载"
    echo

    # 检查Docker状态
    if ! docker info &> /dev/null; then
        log_error "Docker未运行或权限不足"
        log_info "请确保Docker正常运行并有足够权限"
        exit 1
    fi

    # 检查镜像是否已存在
    if docker images | grep -q "osrf/ros.*foxy"; then
        log_success "✅ ROS2 Foxy镜像已存在"
        docker images | grep foxy
        read -p "是否重新下载最新版本? [y/N]: " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过下载"
            exit 0
        fi
    fi

    # 尝试不同的镜像源
    local mirrors=("multiple" "aliyun" "dockerproxy" "tencent")
    local success=false

    for mirror in "${mirrors[@]}"; do
        log_info "🔄 尝试使用 $mirror 镜像源..."

        configure_mirrors "$mirror"

        if test_mirror; then
            log_info "📥 开始下载ROS2镜像..."
            if download_ros2_image; then
                success=true
                break
            fi
        fi

        log_warning "当前镜像源失败，尝试下一个..."
        echo
    done

    if [[ "$success" == "true" ]]; then
        echo
        log_success "🎉 ROS2 Foxy镜像下载完成！"
        log_info "现在可以运行: cd ~/ros2_docker_ws && ./start_ros2.sh"
    else
        echo
        log_error "❌ 所有镜像源都失败了"
        log_info "💡 建议："
        echo "  1. 检查网络连接"
        echo "  2. 尝试使用VPN"
        echo "  3. 稍后重试"
        exit 1
    fi
}

# 配置自定义镜像源
configure_custom_mirror() {
    local custom_mirror="$1"

    log_info "配置自定义镜像源: $custom_mirror"

    # 验证URL格式
    if [[ ! "$custom_mirror" =~ ^https?:// ]]; then
        log_error "无效的镜像源URL格式"
        return 1
    fi

    sudo tee /etc/docker/daemon.json > /dev/null << EOF
{
  "registry-mirrors": [
    "$custom_mirror"
  ],
  "dns": ["*******", "*******"],
  "max-concurrent-downloads": 3
}
EOF

    # 重启Docker
    sudo systemctl stop docker
    sudo systemctl daemon-reload
    sudo systemctl start docker
    sleep 5
}

# 交互式设置镜像源
interactive_mirror_setup() {
    log_header "=== 自定义镜像源设置 ==="
    echo

    log_info "常用镜像源列表："
    echo "1) https://registry.cn-hangzhou.aliyuncs.com (阿里云)"
    echo "2) https://dockerproxy.com (DockerProxy)"
    echo "3) https://mirror.ccs.tencentyun.com (腾讯云)"
    echo "4) https://docker.m.daocloud.io (DaoCloud)"
    echo "5) https://docker.mirrors.ustc.edu.cn (中科大)"
    echo "6) https://hub-mirror.c.163.com (网易)"
    echo "7) 自定义输入"
    echo

    read -p "请选择镜像源 [1-7]: " -n 1 -r choice
    echo

    local mirror_url=""
    case $choice in
        1) mirror_url="https://registry.cn-hangzhou.aliyuncs.com" ;;
        2) mirror_url="https://dockerproxy.com" ;;
        3) mirror_url="https://mirror.ccs.tencentyun.com" ;;
        4) mirror_url="https://docker.m.daocloud.io" ;;
        5) mirror_url="https://docker.mirrors.ustc.edu.cn" ;;
        6) mirror_url="https://hub-mirror.c.163.com" ;;
        7)
            echo
            read -p "请输入自定义镜像源URL: " mirror_url
            ;;
        *)
            log_error "无效选择"
            return 1
            ;;
    esac

    if [[ -n "$mirror_url" ]]; then
        log_info "设置镜像源: $mirror_url"
        configure_custom_mirror "$mirror_url"

        # 测试镜像源
        if test_mirror; then
            log_success "✅ 镜像源设置成功"
            return 0
        else
            log_error "❌ 镜像源测试失败"
            return 1
        fi
    fi
}

# 显示帮助
show_help() {
    echo "ROS2 Foxy 镜像下载工具"
    echo
    echo "用法: $0 [选项] [自定义镜像源URL]"
    echo
    echo "选项:"
    echo "  --auto          自动选择最佳镜像源下载"
    echo "  --aliyun        使用阿里云镜像源"
    echo "  --tencent       使用腾讯云镜像源"
    echo "  --dockerproxy   使用DockerProxy镜像源"
    echo "  --multiple      使用多个镜像源"
    echo "  --custom URL    使用自定义镜像源"
    echo "  --interactive   交互式选择镜像源"
    echo "  --test          仅测试镜像源连接"
    echo "  --help          显示此帮助"
    echo
    echo "示例:"
    echo "  $0 --auto                                    # 智能下载（推荐）"
    echo "  $0 --aliyun                                  # 使用阿里云镜像源"
    echo "  $0 --custom https://your-mirror.com         # 使用自定义镜像源"
    echo "  $0 --interactive                             # 交互式选择"
    echo "  $0 --test                                    # 测试当前镜像源"
}

# 主函数
main() {
    case "${1:---auto}" in
        --auto)
            smart_download
            ;;
        --aliyun)
            configure_mirrors "aliyun"
            download_ros2_image
            ;;
        --tencent)
            configure_mirrors "tencent"
            download_ros2_image
            ;;
        --dockerproxy)
            configure_mirrors "dockerproxy"
            download_ros2_image
            ;;
        --multiple)
            configure_mirrors "multiple"
            download_ros2_image
            ;;
        --custom)
            if [[ -n "$2" ]]; then
                configure_custom_mirror "$2"
                if test_mirror; then
                    download_ros2_image
                else
                    log_error "自定义镜像源测试失败"
                    exit 1
                fi
            else
                log_error "请提供镜像源URL"
                echo "用法: $0 --custom https://your-mirror.com"
                exit 1
            fi
            ;;
        --interactive)
            if interactive_mirror_setup; then
                download_ros2_image
            else
                log_error "镜像源设置失败"
                exit 1
            fi
            ;;
        --test)
            test_mirror
            ;;
        --help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
