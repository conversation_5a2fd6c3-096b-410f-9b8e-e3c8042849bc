#!/bin/bash

# ROS2 Foxy Docker 完整安装脚本 - 支持多发行版
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)
# 描述: 支持Ubuntu/Debian/Arch/Manjaro的ROS2 Foxy Docker环境安装

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        DISTRO_ID="$ID"
        DISTRO_NAME="$NAME"
        DISTRO_VERSION="$VERSION"
    else
        log_error "无法检测系统版本"
        exit 1
    fi

    case "$DISTRO_ID" in
        ubuntu|debian)
            PACKAGE_MANAGER="apt"
            INSTALL_CMD="apt-get install -y"
            UPDATE_CMD="apt-get update"
            ;;
        arch|manjaro)
            PACKAGE_MANAGER="pacman"
            INSTALL_CMD="pacman -S --noconfirm"
            UPDATE_CMD="pacman -Sy"
            ;;
        *)
            log_warning "未明确支持的系统: $DISTRO_ID"
            log_info "将尝试使用通用方法安装"
            PACKAGE_MANAGER="unknown"
            ;;
    esac
}

# 检查系统版本
check_system_version() {
    log_info "检查系统版本..."
    detect_system

    log_info "检测到系统: $DISTRO_NAME $DISTRO_VERSION"
    log_info "包管理器: $PACKAGE_MANAGER"

    case "$DISTRO_ID" in
        ubuntu|debian|arch|manjaro)
            log_success "系统支持确认"
            ;;
        *)
            log_warning "当前系统: $DISTRO_ID 可能不完全兼容"
            read -p "是否继续安装？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
            ;;
    esac

    log_success "系统版本检查完成"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."

    case "$PACKAGE_MANAGER" in
        apt)
            sudo apt-get update
            sudo apt-get upgrade -y
            ;;
        pacman)
            sudo pacman -Syu --noconfirm
            ;;
        *)
            log_warning "未知包管理器，跳过系统更新"
            ;;
    esac

    log_success "系统包更新完成"
}

# 安装必要的依赖包
install_dependencies() {
    log_info "安装必要的依赖包..."

    case "$PACKAGE_MANAGER" in
        apt)
            sudo $INSTALL_CMD \
                apt-transport-https \
                ca-certificates \
                curl \
                gnupg \
                lsb-release \
                software-properties-common \
                wget \
                git \
                vim \
                htop
            ;;
        pacman)
            sudo $INSTALL_CMD \
                curl \
                wget \
                git \
                vim \
                htop \
                base-devel
            ;;
        *)
            log_warning "未知包管理器，请手动安装必要依赖"
            ;;
    esac

    log_success "依赖包安装完成"
}

# 智能处理旧版本Docker
handle_old_docker() {
    # 如果已经有可用的Docker，跳过清理
    if command -v docker &> /dev/null && docker --version &> /dev/null; then
        log_info "检测到可用的Docker，跳过旧版本清理"
        return 0
    fi

    log_info "清理可能存在的旧版本Docker..."

    case "$PACKAGE_MANAGER" in
        apt)
            sudo apt-get remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
            ;;
        pacman)
            sudo pacman -R --noconfirm docker docker-compose 2>/dev/null || true
            ;;
        *)
            log_warning "未知包管理器，请手动卸载旧版本Docker"
            ;;
    esac

    log_success "旧版本Docker处理完成"
}

# 检查Docker是否已安装
check_docker_installed() {
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version 2>/dev/null || echo "未知版本")
        log_success "Docker已安装: $docker_version"

        # 检查Docker Compose
        if docker compose version &> /dev/null 2>&1; then
            local compose_version=$(docker compose version --short 2>/dev/null || echo "未知版本")
            log_success "Docker Compose已安装: $compose_version"
        elif command -v docker-compose &> /dev/null; then
            local compose_version=$(docker-compose --version 2>/dev/null || echo "未知版本")
            log_success "Docker Compose已安装: $compose_version"
        else
            log_warning "Docker Compose未安装，将安装"
            return 1
        fi

        return 0
    else
        log_info "Docker未安装，需要安装"
        return 1
    fi
}

# 安装Docker
install_docker() {
    # 先检查是否已安装
    if check_docker_installed; then
        log_info "跳过Docker安装，使用现有环境"
        return 0
    fi

    log_info "安装Docker..."

    case "$PACKAGE_MANAGER" in
        apt)
            # Ubuntu/Debian安装方式
            case "$DISTRO_ID" in
                ubuntu)
                    # 添加Docker官方GPG密钥
                    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

                    # 添加Docker仓库
                    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
                    ;;
                debian)
                    # 添加Docker官方GPG密钥
                    curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

                    # 添加Docker仓库
                    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
                    ;;
            esac

            # 更新包索引
            sudo apt-get update

            # 安装Docker Engine
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
            ;;
        pacman)
            # Arch/Manjaro安装方式
            sudo $INSTALL_CMD docker docker-compose
            ;;
        *)
            log_error "不支持的包管理器: $PACKAGE_MANAGER"
            log_info "请手动安装Docker"
            exit 1
            ;;
    esac

    log_success "Docker安装完成"
}

# 配置Docker用户权限
configure_docker_permissions() {
    log_info "配置Docker用户权限..."

    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER

    # 启动并启用Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker

    log_success "Docker权限配置完成"
    log_warning "请注意：需要重新登录或重启系统以使docker组权限生效"
}

# 智能验证Docker安装
verify_docker() {
    log_info "验证Docker安装..."

    # 检查Docker版本
    if docker --version; then
        log_success "Docker版本检查通过"
    else
        log_error "Docker命令不可用"
        return 1
    fi

    # 检查Docker服务状态
    if ! systemctl is-active docker &> /dev/null; then
        log_warning "Docker服务未运行，正在启动..."
        sudo systemctl start docker
        sleep 3
    fi

    # 智能权限检查和修复
    log_info "检查Docker权限..."
    if docker info &> /dev/null; then
        log_success "Docker权限正常"
    else
        log_warning "Docker权限问题，尝试解决..."

        # 检查用户是否在docker组中
        if ! groups $USER | grep -q docker; then
            log_info "用户不在docker组中，正在添加..."
            sudo usermod -aG docker $USER
            log_warning "已添加到docker组，需要重新登录生效"
        fi

        # 尝试使用newgrp激活权限
        log_info "尝试激活docker组权限..."
        if newgrp docker -c "docker info" &> /dev/null; then
            log_success "权限激活成功"
            export DOCKER_NEWGRP=true
        else
            # 使用sudo进行测试
            log_info "使用sudo进行Docker测试..."
            if sudo docker run --rm hello-world &> /dev/null; then
                log_warning "Docker功能正常，但需要重新登录以获得完整权限"
                log_info "建议安装完成后运行: newgrp docker 或重新登录"
                return 0
            else
                log_error "Docker安装验证失败"
                return 1
            fi
        fi
    fi

    # 运行hello-world测试
    log_info "运行Docker功能测试..."
    local test_cmd="docker run --rm hello-world"

    if [[ "$DOCKER_NEWGRP" == "true" ]]; then
        test_cmd="newgrp docker -c '$test_cmd'"
    fi

    if eval $test_cmd &> /dev/null; then
        log_success "Docker安装验证成功"
        return 0
    else
        log_error "Docker功能测试失败"
        log_info "这可能是网络问题，将在后续步骤中配置镜像源"
        return 1
    fi
}

# 创建ROS2工作目录
create_ros2_workspace() {
    log_info "创建ROS2工作目录..."

    mkdir -p ~/ros2_docker_ws/{src,launch,config}

    log_success "ROS2工作目录创建完成: ~/ros2_docker_ws"
}

# 创建ROS2 Docker Compose文件
create_docker_compose() {
    log_info "创建ROS2 Docker Compose配置..."

    cat > ~/ros2_docker_ws/docker-compose.yml << 'EOF'
version: '3.8'

services:
  ros2-foxy:
    image: osrf/ros:foxy-desktop-full
    container_name: ros2-foxy-container
    stdin_open: true
    tty: true
    network_mode: host
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
      - ROS_DOMAIN_ID=0
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ./src:/ros2_ws/src:rw
      - ./launch:/ros2_ws/launch:rw
      - ./config:/ros2_ws/config:rw
      - ./logs:/ros2_ws/log:rw
      - ~/.bashrc:/root/.bashrc:ro
      - /mine:/mine:rw
    working_dir: /ros2_ws
    command: bash -c "source /opt/ros/foxy/setup.bash && echo 'ROS2 Foxy环境已准备就绪!' && bash"
    restart: unless-stopped

  # 可选的其他版本（如需要可取消注释）
  # ros2-galactic:
  #   image: osrf/ros:galactic-desktop-full
  #   container_name: ros2-galactic-container
  #   stdin_open: true
  #   tty: true
  #   network_mode: host
  #   environment:
  #     - DISPLAY=${DISPLAY}
  #     - QT_X11_NO_MITSHM=1
  #     - ROS_DOMAIN_ID=0
  #   volumes:
  #     - /tmp/.X11-unix:/tmp/.X11-unix:rw
  #     - ./src:/ros2_ws/src:rw
  #     - ./launch:/ros2_ws/launch:rw
  #     - ./config:/ros2_ws/config:rw
  #   working_dir: /ros2_ws
  #   command: bash -c "source /opt/ros/galactic/setup.bash && bash"
EOF

    log_success "Docker Compose配置文件创建完成"
}

# 创建便捷脚本
create_convenience_scripts() {
    log_info "创建便捷使用脚本..."

    # 创建启动脚本
    cat > ~/ros2_docker_ws/start_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Foxy Docker 启动脚本

ROS_DISTRO=${1:-foxy}

echo "启动ROS2 $ROS_DISTRO 容器..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker服务"
    exit 1
fi

# 允许X11转发
xhost +local:docker

# 启动对应的ROS2容器
docker-compose up -d ros2-$ROS_DISTRO

# 等待容器启动
sleep 2

# 进入容器
echo "进入ROS2 $ROS_DISTRO 容器..."
docker exec -it ros2-$ROS_DISTRO-container bash

EOF

    # 创建停止脚本
    cat > ~/ros2_docker_ws/stop_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Docker 停止脚本

echo "停止所有ROS2容器..."
docker-compose down

# 禁用X11转发
xhost -local:docker

echo "ROS2容器已停止"
EOF

    # 创建清理脚本
    cat > ~/ros2_docker_ws/clean_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Docker 清理脚本

echo "清理ROS2 Docker环境..."

# 停止并删除容器
docker-compose down --rmi all --volumes --remove-orphans

# 清理未使用的Docker资源
docker system prune -f

echo "ROS2 Docker环境清理完成"
EOF

    # 设置脚本执行权限
    chmod +x ~/ros2_docker_ws/*.sh

    log_success "便捷脚本创建完成"
}

# 创建示例ROS2包
create_sample_package() {
    log_info "创建示例ROS2包..."

    # 创建示例Python包
    mkdir -p ~/ros2_docker_ws/src/hello_ros2/hello_ros2

    cat > ~/ros2_docker_ws/src/hello_ros2/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>hello_ros2</name>
  <version>0.0.0</version>
  <description>Hello ROS2 example package</description>
  <maintainer email="<EMAIL>">user</maintainer>
  <license>Apache-2.0</license>

  <depend>rclpy</depend>
  <depend>std_msgs</depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/setup.py << 'EOF'
from setuptools import setup

package_name = 'hello_ros2'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='user',
    maintainer_email='<EMAIL>',
    description='Hello ROS2 example package',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'talker = hello_ros2.publisher:main',
            'listener = hello_ros2.subscriber:main',
        ],
    },
)
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/setup.cfg << 'EOF'
[develop]
script-dir=$base/lib/hello_ros2
[install]
install-scripts=$base/lib/hello_ros2
EOF

    mkdir -p ~/ros2_docker_ws/src/hello_ros2/resource
    touch ~/ros2_docker_ws/src/hello_ros2/resource/hello_ros2

    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/__init__.py << 'EOF'
# Hello ROS2 package
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/publisher.py << 'EOF'
import rclpy
from rclpy.node import Node
from std_msgs.msg import String


class MinimalPublisher(Node):

    def __init__(self):
        super().__init__('minimal_publisher')
        self.publisher_ = self.create_publisher(String, 'topic', 10)
        timer_period = 0.5  # seconds
        self.timer = self.create_timer(timer_period, self.timer_callback)
        self.i = 0

    def timer_callback(self):
        msg = String()
        msg.data = 'Hello World: %d' % self.i
        self.publisher_.publish(msg)
        self.get_logger().info('Publishing: "%s"' % msg.data)
        self.i += 1


def main(args=None):
    rclpy.init(args=args)
    minimal_publisher = MinimalPublisher()
    rclpy.spin(minimal_publisher)
    minimal_publisher.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/subscriber.py << 'EOF'
import rclpy
from rclpy.node import Node
from std_msgs.msg import String


class MinimalSubscriber(Node):

    def __init__(self):
        super().__init__('minimal_subscriber')
        self.subscription = self.create_subscription(
            String,
            'topic',
            self.listener_callback,
            10)
        self.subscription  # prevent unused variable warning

    def listener_callback(self, msg):
        self.get_logger().info('I heard: "%s"' % msg.data)


def main(args=None):
    rclpy.init(args=args)
    minimal_subscriber = MinimalSubscriber()
    rclpy.spin(minimal_subscriber)
    minimal_subscriber.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
EOF

    log_success "示例ROS2包创建完成"
}

# 创建使用说明文档
create_documentation() {
    log_info "创建使用说明文档..."

    cat > ~/ros2_docker_ws/README.md << 'EOF'
# ROS2 Foxy Docker 环境使用指南

## 概述
这是一个专门为ROS2 Foxy版本设计的Docker开发环境，提供完整的ROS2 Foxy Fitzroy开发工具链。

## 目录结构
```
ros2_docker_ws/
├── docker-compose.yml      # Docker Compose配置文件
├── start_ros2.sh          # 启动ROS2容器脚本
├── stop_ros2.sh           # 停止ROS2容器脚本
├── clean_ros2.sh          # 清理Docker环境脚本
├── src/                   # ROS2源码包目录
├── launch/                # Launch文件目录
├── config/                # 配置文件目录
├── logs/                  # 日志文件目录
└── README.md              # 本文档

容器内映射目录:
├── /ros2_ws/              # ROS2工作空间
├── /mine/                 # 宿主机/mine目录映射
└── /tmp/.X11-unix/        # X11显示支持
```

## 快速开始

### 1. 启动ROS2 Foxy环境
```bash
cd ~/ros2_docker_ws

# 启动ROS2 Foxy（默认）
./start_ros2.sh

# 或者明确指定Foxy版本
./start_ros2.sh foxy
```

### 2. 在容器中构建包
```bash
# 在容器内执行
colcon build
source install/setup.bash
```

### 3. 访问宿主机文件
```bash
# 宿主机的/mine目录已映射到容器的/mine目录
ls /mine
cd /mine/note  # 如果你的项目在/mine/note下
```

### 3. 运行示例程序
```bash
# 终端1：运行发布者
ros2 run hello_ros2 talker

# 终端2：运行订阅者（需要新开一个容器终端）
ros2 run hello_ros2 listener
```

### 4. 停止环境
```bash
# 退出容器后执行
./stop_ros2.sh
```

## 常用命令

### Docker相关
```bash
# 查看运行中的容器
docker ps

# 进入已运行的容器
docker exec -it ros2-foxy-container bash

# 查看容器日志
docker logs ros2-foxy-container
```

### ROS2相关
```bash
# 列出所有话题
ros2 topic list

# 查看话题信息
ros2 topic info /topic

# 监听话题
ros2 topic echo /topic

# 列出所有节点
ros2 node list

# 查看节点信息
ros2 node info /minimal_publisher
```

## 开发指南

### 创建新包
```bash
# 在src目录下创建Python包
ros2 pkg create --build-type ament_python my_package

# 在src目录下创建C++包
ros2 pkg create --build-type ament_cmake my_cpp_package
```

### 构建和测试
```bash
# 构建所有包
colcon build

# 构建特定包
colcon build --packages-select my_package

# 运行测试
colcon test
```

## 故障排除

### GUI应用无法显示
确保已执行X11转发设置：
```bash
xhost +local:docker
```

### 权限问题
确保当前用户在docker组中：
```bash
sudo usermod -aG docker $USER
# 然后重新登录
```

### 容器无法启动
检查Docker服务状态：
```bash
sudo systemctl status docker
sudo systemctl start docker
```

## ROS2版本信息
- **ROS2 Foxy Fitzroy** - 长期支持版本(LTS)
- **发布日期**: 2020年6月
- **支持期限**: 至2023年5月
- **基于**: Ubuntu 20.04 Focal Fossa

## 更多资源
- [ROS2 Foxy官方文档](https://docs.ros.org/en/foxy/)
- [ROS2 Foxy教程](https://docs.ros.org/en/foxy/Tutorials.html)
- [ROS2 Foxy发行说明](https://docs.ros.org/en/foxy/Releases/Release-Foxy-Fitzroy.html)
- [Docker官方文档](https://docs.docker.com/)
EOF

    log_success "使用说明文档创建完成"
}

# 配置Docker镜像源（强制更新）
configure_docker_mirrors() {
    log_info "配置Docker镜像源以提高下载速度..."

    local daemon_json="/etc/docker/daemon.json"

    # 强制更新镜像源配置
    log_info "更新Docker镜像源配置..."

    # 完全停止Docker服务
    log_info "完全停止Docker服务..."
    sudo systemctl stop docker
    sudo pkill -f docker 2>/dev/null || true
    sleep 3

    # 备份原配置
    if [[ -f "$daemon_json" ]]; then
        sudo cp "$daemon_json" "${daemon_json}.backup.$(date +%s)"
        log_info "已备份原配置"
    fi

    # 清理可能的配置冲突
    sudo rm -f /etc/docker/daemon.json.backup* 2>/dev/null || true

    # 创建简单有效的镜像源配置
    log_info "写入镜像源配置..."
    sudo tee "$daemon_json" > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://dockerproxy.com"
  ],
  "dns": ["8.8.8.8", "8.8.4.4"]
}
EOF

    # 验证配置文件语法
    if ! python3 -m json.tool "$daemon_json" > /dev/null 2>&1; then
        log_error "配置文件JSON格式错误"
        return 1
    fi

    log_info "配置文件内容："
    cat "$daemon_json"

    # 重新加载配置并启动Docker
    log_info "重新加载Docker配置..."
    sudo systemctl daemon-reload

    log_info "启动Docker服务..."
    if sudo systemctl start docker; then
        log_success "Docker服务启动成功"

        # 等待Docker完全启动
        log_info "等待Docker完全启动..."
        local retry=0
        while ! docker info &> /dev/null && [[ $retry -lt 15 ]]; do
            sleep 2
            ((retry++))
            echo -n "."
        done
        echo

        if docker info &> /dev/null; then
            log_success "Docker服务就绪"

            # 强制验证配置是否生效
            log_info "验证镜像源配置..."
            if docker info | grep -q "Registry Mirrors"; then
                log_success "✅ 镜像源配置已生效"
                echo "配置的镜像源："
                docker info | grep -A 10 "Registry Mirrors" | head -5
            else
                log_error "❌ 镜像源配置未生效！"
                log_warning "将尝试使用备用配置方案..."

                # 尝试备用配置
                sudo tee "$daemon_json" > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://dockerproxy.com"
  ]
}
EOF
                sudo systemctl restart docker
                sleep 8

                if docker info | grep -q "Registry Mirrors"; then
                    log_success "✅ 备用配置生效"
                else
                    log_error "❌ 镜像源配置完全失败"
                    return 1
                fi
            fi
        else
            log_error "Docker服务启动失败"
            return 1
        fi
    else
        log_error "Docker服务启动失败"
        return 1
    fi

    log_success "Docker镜像源配置完成"
}

# 智能下载ROS2 Foxy Docker镜像
pull_ros2_images() {
    log_info "下载ROS2 Foxy Docker镜像..."

    # 选择合适的docker命令
    local docker_cmd="docker"
    if [[ "$DOCKER_NEWGRP" == "true" ]]; then
        docker_cmd="newgrp docker -c docker"
    elif ! docker info &> /dev/null; then
        docker_cmd="sudo docker"
    fi

    # 智能拉取镜像（强化重试机制）
    log_info "正在拉取 osrf/ros:foxy-desktop-full 镜像..."
    log_info "镜像大小约2GB，下载时间取决于网络速度"
    echo

    local download_success=false
    local attempt=1
    local max_attempts=4

    # 多次尝试下载，每次使用不同策略
    while [[ $attempt -le $max_attempts && "$download_success" == "false" ]]; do
        log_info "📥 下载尝试 $attempt/$max_attempts"
        echo "----------------------------------------"

        # 根据尝试次数使用不同的镜像源配置
        case $attempt in
            1)
                log_info "使用当前镜像源配置..."
                ;;
            2)
                log_info "🔧 尝试DockerProxy镜像源..."
                sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": ["https://dockerproxy.com"]
}
EOF
                sudo systemctl restart docker
                sleep 8
                ;;
            3)
                log_info "🔧 尝试阿里云镜像源..."
                sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": ["https://registry.cn-hangzhou.aliyuncs.com"]
}
EOF
                sudo systemctl restart docker
                sleep 8
                ;;
            4)
                log_info "🔧 尝试腾讯云镜像源..."
                sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": ["https://mirror.ccs.tencentyun.com"]
}
EOF
                sudo systemctl restart docker
                sleep 8
                ;;
        esac

        # 验证镜像源是否生效
        if docker info | grep -q "Registry Mirrors"; then
            log_info "✅ 镜像源配置已生效"
        else
            log_warning "⚠️  镜像源配置未生效，但继续尝试..."
        fi

        # 尝试下载
        log_info "开始下载..."
        if timeout 600 bash -c "$docker_cmd pull osrf/ros:foxy-desktop-full 2>&1"; then
            echo
            log_success "✅ ROS2 Foxy镜像下载成功！"
            download_success=true
            break
        else
            echo
            log_warning "❌ 第 $attempt 次下载失败"

            # 显示错误信息
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "所有镜像源都尝试失败"
            else
                log_info "⏳ 等待5秒后尝试下一个镜像源..."
                sleep 5
            fi
        fi

        ((attempt++))
    done

    if [[ "$download_success" == "false" ]]; then
        echo
        log_error "❌ 所有下载尝试都失败了"
        log_info "💡 可能的解决方案："
        echo "  1. 检查网络连接: ping google.com"
        echo "  2. 使用专用下载脚本: ./download_ros2_image.sh --auto"
        echo "  3. 手动下载: $docker_cmd pull osrf/ros:foxy-desktop-full"
        echo "  4. 使用VPN或代理"
        echo "  5. 稍后重试: cd ~/ros2_docker_ws && ./download_images.sh"

        log_warning "环境已创建，可稍后手动下载镜像"
        return 1
    fi

    # 显示镜像信息
    log_info "镜像信息:"
    $docker_cmd images | grep foxy || true

    log_success "ROS2 Foxy Docker镜像准备完成"
}

# 配置X11转发
configure_x11() {
    log_info "配置X11转发..."

    # 允许本地Docker容器访问X11
    xhost +local:docker

    # 创建持久化的X11配置
    echo "xhost +local:docker" >> ~/.bashrc

    log_success "X11转发配置完成"
}

# 安装后验证和优化
post_install_verification() {
    log_info "执行安装后验证..."

    # 验证工作空间
    if [[ -d ~/ros2_docker_ws ]]; then
        log_success "ROS2工作空间创建成功"
    else
        log_error "ROS2工作空间创建失败"
        return 1
    fi

    # 验证Docker Compose配置
    if [[ -f ~/ros2_docker_ws/docker-compose.yml ]]; then
        log_success "Docker Compose配置文件存在"

        # 验证配置语法
        cd ~/ros2_docker_ws
        if docker-compose config &> /dev/null; then
            log_success "Docker Compose配置语法正确"
        else
            log_warning "Docker Compose配置可能有问题"
        fi
    else
        log_error "Docker Compose配置文件缺失"
        return 1
    fi

    # 验证脚本权限
    local scripts=("start_ros2.sh" "stop_ros2.sh" "clean_ros2.sh")
    for script in "${scripts[@]}"; do
        if [[ -x ~/ros2_docker_ws/$script ]]; then
            log_success "$script 权限正确"
        else
            log_warning "$script 权限可能有问题"
            chmod +x ~/ros2_docker_ws/$script
        fi
    done

    return 0
}

# 生成快速启动指南
generate_quick_start_guide() {
    local guide_file="$HOME/ROS2_Foxy_快速启动.md"

    cat > "$guide_file" << 'EOF'
# ROS2 Foxy Docker 快速启动指南

## 🚀 立即开始

### 1. 启动ROS2环境
```bash
cd ~/ros2_docker_ws
./start_ros2.sh
```

### 2. 在容器内测试
```bash
# 构建示例包
colcon build
source install/setup.bash

# 运行发布者（终端1）
ros2 run hello_ros2 talker

# 运行订阅者（终端2 - 需要新开容器终端）
docker exec -it ros2-foxy-container bash
cd /ros2_ws && source install/setup.bash
ros2 run hello_ros2 listener
```

### 3. 访问宿主机文件
```bash
# 在容器内访问宿主机/mine目录
cd /mine
ls -la
```

### 4. 停止环境
```bash
# 退出容器后
cd ~/ros2_docker_ws
./stop_ros2.sh
```

## 🛠️ 常用命令

- 启动: `./start_ros2.sh`
- 停止: `./stop_ros2.sh`
- 清理: `./clean_ros2.sh`
- 状态: `docker ps`
- 进入: `docker exec -it ros2-foxy-container bash`

## 📚 更多帮助

- 详细文档: `~/ros2_docker_ws/README.md`
- 测试映射: `./test_mine_mapping.sh`
- 问题诊断: `./fix_docker.sh`

---
安装时间: $(date)
EOF

    log_success "快速启动指南已创建: $guide_file"
}

# 主安装函数
main() {
    log_info "开始ROS2 Foxy Docker完整安装..."
    echo

    # 安装前检查
    check_root
    check_system_version

    # 系统准备
    update_system
    install_dependencies

    # Docker安装和配置
    handle_old_docker
    install_docker
    configure_docker_permissions

    # Docker验证（允许失败，后续会修复）
    if ! verify_docker; then
        log_warning "Docker验证失败，但继续安装过程"
        log_info "将在后续步骤中尝试修复"
    fi

    # ROS2环境创建
    create_ros2_workspace
    create_docker_compose
    create_convenience_scripts
    create_sample_package
    create_documentation

    # 镜像下载（智能处理网络问题）
    if ! pull_ros2_images; then
        log_warning "镜像下载失败，但环境已准备就绪"
        log_info "你可以稍后手动下载镜像"
    fi

    # X11配置
    configure_x11

    # 安装后验证
    post_install_verification

    # 生成快速启动指南
    generate_quick_start_guide

    echo
    log_success "🎉 ROS2 Foxy Docker环境安装完成！"
    echo
    log_info "📋 安装总结："
    echo "  ✅ Docker已安装并配置"
    echo "  ✅ ROS2 Foxy工作空间: ~/ros2_docker_ws"
    echo "  ✅ 示例包已创建: hello_ros2"
    echo "  ✅ 便捷脚本已创建"
    echo "  ✅ 宿主机/mine目录已映射到容器/mine"
    echo "  ✅ 快速启动指南: ~/ROS2_Foxy_快速启动.md"
    echo
    log_warning "⚠️  重要提醒："
    echo "  1. 如果Docker权限有问题，运行: newgrp docker"
    echo "  2. 或者重新登录/重启系统"
    echo "  3. 然后启动: cd ~/ros2_docker_ws && ./start_ros2.sh"
    echo
    log_info "🔧 故障排除："
    echo "  • Docker问题: ./fix_docker.sh"
    echo "  • 测试映射: ./test_mine_mapping.sh"
    echo "  • 系统检查: ./check_system.sh"
    echo
    log_info "📖 查看快速启动指南: cat ~/ROS2_Foxy_快速启动.md"
    echo
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
