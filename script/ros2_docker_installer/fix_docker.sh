#!/bin/bash

# Docker问题诊断和修复脚本
# 解决Docker安装后的常见问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 检查Docker安装
check_docker_installation() {
    log_info "检查Docker安装状态..."

    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version 2>/dev/null || echo "无法获取版本")
        log_success "Docker已安装: $docker_version"
        return 0
    else
        log_error "Docker未安装"
        return 1
    fi
}

# 检查Docker服务状态
check_docker_service() {
    log_info "检查Docker服务状态..."

    if systemctl is-active docker &> /dev/null; then
        log_success "Docker服务正在运行"
        return 0
    else
        log_warning "Docker服务未运行"
        return 1
    fi
}

# 启动Docker服务
start_docker_service() {
    log_info "启动Docker服务..."

    if sudo systemctl start docker; then
        log_success "Docker服务启动成功"

        # 启用自动启动
        if sudo systemctl enable docker; then
            log_success "Docker服务已设置为开机自启"
        else
            log_warning "无法设置Docker开机自启"
        fi

        return 0
    else
        log_error "Docker服务启动失败"
        return 1
    fi
}

# 检查用户权限
check_user_permissions() {
    log_info "检查用户权限..."

    if groups $USER | grep -q docker; then
        log_success "用户 $USER 已在docker组中"
        return 0
    else
        log_warning "用户 $USER 不在docker组中"
        return 1
    fi
}

# 添加用户到docker组
add_user_to_docker_group() {
    log_info "将用户添加到docker组..."

    if sudo usermod -aG docker $USER; then
        log_success "用户已添加到docker组"
        log_warning "需要重新登录或重启系统以使权限生效"
        return 0
    else
        log_error "无法添加用户到docker组"
        return 1
    fi
}

# 检查网络连接
check_network_connectivity() {
    log_info "检查网络连接..."

    local network_ok=true

    # 检查基本网络连接（先测试IP，再测试域名）
    if ping -c 2 -W 5 ******* &> /dev/null; then
        log_success "基本网络连接正常"

        # 测试域名解析
        if ping -c 2 -W 5 google.com &> /dev/null; then
            log_success "DNS解析正常"
        else
            log_warning "DNS解析可能有问题"
            network_ok=false
        fi
    else
        log_error "网络连接失败"
        network_ok=false
    fi

    # 检查Docker Hub连接
    if curl -s --connect-timeout 10 --max-time 15 https://hub.docker.com &> /dev/null; then
        log_success "Docker Hub连接正常"
    else
        log_warning "Docker Hub连接可能有问题"
        network_ok=false
    fi

    # 检查Docker Registry连接（使用多种方式）
    local registry_ok=false

    # 方式1: 使用curl测试
    if curl -s --connect-timeout 10 --max-time 15 https://registry-1.docker.io/v2/ &> /dev/null; then
        log_success "Docker Registry连接正常"
        registry_ok=true
    fi

    # 方式2: 如果有nslookup，测试DNS解析
    if command -v nslookup &> /dev/null; then
        if nslookup registry-1.docker.io &> /dev/null; then
            log_success "Docker Registry DNS解析正常"
            registry_ok=true
        fi
    fi

    # 方式3: 使用getent测试（更通用）
    if getent hosts registry-1.docker.io &> /dev/null; then
        log_success "Docker Registry主机解析正常"
        registry_ok=true
    fi

    if [[ "$registry_ok" == "false" ]]; then
        log_warning "Docker Registry连接有问题"
        network_ok=false
    fi

    if [[ "$network_ok" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# 测试Docker功能
test_docker_functionality() {
    log_info "测试Docker功能..."

    # 测试Docker info命令
    if docker info &> /dev/null; then
        log_success "Docker info命令正常"
    else
        log_error "Docker info命令失败"
        log_info "尝试使用sudo运行..."

        if sudo docker info &> /dev/null; then
            log_warning "需要sudo权限运行Docker（权限问题）"
            return 1
        else
            log_error "即使使用sudo也无法运行Docker"
            return 1
        fi
    fi

    # 检查网络连接
    local network_ok=true
    if ! check_network_connectivity; then
        network_ok=false
        log_warning "网络连接有问题，这可能导致镜像拉取失败"
    fi

    # 测试拉取镜像（显示进度）
    log_info "测试拉取hello-world镜像..."
    echo "下载进度："
    if timeout 30 bash -c "docker pull hello-world 2>&1"; then
        echo
        log_success "镜像拉取成功"
    else
        echo
        log_warning "镜像拉取失败，尝试使用sudo..."
        echo "下载进度："
        if timeout 30 bash -c "sudo docker pull hello-world 2>&1"; then
            echo
            log_warning "使用sudo拉取成功（权限问题）"
        else
            echo
            log_error "镜像拉取失败"
            if [[ "$network_ok" == "false" ]]; then
                log_info "这可能是网络问题导致的"
            fi
            return 1
        fi
    fi

    # 测试运行容器
    log_info "测试运行hello-world容器..."
    if docker run --rm hello-world &> /dev/null; then
        log_success "容器运行成功"
        return 0
    else
        log_warning "容器运行失败，尝试使用sudo..."
        if sudo docker run --rm hello-world &> /dev/null; then
            log_warning "使用sudo运行成功（权限问题）"
            return 1
        else
            log_error "容器运行失败"
            return 1
        fi
    fi
}

# 清理测试镜像
cleanup_test_images() {
    log_info "清理测试镜像..."

    # 尝试删除hello-world镜像
    if docker images | grep -q hello-world; then
        if docker rmi hello-world &> /dev/null; then
            log_success "测试镜像已清理"
        else
            sudo docker rmi hello-world &> /dev/null || true
            log_info "使用sudo清理测试镜像"
        fi
    fi
}

# 配置Docker镜像源
configure_docker_mirrors() {
    log_info "配置Docker镜像源..."

    local daemon_json="/etc/docker/daemon.json"
    local backup_file="/etc/docker/daemon.json.backup"

    # 备份原配置
    if [[ -f "$daemon_json" ]]; then
        sudo cp "$daemon_json" "$backup_file"
        log_info "已备份原配置到 $backup_file"
    fi

    # 完全停止Docker服务
    log_info "完全停止Docker服务..."
    sudo systemctl stop docker
    sudo pkill -f docker 2>/dev/null || true
    sleep 3

    # 清理可能的配置冲突
    sudo rm -f /etc/docker/daemon.json.backup* 2>/dev/null || true

    # 创建简单有效的配置（先用单一镜像源测试）
    log_info "写入镜像源配置..."
    sudo tee "$daemon_json" > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://dockerproxy.com"
  ],
  "dns": ["*******", "8.8.4.4"]
}
EOF

    # 验证配置文件语法
    if ! python3 -m json.tool "$daemon_json" > /dev/null 2>&1; then
        log_error "配置文件JSON格式错误"
        return 1
    fi

    log_info "配置文件内容："
    cat "$daemon_json"

    log_success "Docker镜像源配置完成"

    # 重新加载配置并启动Docker
    log_info "重新加载Docker配置..."
    sudo systemctl daemon-reload

    log_info "启动Docker服务..."
    if sudo systemctl start docker; then
        log_success "Docker服务启动成功"

        # 等待Docker完全启动
        log_info "等待Docker完全启动..."
        local retry=0
        while ! docker info &> /dev/null && [[ $retry -lt 10 ]]; do
            sleep 2
            ((retry++))
            echo -n "."
        done
        echo

        if docker info &> /dev/null; then
            log_success "Docker服务就绪"

            # 验证配置
            log_info "验证镜像源配置..."
            if docker info | grep -q "Registry Mirrors"; then
                log_success "✅ 镜像源配置已生效"
                echo "配置的镜像源："
                docker info | grep -A 10 "Registry Mirrors" | head -5
            else
                log_error "❌ 镜像源配置未生效！"
                log_info "当前Docker配置："
                docker info | grep -A 5 "Server Version" || true
                return 1
            fi
        else
            log_error "Docker服务启动失败"
            return 1
        fi

        return 0
    else
        log_error "Docker服务启动失败"
        return 1
    fi
}

# 显示修复建议
show_fix_suggestions() {
    log_header "=== 修复建议 ==="
    echo

    echo "Docker镜像拉取失败的可能原因和解决方案："
    echo
    echo "1. 网络连接问题："
    echo "   - 检查网络连接: ping google.com"
    echo "   - 检查防火墙设置"
    echo "   - 尝试使用VPN或代理"
    echo
    echo "2. Docker Hub访问问题："
    echo "   - 配置国内镜像源（推荐）"
    echo "   - 运行: ./fix_docker.sh --configure-mirrors"
    echo
    echo "3. DNS解析问题："
    echo "   - 更改DNS服务器: sudo systemctl restart systemd-resolved"
    echo "   - 或手动设置DNS: echo 'nameserver *******' | sudo tee /etc/resolv.conf"
    echo
    echo "4. Docker服务问题："
    echo "   - 重启Docker: sudo systemctl restart docker"
    echo "   - 检查日志: sudo journalctl -u docker.service"
    echo
    echo "5. 磁盘空间问题："
    echo "   - 检查空间: df -h"
    echo "   - 清理Docker: docker system prune -a"
    echo
    echo "6. 如果仍有问题，尝试重新安装Docker："
    echo "   sudo apt remove docker docker-engine docker.io containerd runc"
    echo "   # 然后重新运行安装脚本"
}

# 主函数
main() {
    # 检查命令行参数
    if [[ "$1" == "--configure-mirrors" ]]; then
        log_header "配置Docker镜像源"
        echo
        configure_docker_mirrors
        echo
        log_info "镜像源配置完成，现在测试Docker功能..."
        echo
        if test_docker_functionality; then
            log_success "Docker功能测试通过！"
        else
            log_error "Docker功能仍有问题"
            show_fix_suggestions
        fi
        return
    fi

    log_header "Docker问题诊断和修复工具"
    echo

    local needs_relogin=false
    local docker_works=false

    # 检查Docker安装
    if ! check_docker_installation; then
        log_error "Docker未安装，请先运行安装脚本"
        exit 1
    fi

    echo

    # 检查Docker服务
    if ! check_docker_service; then
        if start_docker_service; then
            log_success "Docker服务已启动"
        else
            log_error "无法启动Docker服务"
            show_fix_suggestions
            exit 1
        fi
    fi

    echo

    # 检查用户权限
    if ! check_user_permissions; then
        if add_user_to_docker_group; then
            needs_relogin=true
        else
            log_error "无法修复用户权限问题"
            exit 1
        fi
    fi

    echo

    # 测试Docker功能
    if test_docker_functionality; then
        docker_works=true
        log_success "Docker功能测试通过！"
    else
        log_warning "Docker功能测试失败"
        if [[ "$needs_relogin" == "true" ]]; then
            log_info "这可能是因为需要重新登录以使权限生效"
        fi
    fi

    echo

    # 清理测试镜像
    cleanup_test_images

    echo

    # 总结
    log_header "=== 诊断结果 ==="

    if [[ "$docker_works" == "true" ]]; then
        log_success "Docker已正常工作，可以继续ROS2安装"
        echo
        echo "下一步："
        echo "  cd ~/ros2_docker_ws"
        echo "  ./start_ros2.sh"
    elif [[ "$needs_relogin" == "true" ]]; then
        log_warning "需要重新登录以使Docker权限生效"
        echo
        echo "请执行以下步骤："
        echo "1. 重新登录系统或重启"
        echo "2. 再次运行此脚本验证"
        echo "3. 如果正常，继续ROS2安装"
    else
        log_error "Docker仍有问题，需要手动修复"
        echo
        echo "推荐解决方案："
        echo "1. 配置国内镜像源: ./fix_docker.sh --configure-mirrors"
        echo "2. 检查网络连接"
        echo "3. 查看详细修复建议"
        echo
        show_fix_suggestions
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
