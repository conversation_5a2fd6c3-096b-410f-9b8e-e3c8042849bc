#!/bin/bash

# Arch Linux / Manjaro 支持测试脚本
# 测试ROS2 Docker安装脚本在Arch系统上的兼容性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 检测系统类型
detect_system() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        DISTRO_ID="$ID"
        DISTRO_NAME="$NAME"
        DISTRO_VERSION="$VERSION"
    else
        log_error "无法检测系统版本"
        return 1
    fi
    
    case "$DISTRO_ID" in
        arch|manjaro)
            log_success "检测到支持的系统: $DISTRO_NAME"
            return 0
            ;;
        *)
            log_error "此脚本仅用于测试Arch Linux和Manjaro系统"
            log_info "当前系统: $DISTRO_NAME"
            return 1
            ;;
    esac
}

# 检查包管理器
check_package_manager() {
    log_info "检查包管理器..."
    
    if command -v pacman &> /dev/null; then
        log_success "pacman 可用"
        PACMAN_VERSION=$(pacman --version | head -1)
        log_info "版本: $PACMAN_VERSION"
    else
        log_error "pacman 不可用"
        return 1
    fi
    
    if command -v yay &> /dev/null; then
        log_success "yay (AUR helper) 可用"
        YAY_VERSION=$(yay --version | head -1)
        log_info "版本: $YAY_VERSION"
    else
        log_warning "yay 不可用，某些AUR包可能无法安装"
    fi
}

# 检查系统更新状态
check_system_updates() {
    log_info "检查系统更新状态..."
    
    # 检查是否有可用更新
    if pacman -Qu &> /dev/null; then
        local update_count=$(pacman -Qu | wc -l)
        log_warning "有 $update_count 个包可以更新"
        log_info "建议运行: sudo pacman -Syu"
    else
        log_success "系统已是最新状态"
    fi
}

# 检查必要的依赖包
check_dependencies() {
    log_info "检查必要的依赖包..."
    
    local deps=("curl" "wget" "git" "base-devel")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if pacman -Qi "$dep" &> /dev/null; then
            log_success "$dep 已安装"
        else
            log_warning "$dep 未安装"
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_info "缺少的依赖包: ${missing_deps[*]}"
        log_info "可以运行: sudo pacman -S ${missing_deps[*]}"
    else
        log_success "所有必要依赖都已安装"
    fi
}

# 检查Docker状态
check_docker() {
    log_info "检查Docker状态..."
    
    if pacman -Qi docker &> /dev/null; then
        log_success "Docker 已安装"
        DOCKER_VERSION=$(docker --version 2>/dev/null || echo "Docker未运行")
        log_info "版本: $DOCKER_VERSION"
        
        # 检查Docker服务状态
        if systemctl is-active docker &> /dev/null; then
            log_success "Docker 服务正在运行"
        else
            log_warning "Docker 服务未运行"
            log_info "可以运行: sudo systemctl start docker"
        fi
        
        # 检查Docker服务是否启用
        if systemctl is-enabled docker &> /dev/null; then
            log_success "Docker 服务已启用"
        else
            log_warning "Docker 服务未启用"
            log_info "可以运行: sudo systemctl enable docker"
        fi
    else
        log_warning "Docker 未安装"
        log_info "可以运行: sudo pacman -S docker"
    fi
    
    # 检查docker-compose
    if pacman -Qi docker-compose &> /dev/null; then
        log_success "docker-compose 已安装"
        COMPOSE_VERSION=$(docker-compose --version 2>/dev/null || echo "未知版本")
        log_info "版本: $COMPOSE_VERSION"
    else
        log_warning "docker-compose 未安装"
        log_info "可以运行: sudo pacman -S docker-compose"
    fi
}

# 检查用户权限
check_user_permissions() {
    log_info "检查用户权限..."
    
    if groups $USER | grep -q docker; then
        log_success "用户 $USER 在 docker 组中"
    else
        log_warning "用户 $USER 不在 docker 组中"
        log_info "可以运行: sudo usermod -aG docker $USER"
        log_info "然后重新登录"
    fi
    
    if [[ $EUID -eq 0 ]]; then
        log_warning "当前以root用户运行"
        log_info "建议使用普通用户运行安装脚本"
    else
        log_success "当前使用普通用户: $USER"
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    if ping -c 1 google.com &> /dev/null; then
        log_success "网络连接正常"
    else
        log_error "网络连接失败"
        return 1
    fi
    
    # 检查Docker Hub连接
    if curl -s --connect-timeout 5 https://hub.docker.com &> /dev/null; then
        log_success "Docker Hub 连接正常"
    else
        log_warning "Docker Hub 连接可能有问题"
    fi
}

# 测试Docker功能
test_docker_functionality() {
    log_info "测试Docker功能..."
    
    if ! command -v docker &> /dev/null; then
        log_warning "Docker未安装，跳过功能测试"
        return 0
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        log_warning "Docker服务未运行，跳过功能测试"
        return 0
    fi
    
    # 测试Docker命令
    if docker info &> /dev/null; then
        log_success "Docker 命令可用"
    else
        log_error "Docker 命令不可用（可能是权限问题）"
        return 1
    fi
    
    # 测试拉取镜像
    log_info "测试拉取小镜像..."
    if docker pull hello-world &> /dev/null; then
        log_success "镜像拉取成功"
        
        # 测试运行容器
        if docker run --rm hello-world &> /dev/null; then
            log_success "容器运行成功"
        else
            log_error "容器运行失败"
            return 1
        fi
    else
        log_error "镜像拉取失败"
        return 1
    fi
}

# 生成系统报告
generate_report() {
    log_header "=== 系统兼容性报告 ==="
    echo
    
    echo "系统信息:"
    echo "  发行版: $DISTRO_NAME"
    echo "  ID: $DISTRO_ID"
    echo "  版本: $DISTRO_VERSION"
    echo "  内核: $(uname -r)"
    echo "  架构: $(uname -m)"
    echo
    
    echo "包管理器:"
    if command -v pacman &> /dev/null; then
        echo "  pacman: ✓"
    else
        echo "  pacman: ✗"
    fi
    
    if command -v yay &> /dev/null; then
        echo "  yay: ✓"
    else
        echo "  yay: ✗"
    fi
    echo
    
    echo "Docker状态:"
    if pacman -Qi docker &> /dev/null; then
        echo "  Docker安装: ✓"
    else
        echo "  Docker安装: ✗"
    fi
    
    if systemctl is-active docker &> /dev/null; then
        echo "  Docker服务: ✓"
    else
        echo "  Docker服务: ✗"
    fi
    
    if groups $USER | grep -q docker; then
        echo "  用户权限: ✓"
    else
        echo "  用户权限: ✗"
    fi
    echo
    
    echo "网络连接:"
    if ping -c 1 google.com &> /dev/null; then
        echo "  互联网: ✓"
    else
        echo "  互联网: ✗"
    fi
    
    if curl -s --connect-timeout 5 https://hub.docker.com &> /dev/null; then
        echo "  Docker Hub: ✓"
    else
        echo "  Docker Hub: ✗"
    fi
}

# 主函数
main() {
    log_header "Arch Linux / Manjaro ROS2 Docker 兼容性测试"
    echo
    
    # 系统检测
    if ! detect_system; then
        exit 1
    fi
    
    echo
    
    # 各项检查
    check_package_manager
    echo
    
    check_system_updates
    echo
    
    check_dependencies
    echo
    
    check_docker
    echo
    
    check_user_permissions
    echo
    
    check_network
    echo
    
    # Docker功能测试
    if [[ "${1:-}" == "--test-docker" ]]; then
        test_docker_functionality
        echo
    fi
    
    # 生成报告
    generate_report
    
    echo
    log_success "兼容性测试完成！"
    
    if [[ "${1:-}" != "--test-docker" ]]; then
        echo
        log_info "运行 '$0 --test-docker' 进行完整的Docker功能测试"
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
