#!/bin/bash

# ROS2 Foxy Docker 离线安装脚本
# 跳过镜像下载，先创建环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# 创建ROS2工作空间
create_ros2_workspace() {
    log_info "创建ROS2工作空间..."
    
    mkdir -p ~/ros2_docker_ws/{src,launch,config,logs}
    
    log_success "ROS2工作空间创建完成: ~/ros2_docker_ws"
}

# 创建Docker Compose配置
create_docker_compose() {
    log_info "创建Docker Compose配置..."
    
    cat > ~/ros2_docker_ws/docker-compose.yml << 'EOF'
version: '3.8'

services:
  ros2-foxy:
    image: osrf/ros:foxy-desktop-full
    container_name: ros2-foxy-container
    stdin_open: true
    tty: true
    network_mode: host
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
      - ROS_DOMAIN_ID=0
      - ROS_DISTRO=foxy
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ./src:/ros2_ws/src:rw
      - ./launch:/ros2_ws/launch:rw
      - ./config:/ros2_ws/config:rw
      - ./logs:/ros2_ws/log:rw
      - /mine:/mine:rw
    working_dir: /ros2_ws
    command: bash -c "source /opt/ros/foxy/setup.bash && echo 'ROS2 Foxy环境已准备就绪!' && echo 'ROS_DISTRO: $ROS_DISTRO' && bash"
    restart: unless-stopped
EOF
    
    log_success "Docker Compose配置文件创建完成"
}

# 创建便捷脚本
create_convenience_scripts() {
    log_info "创建便捷使用脚本..."
    
    # 创建启动脚本
    cat > ~/ros2_docker_ws/start_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Foxy Docker 启动脚本

ROS_DISTRO=${1:-foxy}

echo "启动ROS2 $ROS_DISTRO 容器..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker服务"
    exit 1
fi

# 检查镜像是否存在
if ! docker images | grep -q "osrf/ros.*foxy"; then
    echo "警告: ROS2 Foxy镜像不存在"
    echo "请先下载镜像: docker pull osrf/ros:foxy-desktop-full"
    echo "或者等网络问题解决后再下载"
    read -p "是否继续启动容器? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 允许X11转发
xhost +local:docker

# 启动对应的ROS2容器
docker-compose up -d ros2-$ROS_DISTRO

# 等待容器启动
sleep 2

# 进入容器
echo "进入ROS2 $ROS_DISTRO 容器..."
docker exec -it ros2-$ROS_DISTRO-container bash

EOF

    # 创建停止脚本
    cat > ~/ros2_docker_ws/stop_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Docker 停止脚本

echo "停止所有ROS2容器..."
docker-compose down

# 禁用X11转发
xhost -local:docker

echo "ROS2容器已停止"
EOF

    # 创建镜像下载脚本
    cat > ~/ros2_docker_ws/download_images.sh << 'EOF'
#!/bin/bash

# ROS2镜像下载脚本

echo "开始下载ROS2 Foxy镜像..."
echo "这可能需要几分钟时间，请耐心等待..."

# 尝试下载镜像
if docker pull osrf/ros:foxy-desktop-full; then
    echo "✅ ROS2 Foxy镜像下载成功！"
    echo "现在可以运行: ./start_ros2.sh"
else
    echo "❌ 镜像下载失败"
    echo "请检查网络连接或稍后重试"
    echo ""
    echo "可以尝试的解决方案："
    echo "1. 检查网络连接"
    echo "2. 配置Docker镜像源"
    echo "3. 使用VPN或代理"
fi
EOF

    # 创建清理脚本
    cat > ~/ros2_docker_ws/clean_ros2.sh << 'EOF'
#!/bin/bash

# ROS2 Docker 清理脚本

echo "清理ROS2 Docker环境..."

# 停止并删除容器
docker-compose down --rmi all --volumes --remove-orphans

# 清理未使用的Docker资源
docker system prune -f

echo "ROS2 Docker环境清理完成"
EOF

    # 设置脚本执行权限
    chmod +x ~/ros2_docker_ws/*.sh
    
    log_success "便捷脚本创建完成"
}

# 创建示例包
create_sample_package() {
    log_info "创建示例ROS2包..."
    
    # 创建示例Python包
    mkdir -p ~/ros2_docker_ws/src/hello_ros2/hello_ros2
    
    cat > ~/ros2_docker_ws/src/hello_ros2/package.xml << 'EOF'
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>hello_ros2</name>
  <version>0.0.0</version>
  <description>Hello ROS2 example package</description>
  <maintainer email="<EMAIL>">user</maintainer>
  <license>Apache-2.0</license>

  <depend>rclpy</depend>
  <depend>std_msgs</depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/setup.py << 'EOF'
from setuptools import setup

package_name = 'hello_ros2'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='user',
    maintainer_email='<EMAIL>',
    description='Hello ROS2 example package',
    license='Apache-2.0',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'talker = hello_ros2.publisher:main',
            'listener = hello_ros2.subscriber:main',
        ],
    },
)
EOF

    mkdir -p ~/ros2_docker_ws/src/hello_ros2/resource
    touch ~/ros2_docker_ws/src/hello_ros2/resource/hello_ros2

    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/__init__.py << 'EOF'
# Hello ROS2 package
EOF

    # 创建简单的发布者和订阅者
    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/publisher.py << 'EOF'
import rclpy
from rclpy.node import Node
from std_msgs.msg import String

class MinimalPublisher(Node):
    def __init__(self):
        super().__init__('minimal_publisher')
        self.publisher_ = self.create_publisher(String, 'topic', 10)
        timer_period = 0.5
        self.timer = self.create_timer(timer_period, self.timer_callback)
        self.i = 0

    def timer_callback(self):
        msg = String()
        msg.data = 'Hello World: %d' % self.i
        self.publisher_.publish(msg)
        self.get_logger().info('Publishing: "%s"' % msg.data)
        self.i += 1

def main(args=None):
    rclpy.init(args=args)
    minimal_publisher = MinimalPublisher()
    rclpy.spin(minimal_publisher)
    minimal_publisher.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

    cat > ~/ros2_docker_ws/src/hello_ros2/hello_ros2/subscriber.py << 'EOF'
import rclpy
from rclpy.node import Node
from std_msgs.msg import String

class MinimalSubscriber(Node):
    def __init__(self):
        super().__init__('minimal_subscriber')
        self.subscription = self.create_subscription(
            String,
            'topic',
            self.listener_callback,
            10)

    def listener_callback(self, msg):
        self.get_logger().info('I heard: "%s"' % msg.data)

def main(args=None):
    rclpy.init(args=args)
    minimal_subscriber = MinimalSubscriber()
    rclpy.spin(minimal_subscriber)
    minimal_subscriber.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

    log_success "示例ROS2包创建完成"
}

# 主函数
main() {
    log_header "ROS2 Foxy Docker 离线安装"
    echo
    
    log_info "此脚本将创建ROS2环境，但跳过镜像下载"
    log_info "稍后网络问题解决后，可以手动下载镜像"
    echo
    
    # 创建环境
    create_ros2_workspace
    create_docker_compose
    create_convenience_scripts
    create_sample_package
    
    echo
    log_success "🎉 ROS2 Foxy环境创建完成！"
    echo
    log_info "📋 下一步操作："
    echo "  1. 等网络问题解决后，运行: cd ~/ros2_docker_ws && ./download_images.sh"
    echo "  2. 镜像下载完成后，运行: ./start_ros2.sh"
    echo "  3. 或者现在就尝试启动（如果镜像已存在）"
    echo
    log_info "📁 工作空间位置: ~/ros2_docker_ws"
    log_info "🔧 管理脚本已创建，可直接使用"
    echo
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
