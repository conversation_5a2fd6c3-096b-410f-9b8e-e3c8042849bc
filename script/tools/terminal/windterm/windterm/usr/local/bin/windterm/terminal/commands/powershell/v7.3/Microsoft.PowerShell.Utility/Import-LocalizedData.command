description: Imports language-specific data into scripts and functions based on the
  UI culture that is selected for the operating system
synopses:
- Import-LocalizedData [[-BindingVariable] <String>] [[-UICulture] <String>] [-BaseDirectory
  <String>] [-FileName <String>] [-SupportedCommand <String[]>] [<CommonParameters>]
options:
  -BaseDirectory System.String: ~
  -BindingVariable,-Variable System.String: ~
  -FileName System.String: ~
  -SupportedCommand System.String[]: ~
  -UICulture System.String: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
