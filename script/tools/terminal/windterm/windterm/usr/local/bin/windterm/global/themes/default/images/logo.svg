<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="1411px" height="191px" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
  viewBox="0 0 2616 354"
  xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="label" class="punct">
    <circle fill="cornflowerblue" cx="177" cy="177" r="67"/>
    <rect fill="none" width="354" height="354"/>
  </g>
  <g id="bing">
    <path fill="#0D8484" d="M121 249c35,-19 70,-37 106,-56 -10,-4 -18,-8 -27,-12 -7,-3 -14,-7 -21,-10 -2,0 -3,-1 -3,-3 -11,-23 -22,-46 -32,-69 -1,-1 -1,-1 -1,-3 8,3 15,5 22,7 30,10 61,19 91,28 17,5 35,11 52,16 3,1 3,2 3,4 0,26 0,51 0,76 0,2 0,4 -2,5 -60,35 -120,71 -180,107 -2,1 -5,3 -8,4 -1,1 -2,1 -3,0 -25,-17 -49,-34 -73,-51 -1,-1 -2,-2 -2,-3 0,-5 0,-9 0,-14 0,-68 0,-136 0,-205 0,-19 0,-38 0,-57 0,-3 1,-3 3,-2 24,8 48,16 72,24 2,1 2,2 2,4 0,13 0,27 0,41 0,55 1,110 1,166 0,1 0,2 0,3z"/>
    <rect fill="none" width="354" height="354"/>
  </g>
  <g id="duckduckgo">
    <path fill="#DE5833" d="M629 35c79,0 143,64 143,142 0,79 -64,142 -143,142 -78,0 -142,-63 -142,-142 0,-78 64,-142 142,-142z"/>
    <path fill="#DE5833" d="M629 344c93,0 167,-75 167,-167 0,-92 -74,-167 -167,-167 -92,0 -166,75 -166,167 0,92 74,167 166,167l0 0zm0 -14c-84,0 -153,-68 -153,-153 0,-84 69,-153 153,-153 85,0 153,69 153,153 0,85 -68,153 -153,153z"/>
    <path fill="#D5D7D8" d="M560 81c0,-2 2,-4 4,-5 -1,1 -2,1 -3,1 -2,1 -4,5 -4,7 12,-1 29,0 42,4 0,0 1,0 2,-1 -12,-5 -27,-7 -41,-6l0 0zm2 -14c1,0 1,0 1,0 -2,0 -4,1 -5,1 2,0 9,4 14,6 1,0 2,-1 2,-1 -2,0 -9,-6 -12,-6l0 0zm13 28c-1,0 -2,1 -2,1 -19,10 -28,33 -23,61 5,26 24,113 32,154 3,1 5,2 8,3 -8,-37 -29,-134 -34,-161 -5,-28 0,-48 19,-58z"/>
    <path fill="white" d="M652 296c-1,1 -1,1 -1,1 -1,1 -1,2 -2,3 -4,2 -15,3 -21,2 -1,0 -1,0 -2,-1 -10,6 -24,13 -27,11 -5,-2 -6,-37 -5,-45 1,-7 23,4 34,9 2,-2 8,-4 14,-4 -8,-20 -14,-42 -11,-57 -5,-4 -11,-12 -10,-20 1,-7 18,-19 29,-19 12,-1 16,-1 25,-3l2 -1c6,-21 -8,-57 -24,-73 -6,-6 -14,-9 -23,-11 -3,-5 -9,-9 -17,-13 -14,-8 -33,-11 -50,-8 0,0 0,0 -1,0 3,0 10,6 12,6 0,0 -1,1 -2,1 -2,1 -5,1 -8,2 -2,1 -4,3 -4,5 14,-1 29,1 41,6 -1,1 -2,1 -2,1 -9,1 -18,3 -24,7 0,0 0,0 0,0 -19,10 -24,30 -19,58 5,27 26,125 34,162 13,4 24,7 38,7 12,0 26,-3 37,-5 -4,-8 -9,-16 -12,-22 -1,1 -1,1 -1,1l0 0zm7 -139c-5,0 -9,-4 -9,-9 0,-5 4,-9 9,-9 5,0 9,4 9,9 0,0 0,0 0,0 0,5 -4,9 -9,9 0,0 0,0 0,0l0 0zm15 -43c0,0 -6,-3 -10,-3 -9,0 -12,4 -12,4 0,0 2,-10 13,-8 7,1 9,7 9,7l0 0zm-96 12c0,0 -4,-10 7,-14 11,-5 16,2 16,2 0,0 -8,-3 -16,2 -7,5 -7,10 -7,10zm10 19c0,0 0,0 0,0 5,0 10,5 10,11 0,0 0,0 0,0 0,5 -5,10 -10,10 0,0 0,0 0,0 -6,0 -11,-5 -11,-10 0,0 0,0 0,0l11 -11z"/>
    <path fill="#2D4F8E" d="M600 154c0,0 0,0 0,0 0,6 4,11 10,11 0,0 0,0 0,0 6,0 11,-5 11,-11l-21 0zm-6 -1c0,2 -2,3 -3,3l3 -3z"/>
    <path fill="white" d="M594 148c-2,0 -3,1 -3,3 0,0 0,0 0,0 0,1 1,2 3,2 0,0 0,0 0,0 0,-1 -2,-2 -3,-2 0,0 0,0 0,0l3 -3zm0 0l0 0 0 0z"/>
    <path fill="#2D4F8E" d="M659 139c-5,0 -9,4 -9,9 0,5 4,9 9,9 0,0 0,0 0,0 5,0 9,-4 9,-9 0,0 0,0 0,0 0,-5 -4,-9 -9,-9l0 0zm4 26c-1,0 -2,-1 -2,-2 0,-1 1,-2 2,-2 2,0 3,1 3,2 0,1 -1,2 -3,2z"/>
    <path fill="white" d="M663 142c-1,0 -2,2 -2,3 0,1 1,2 2,2 0,0 0,0 0,0 2,0 3,-1 3,-2 0,0 0,0 0,0 0,-1 -1,-3 -3,-3l0 0zm0 0l0 0 0 0z"/>
    <path fill="none" d="M592 124c0,0 -5,-7 -16,-3 -11,4 -7,14 -7,14 0,0 0,-5 7,-10 8,-5 16,-1 16,-1m64 -8c-11,-2 -13,8 -13,8 0,0 3,-4 12,-4 4,0 10,3 10,3 0,0 -2,-6 -9,-7"/>
    <path fill="#FDD209" d="M650 176c-11,0 -28,12 -29,19 -1,8 5,16 10,20l0 0c5,3 38,14 55,14 16,0 43,-10 40,-19 -3,-8 -29,8 -57,5 -21,-2 -25,-11 -20,-18 6,-8 16,2 33,-4 18,-5 42,-14 51,-19 21,-12 -9,-16 -16,-13 -6,3 -29,9 -40,11l-1 1c-10,2 -14,2 -26,3l0 0zm0 0l0 0 0 0z"/>
    <path fill="#65BC46" d="M624 281c0,-1 1,-2 3,-3 0,-1 0,-1 1,-2 -11,-5 -33,-16 -34,-9 -1,8 0,43 5,45 3,2 17,-5 27,-11 -3,-2 -2,-8 -2,-20l0 0zm29 14l0 0c9,4 27,10 31,9 5,-2 3,-44 -2,-45 -5,-1 -23,11 -30,17 1,5 3,15 1,19l0 0zm0 0l0 0 0 0z"/>
    <path fill="#43A244" d="M631 299c-6,-1 -4,-7 -4,-21 0,0 0,0 0,0 -2,1 -3,2 -3,3 0,12 -1,18 2,20 1,1 1,1 2,1 6,1 17,0 21,-2 1,-1 1,-2 2,-3 -5,2 -15,3 -20,2l0 0zm0 0l0 0 0 0z"/>
    <path fill="#65BC46" d="M628 276c-1,1 -1,1 -1,2 0,0 0,0 0,0 0,14 -2,20 4,21 5,1 15,0 20,-2 0,0 0,0 1,-1 0,0 0,0 1,-1 2,-4 0,-14 -1,-19 0,-2 -1,-3 -1,-3 0,-1 -5,-2 -9,-1 -6,0 -12,2 -14,4l0 0zm0 0l0 0 0 0z"/>
    <rect fill="none" x="452" width="354" height="354"/>
  </g>
  <g id="github">
    <path fill="steelblue" d="M1534 10c-94,0 -170,77 -170,171 0,76 49,140 116,163 9,1 12,-4 12,-9 0,-4 0,-14 0,-29 -48,11 -58,-23 -58,-23 -7,-19 -18,-25 -18,-25 -16,-10 1,-10 1,-10 17,1 26,18 26,18 15,26 40,18 49,14 2,-11 6,-19 11,-23 -38,-4 -77,-19 -77,-85 0,-18 6,-34 17,-46 -2,-4 -7,-21 2,-45 0,0 14,-4 46,18 14,-4 29,-6 43,-6 14,0 29,2 43,6 32,-22 46,-18 46,-18 10,24 4,41 2,45 11,12 17,28 17,46 0,66 -39,81 -77,85 6,5 11,16 11,32 0,22 0,41 0,46 0,5 3,10 12,9 67,-23 116,-87 116,-163 0,-94 -76,-171 -170,-171l0 0zm0 0z"/>
    <rect fill="none" x="1357" width="354" height="354"/>
  </g>
  <g id="google">
    <path fill="#4285F4" d="M1195 304c40,-37 57,-99 47,-157l-157 0 0 65 89 0c-3,21 -15,38 -33,50l54 42z"/>
    <path fill="#34A853" d="M936 252c29,56 87,92 150,92 40,0 79,-14 109,-40l-54 -42c-46,30 -123,19 -150,-53l-55 43z"/>
    <path fill="#FBBC02" d="M991 209c-7,-22 -7,-42 0,-64l-55 -43c-20,40 -26,97 0,150l55 -43z"/>
    <path fill="#EA4335" d="M991 145c20,-60 102,-96 157,-44l48 -47c-68,-66 -201,-63 -260,48l55 43z"/>
    <rect fill="none" x="905" width="354" height="354"/>
  </g>
  <g id="stackoverflow">
    <path fill="#BCBBBB" d="M1965 344c-35,0 -70,0 -105,0 -5,0 -5,0 -5,-5 0,-43 0,-85 0,-128 0,-5 0,-5 5,-5 5,0 10,1 15,0 2,0 3,1 3,4 0,31 0,62 0,93 0,5 0,10 0,15 0,3 1,3 4,3 37,0 75,0 112,0 18,0 35,0 53,0 3,0 4,0 4,-4 0,-36 0,-71 0,-107 0,-3 1,-4 4,-4 5,0 10,0 15,0 3,0 4,1 4,4 0,33 0,67 0,100 0,10 0,20 0,29 0,5 0,5 -5,5 -35,0 -70,0 -104,0z"/>
    <path fill="#F38126" d="M2093 10c2,0 2,1 3,3 2,12 4,24 6,36 5,29 10,58 15,87 1,3 0,4 -3,4 -7,1 -14,2 -21,4 -3,1 -3,-1 -4,-3 -2,-15 -5,-31 -8,-46 -4,-25 -8,-50 -13,-75 -1,-5 -1,-5 5,-6 6,-1 13,-2 20,-4z"/>
    <path fill="#F38126" d="M2084 146c0,1 -1,1 -1,2 -7,4 -13,8 -20,13 -2,2 -2,0 -3,-2 -18,-25 -35,-51 -53,-76 -6,-9 -12,-18 -18,-27 -1,-2 -2,-3 1,-4 6,-4 12,-9 18,-13 2,-1 3,-1 4,1 24,34 48,69 71,104 1,1 1,1 1,2z"/>
    <path fill="#F38126" d="M2031 240c0,6 -1,13 -2,20 0,2 0,3 -3,3 -27,-3 -53,-5 -80,-8 -15,-1 -30,-3 -44,-4 -3,0 -4,-1 -4,-4 1,-7 2,-15 2,-22 1,-2 1,-2 3,-2 31,3 61,6 92,8 11,1 21,2 32,3 5,1 5,1 4,6z"/>
    <path fill="#F38126" d="M2057 166c-1,1 -1,2 -1,2 -4,7 -8,13 -12,20 -1,2 -2,2 -4,1 -36,-22 -72,-43 -108,-64 -2,-2 -2,-2 -1,-4 4,-7 7,-13 11,-20 1,-1 2,-2 4,-1 36,22 73,43 109,65 1,0 2,0 2,1z"/>
    <path fill="#F38126" d="M1915 164c9,3 19,5 29,8 31,8 62,17 93,25 3,1 3,2 3,4 -3,7 -4,14 -6,21 -1,2 -1,3 -3,3 -41,-11 -82,-22 -122,-33 -2,-1 -3,-2 -2,-4 2,-7 4,-14 6,-22 0,-1 0,-2 2,-2z"/>
    <path fill="#F38126" d="M1962 299c-20,0 -41,0 -62,0 -2,0 -3,-1 -3,-3 0,-8 0,-15 0,-22 0,-3 1,-3 3,-3 42,0 84,0 126,0 2,0 3,1 3,3 -1,7 -1,14 0,22 0,2 -1,3 -4,3 -21,0 -42,0 -63,0 0,0 0,0 0,0z"/>
    <rect fill="none" x="1809" width="354" height="354"/>
  </g>
  <g id="wikipedia">
     <path fill="#D3D3D3" d="M2439 10l0 0c92,0 167,75 167,167l0 0c0,92 -75,167 -167,167l0 0c-92,0 -167,-75 -167,-167l0 0c0,-92 75,-167 167,-167z"/>
     <path fill="black" d="M2314 98l0 6c0,1 1,2 3,2 13,0 13,3 18,15l59 127c4,10 10,11 16,0l29 -58 28 58c5,10 10,10 15,0l56 -127c6,-11 13,-15 24,-15 1,0 1,-1 1,-2l0 -6 -52 0 -1 1 0 5c0,1 2,2 3,2 7,0 19,1 14,12l-46 108 -2 0 -28 -58 24 -47c5,-9 7,-15 16,-15 2,0 3,-1 3,-2l0 -5 -1 -1 -41 0 -1 1 0 5c0,2 3,2 5,2 11,0 10,5 6,12l-17 37 -17 -34c-6,-11 -7,-14 2,-15 1,0 5,0 5,-2l0 -5 -1 -1 -45 0 -1 1 0 5c0,2 2,2 4,2 8,1 8,2 15,17l26 54 -24 49 -2 0 -50 -109c-5,-11 1,-11 12,-11 2,0 2,-1 2,-2l0 -5 -1 -1 -56 0z"/>
     <rect fill="none" x="2262" width="354" height="354"/>
  </g>
</svg>
