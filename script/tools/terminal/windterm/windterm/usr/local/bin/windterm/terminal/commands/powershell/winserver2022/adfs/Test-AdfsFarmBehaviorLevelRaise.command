description: Tests whether you can raise the behavior level of a farm
synopses:
- Test-AdfsFarmBehaviorLevelRaise [-Member <String[]>] [-Credential <PSCredential>]
  [-ServiceAccountCredential <PSCredential>] [-Force] [<CommonParameters>]
- Test-AdfsFarmBehaviorLevelRaise [-Member <String[]>] [-Credential <PSCredential>]
  [-GroupServiceAccountIdentifier <String>] [-Force] [<CommonParameters>]
options:
  -Credential PSCredential: ~
  -Force Switch: ~
  -GroupServiceAccountIdentifier String: ~
  -Member String[]: ~
  -ServiceAccountCredential PSCredential: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
