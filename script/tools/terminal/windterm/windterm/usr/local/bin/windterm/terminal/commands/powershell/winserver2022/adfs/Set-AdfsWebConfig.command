description: Modifies web customization configuration settings
synopses:
- Set-AdfsWebConfig [-ActiveThemeName <String>] [-CDCCookieReader <Uri>] [-CDCCookieWriter
  <Uri>] [-HRDCookieLifetime <Int32>] [-HRDCookieEnabled <Boolean>] [-ContextCookieEnabled
  <Boolean>] [-PassThru] [-WhatIf] [-Confirm] [<CommonParameters>]
options:
  -ActiveThemeName String: ~
  -CDCCookieReader Uri: ~
  -CDCCookieWriter Uri: ~
  -ContextCookieEnabled Boolean: ~
  -HRDCookieEnabled Boolean: ~
  -HRDCookieLifetime Int32: ~
  -PassThru Switch: ~
  -Confirm,-cf Switch: ~
  -WhatIf,-wi Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
