description: Uses a customized view to format the output
synopses:
- Format-Custom [[-Property] <Object[]>] [-Depth <Int32>] [-GroupBy <Object>] [-View
  <String>] [-ShowError] [-DisplayError] [-Force] [-Expand <String>] [-InputObject
  <PSObject>] [<CommonParameters>]
options:
  -Depth System.Int32: ~
  -DisplayError Switch: ~
  -Expand System.String:
    values:
    - CoreOnly
    - EnumOnly
    - Both
  -Force Switch: ~
  -GroupBy System.Object: ~
  -InputObject System.Management.Automation.PSObject: ~
  -Property System.Object[]: ~
  -ShowError Switch: ~
  -View System.String: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
