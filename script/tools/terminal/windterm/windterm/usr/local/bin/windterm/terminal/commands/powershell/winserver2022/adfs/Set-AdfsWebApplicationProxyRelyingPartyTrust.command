description: Modifies properties of the relying party trust object for the Web Application
  Proxy
synopses:
- Set-AdfsWebApplicationProxyRelyingPartyTrust [-AlwaysRequireAuthentication <Boolean>]
  [-Identifier <String[]>] [-AccessControlPolicyName <String>] [-AccessControlPolicyParameters
  <Object>] [-IssuanceAuthorizationRules <String>] [-IssuanceAuthorizationRulesFile
  <String>] [-IssuanceTransformRules <String>] [-IssuanceTransformRulesFile <String>]
  [-AdditionalAuthenticationRules <String>] [-AdditionalAuthenticationRulesFile <String>]
  [-Name <String>] [-NotBeforeSkew <Int32>] [-Notes <String>] [-PassThru] [-TokenLifetime
  <Int32>] [-WhatIf] [-Confirm] [<CommonParameters>]
options:
  -AccessControlPolicyName String: ~
  -AccessControlPolicyParameters Object: ~
  -AdditionalAuthenticationRules String: ~
  -AdditionalAuthenticationRulesFile String: ~
  -AlwaysRequireAuthentication Boolean: ~
  -Identifier String[]: ~
  -IssuanceAuthorizationRules String: ~
  -IssuanceAuthorizationRulesFile String: ~
  -IssuanceTransformRules String: ~
  -IssuanceTransformRulesFile String: ~
  -Name String: ~
  -NotBeforeSkew Int32: ~
  -Notes String: ~
  -PassThru Switch: ~
  -TokenLifetime Int32: ~
  -Confirm,-cf Switch: ~
  -WhatIf,-wi Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
