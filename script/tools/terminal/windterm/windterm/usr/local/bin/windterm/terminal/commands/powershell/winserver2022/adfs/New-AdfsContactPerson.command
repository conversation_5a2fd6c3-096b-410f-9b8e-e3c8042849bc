description: Creates a contact person object
synopses:
- New-Adfs<PERSON><PERSON><PERSON><PERSON>erson [-Company <String>] [-EmailAddress <String[]>] [-GivenName
  <String>] [-TelephoneNumber <String[]>] [-Surname <String>] [<CommonParameters>]
options:
  -Company String: ~
  -EmailAddress String[]: ~
  -GivenName String: ~
  -Surname String: ~
  -TelephoneNumber String[]: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
