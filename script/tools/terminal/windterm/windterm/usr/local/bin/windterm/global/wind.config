﻿{
  "application.language": "auto",
  "application.lockScreenTimeout": 30,
  "application.proxy": "None",
  "application.proxyDns": true,
  "application.searchEngines": [
    { "text": "Google", "icon": "logo::google", "url": "https://google.com/search?q={q}" },
    { "text": "Bing", "icon": "logo::bing", "url": "https://bing.com/search?q={q}" },
    { "text": "Github", "icon": "logo::github", "url": "https://github.com/search?q={q}" },
    { "text": "Stackoverflow", "icon": "logo::stackoverflow", "url": "http://stackoverflow.com/search?q={q}" },
    { "text": "Wikipedia", "icon": "logo::wikipedia", "url": "https://en.wikipedia.org/wiki/{q}" },
    { "text": "DuckDuckGo", "icon": "logo::duckduckgo", "url": "https://duckduckgo.com/?q={q}" }
  ],
  "application.themeGui": "dige-black",
  "application.themeText": "dige-black",
  "application.windowOpacity": 1.0,
  "editor.commandModeEnabled": true,
  "editor.text.hideCursorOnTyping": false,
  "editor.hugeFileThreshold": "50",
  "editor.startupSessions": "LastSessions",
  "editor.text.trimTrailingWhitespaceOnSave": false,
  "filer.hiddenItemsVisible": false,
  "focusMode.openFocusModeDialog": true,
  "focusMode.settings": null,
  "grep.leadingContextLines": 2,
  "grep.trailingContextLines": 2,
  "outline.symbolPreviewEnabled": true,
  "output.tabbarVisible": false,
  "quickbar.showCommandsInOrder": false,
  "quickbar.showGroupTitle": true,
  "quickbar.showMultipleGroups": true,
  "tabbar.doubleClickAction": "CloseTab",
  "tabbar.hoverToSelect": 0.2,
  "tabbar.middleClickAction": "RenameTab",
  "tabbar.rightClickAction": "None",
  "tabbar.selectionBehaviorOnRemove" : "SelectPreviousTab",
  "tabbar.showSequenceNumbers": "FirstNine",
  "tabbar.textElideMode" : "ElideMiddle",
  "tabbar.thumbnailRatio": 40,
  "terminal.altScreen.pasteDialog": false,
  "terminal.askBeforeCloseTab": true,
  "terminal.askBeforeCloseWindow": true,
  "terminal.autoComplete.enabled": true,
  "terminal.autoComplete.preselectBestMatch": 0,
  "terminal.autoComplete.threshold": 1,
  "terminal.autoComplete.visibleRows": 7,
  "terminal.autoComplete.completeCommandName": true,
  "terminal.autoComplete.completeCommandOption": true,
  "terminal.autoComplete.completeCommandParameter": true,
  "terminal.autoComplete.completeHistoryCommand": true,
  "terminal.autoComplete.completeQuickCommand": true,
  "terminal.commandHistory.days": 30,
  "terminal.commandHistory.size": 10000,
  "terminal.commandHistory.storage": true,
  "terminal.expect.password": "password:|password for \\S+:",
  "terminal.expect.username": "username:|login:",
  "terminal.localModeEnabled": true,
  "terminal.loginWizard.defaultAuthType": "OneKey",
  "terminal.mouseTracking.click": true,
  "terminal.mouseTracking.doubleClick": true,
  "terminal.mouseTracking.middleClick": true,
  "terminal.mouseTracking.move": true,
  "terminal.mouseTracking.rightClick": false,
  "terminal.mouseTracking.wheel": true,
  "terminal.reimportShellSessions": true,
  "terminal.screen.pasteDialog": true,
  "terminal.startupSessions": "LastSessions",
  "terminal.rightClickAction": "ShowMenu",
  "terminal.text.caretBlink": false,
  "terminal.text.caretShape": 1,
  "terminal.text.hiddenMargins": ["symbol"],
  "terminal.text.hideCursorOnTyping": true,
  "terminal.text.indentGuideVisible": false,
  "terminal.text.wrapColumn": 0,
  "terminal.text.wrapMode": 1,
  "terminal.text.wrapSymbolVisible": true,
  "terminal.usernameEchoMode": "EchoNone",
  "text.alternatingLineColors": false,
  "text.alternatingHexColumnColors": true,
  "text.autoCopySelection": false,
  "text.caretBlink": true,
  "text.caretPeriod": 0.5,
  "text.caretShape": 0,
  "text.caretWidth": 1.5,
  "text.charSpaceFactor": 0.0,
  "text.columnMarkerColumns": "",
  "text.columnMarkerStyle": 0,
  "text.columnMarkerVisible": true,
  "text.detectIndentation": true,
  "text.eol": 0,
  "text.eolVisible": false,
  "text.fontFamily1": "Roboto Mono",
  "text.fontWeight": 50,
  "text.fontSize": 10,
  "text.hexBase": 16,
  "text.hexColumns": 16,
  "text.hexDivisionColumns": 4,
  "text.hexGroupChars": 1,
  "text.hideCursorOnTyping": true,
  "text.hiddenMargins": ["symbol", "timestamp"],
  "text.highlightCaretLine": true,
  "text.highlightCaretWord": true,
  "text.highlightFold": true,
  "text.highlightPair": true,
  "text.highlightSearchResults": true,
  "text.indentGuideStyle": 0,
  "text.indentGuideVisible": true,
  "text.indentSize": 4,
  "text.lineSpaceFactor": 0.0,
  "text.margins": [
    { "name": "symbol", "type": "symbol", "width": 16, "leftPadding": 4, "trackSelection": true },
    { "name": "timestamp", "type": "timestamp", "width": 10, "leftPadding": 4, "alignment": "right", "trackSelection": true },
    { "name": "number", "type": "number", "width": 10, "leftPadding": 4, "alignment": "right", "trackSelection": true },
    { "name": "fold", "type": "fold", "width": 12, "leftPadding": 4, "trackSelection": true },
    { "name": "blank", "type": "blank", "width": 6, "leftPadding": 4, "trackChange": true, "trackSelection": true }
  ],
  "text.multipleCaretEnabled": true,
  "text.preferThemeFonts": true,
  "text.tabSize": 4,
  "text.unifyEolOnSave": false,
  "text.useTabPosition": 1, //0：Nowhere, 1: Anywhere, 2: Indentation
  "text.useTabStop": false,
  "text.virtualSpaceEnabled": false,
  "text.whiteSpaceVisibility": 0, //0：Nowhere, 1: Anywhere, 2: LeadingPosition, 3: TrailingPosition, 4: SelectedPosition
  "text.wrapColumn": 0,
  "text.wrapMode": 2,
  "text.wrapSymbolVisible": true,
  "text.zoomFactor": 0,
  "xserver.autoStart": false,
  "xserver.clipboard": true,
  "xserver.displayNumber": -1,
  "xserver.keyhook": false,
  "xserver.primarySelection": true,
  "xserver.trayIcon": false,
  "xserver.xkbLayout": "auto",
  "xserver.xkbModel": "pc105",
  "xserver.windowMode": "MultiWindows"
}