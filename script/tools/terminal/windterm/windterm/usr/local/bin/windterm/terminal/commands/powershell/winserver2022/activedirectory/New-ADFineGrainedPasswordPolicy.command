description: Creates a new Active Directory fine-grained password policy
synopses:
- powershell New-ADFineGrainedPasswordPolicy [-WhatIf] [-Confirm] [-AuthType <ADAuthType>]
  [-ComplexityEnabled <Boolean>] [-Credential <PSCredential>] [-Description <String>]
  [-DisplayName <String>] [-Instance <ADFineGrainedPasswordPolicy>] [-LockoutDuration
  <TimeSpan>] [-LockoutObservationWindow <TimeSpan>] [-LockoutThreshold <Int32>] [-MaxPasswordAge
  <TimeSpan>] [-MinPasswordAge <TimeSpan>] [-MinPasswordLength <Int32>] [-Name] <String>
  [-OtherAttributes <Hashtable>] [-PassThru] [-PasswordHistoryCount <Int32>] [-Precedence]
  <Int32> [-ProtectedFromAccidentalDeletion <Boolean>] [-ReversibleEncryptionEnabled
  <Boolean>] [-Server <String>] [<CommonParameters>]
options:
  -AuthType ADAuthType:
    values:
    - Negotiate
    - Basic
  -ComplexityEnabled Boolean: ~
  -Confirm,-cf Switch: ~
  -Credential PSCredential: ~
  -Description String: ~
  -DisplayName String: ~
  -Instance ADFineGrainedPasswordPolicy: ~
  -LockoutDuration TimeSpan: ~
  -LockoutObservationWindow TimeSpan: ~
  -LockoutThreshold Int32: ~
  -MaxPasswordAge TimeSpan: ~
  -MinPasswordAge TimeSpan: ~
  -MinPasswordLength Int32: ~
  -Name String:
    required: true
  -OtherAttributes Hashtable: ~
  -PassThru Switch: ~
  -PasswordHistoryCount Int32: ~
  -Precedence Int32:
    required: true
  -ProtectedFromAccidentalDeletion Boolean: ~
  -ReversibleEncryptionEnabled Boolean: ~
  -Server String: ~
  -WhatIf,-wi Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
