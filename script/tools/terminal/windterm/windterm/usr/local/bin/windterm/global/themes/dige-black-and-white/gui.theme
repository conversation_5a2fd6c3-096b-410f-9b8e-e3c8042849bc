ApplicationPalette {
	qproperty-link: #007acc;
	qproperty-windowDisabled: #f5f5f5;
	qproperty-placeholderText: silver;
	qproperty-placeholderTextDisabled: silver;
	qproperty-placeholderTextInactive: silver;	
}

QWidget {
	font-family: "Roboto Mono", "Fira Code", "${SystemFonts}";
	font-size: 9pt;
	color: #333333;
}
QWidget:disabled {
	color: gray;
}

QAbstractItemView {
	outline: none;
}
QAbstractItemView:active {
	selection-background-color: #C0C0C0;
}
QAbstractItemView:!active {
	selection-background-color: #80C0C0C0;
}
QAbstractItemView QLineEdit:focus {
	border: 1px solid dodgerblue;
	background-color: white;
}
QAbstractItemView QProgressBar {
	max-height: 16px;
}
QAbstractItemView QToolButton:hover {
	background-color: #222222;
}
QAbstractItemView QWidget {
	background-color: transparent;
}
QAbstractItemView QScrollBar {
	background-color: white;
}
QAbstractItemView #TitleLabel, QAbstractItemView #SubtitleLabel {
	color: #444444;
	selection-color: #4d90fe;
	selection-background-color: transparent;
}
QAbstractItemView::item {
	padding: 2px;
}
QAbstractItemView::item:selected {
	color: #444444;
}
QAbstractItemView::indicator:unchecked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-unchecked.svg);
}
QAbstractItemView::indicator:checked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-checked.svg);
}

QAbstractSpinBox {
	border: 1px solid silver;
	padding: 2px;
}
QAbstractSpinBox::disabled {
	background-color: #f5f5f5;
}
QAbstractSpinBox:focus {
	border: 1px solid dodgerblue;
}
QAbstractSpinBox::up-button {
	width: 8px;
	padding: 0px 4px 0px 0px;
	image: url(${AppDir}/global/themes/default/images/button-up.svg);
}
QAbstractSpinBox::down-button {
	width: 8px;
	padding: 0px 4px 4px 0px;
	image: url(${AppDir}/global/themes/default/images/button-down.svg);
}

QListView::item:hover {
	background-color: #80C0C0C0;
}
QListView::indicator {
	position: absolute;
	left: -4px;
	width: 24px;
	height: 24px;
}
QTreeView QLabel {
	padding-top: 2px;
	padding-bottom: 2px;
}
QHeaderView::section {
	background-color: #f0f0f0;
}
QListWidget, QTreeWidget, QTableWidget {
	border: 1px solid silver;
}
QTableWidget::item {
	padding-top: 0px;
	padding-bottom: 0px;
}

QCheckBox::indicator {
	width: 24px;
	height: 24px;
}
QCheckBox::indicator:checked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-checked.svg);
}
QCheckBox::indicator:unchecked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-unchecked.svg);
}
QCheckBox::indicator::disabled {
	image: url(${AppDir}/global/themes/default/images/checkbox-disabled.svg);
}

QComboBox {
	border: 1px solid silver;
	height: 21px;
	padding: 0px 2px 0px 2px;
}
QComboBox::disabled {
	background-color: #f5f5f5;
}
QComboBox:focus {
	border: 1px solid dodgerblue;
}
QComboBox QLineEdit, QComboBox QLineEdit:focus {
	padding: 0px;
	border: none;
	background-color: transparent;
}
QComboBox::drop-down {
	width: 8px;
	padding-right: 4px;
	image: url(${AppDir}/global/themes/default/images/button-down.svg);
}

QDialog QFrame[frameShape = "4"] {
	color: silver;
}

QLineEdit {
	padding: 2px;
	border: 1px solid silver;
}
QLineEdit:disabled {
	background-color: #f5f5f5;
}
QLineEdit:focus {
	border: 1px solid dodgerblue;
}
QLineEdit[state = "Error"] {
	border: 1px solid deeppink;
}
QLineEdit > QAbstractButton:disabled {
	background-color: #f5f5f5;
}
QLineEdit > SwitchButton:checked {
	border: none;
	padding: 4px;
	background-color: #60acacac;
}
QLineEdit > SideButton:hover, QLineEdit > SwitchButton:hover {
	border: 1px solid #acacac;
	background-color: #60acacac;
}
QLineEdit > SideButton:pressed, QLineEdit > SwitchButton:pressed {
	border: 1px solid #acacac;
	background-color: #80acacac;
}
QLineEdit > #CompleterButton {
	padding-top: 4px;
	padding-bottom: 4px;
}
QLineEdit > #CompleterButton:checked {
	border: none;
	background-color: transparent;
}

QMenuBar {
	border-bottom: 1px solid silver;
	padding: 1px;
}
QMenuBar::item {
	margin-top: 6px;
	padding: 2px 4px 2px 4px;
}
QMenuBar::item:pressed, QMenuBar::item:selected {
	background-color: #007acc;
	color: white;
}

QMenu {
	border: 1px solid silver;
	background-color: #dcdcdc;
}
QMenu::item {
	padding: 6px 24px 6px 12px;
	background-color: white;
}
QMenu::item:default {
	font-weight: bold;
	color: black;
}
QMenu::item:disabled {
	color: gray;
}
QMenu::item:selected {
	border: none;
	color: white;
	background-color: #007acc;
}
QMenu::indicator {
	padding-right: -4px;
	width: 24px;
	height: 24px;
}
QMenu::indicator:non-exclusive:unchecked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-unchecked.svg);
}
QMenu::indicator:non-exclusive:checked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/checkbox-checked.svg);
}
QMenu::indicator:exclusive:unchecked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/radiobox-unchecked.svg);
}
QMenu::indicator:exclusive:checked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/radiobox-checked.svg);
}
QMenu::icon {
	margin: 6px;
	padding: 1px;
	width: 24px;
	height: 24px;
}
QMenu::icon:checked {
	border: 1px solid silver;
	background-color: #dcdcdc;
}
QMenu::separator {
	height: 1px;
	background-color: silver;
}
QMenu::right-arrow {
	position: relative;
	left: -10px;
}
QMenu FilterEdit > #CountLabel {
	padding-right: 2px;
	background-color: transparent;
}

QPlainTextEdit {
	border: 1px solid silver;
}
QPlainTextEdit::disabled {
	background-color: #f5f5f5;
}
QPlainTextEdit:focus {
	border: 1px solid dodgerblue;
}
QPlainTextEdit[state = "Error"] {
	border: 1px solid deeppink;
}

QProgressBar {
	text-align: center;
}
QProgressBar::chunk:!active {
	background-color: #0183EA; /* Should same as the normal background color of progressbar chunk. */
}
QProgressBar[state = "Error"]::chunk {
	background-color: red;
}

QPushButton::menu-indicator {
	width: 8px;
	padding-right: 3px;
	image: url(${AppDir}/global/themes/default/images/button-down.svg);
	subcontrol-position: right center;
}

QRadioButton::indicator {
	width: 24px;
	height: 24px;
}
QRadioButton::indicator:checked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/radiobox-checked.svg);
}
QRadioButton::indicator:unchecked {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/radiobox-unchecked.svg);
}
QRadioButton::indicator::disabled {
	image: url(${AppDir}/global/themes/default/images/radiobox-disabled.svg);
}

QScrollBar {
	border: none;
	background-color: transparent;
	margin: 0px 0px 0px 0px;
}
QScrollBar:horizontal {
	height: 8px;
}
QScrollBar:vertical {
	width: 8px;
}
QScrollBar::handle {
	background-color: #80606060;
}
QScrollBar::handle:horizontal {
	min-width: 20px;
}
QScrollBar::handle:vertical {
	min-height: 20px;
}
QScrollBar::handle:hover {
	background-color: #c0606060;
}
QScrollBar::handle:disabled {
	background-color: none;
}
QScrollBar::add-line, QScrollBar::sub-line {
	width: 0px;
	height: 0px;
}

QSplitter::handle:horizontal {
	width: 1px;
	background-color: silver;
}
QSplitter::handle:vertical {
	height: 1px;
	background-color: silver;
}

QStatusBar QLabel {
	padding-left: 4px;
	padding-right: 4px;
}
QStatusBar[messageLevel = "NormalMessage"], QStatusBar[messageLevel = "NormalMessage"] > * {
	color: black;
}
QStatusBar[messageLevel = "InfoMessage"], QStatusBar[messageLevel = "InfoMessage"] > * {
	background-color: #007acc;
	color: white;
}
QStatusBar[messageLevel = "WarningMessage"], QStatusBar[messageLevel = "WarningMessage"] > * {
	background-color: chocolate;
	color: white;
}
QStatusBar[messageLevel = "ErrorMessage"], QStatusBar[messageLevel = "ErrorMessage"] > * {
	background-color: deeppink;
	color: white;
}

QTabBar::scroller {
	width: 0px;
}
QTabBar::tear {
	image: none;
}
QTabBar::tab {
	background-color: #dcdcdc;
	border: none;
	border-right: 1px solid silver;
	border-bottom: 1px solid silver;
	height: 26px;
	padding-left: 8px;
	padding-right: 8px;
}
QTabBar::tab:selected {
	background-color: #f5f5f5;
	border-bottom: 1px solid #f5f5f5;
}
QTabBar::tab:hover {
	background-color: #f5f5f5;
}
QTabBar::tab:only-one {
	border: none;
}
QTabBar::tab:selected:last {
	border: none;
}

QTabWidget:pane {
	border: 1px solid silver;
}
QTabWidget:tab-bar {
	bottom: -1px;
}
QTabWidget QTabBar::tab:selected {
	border-top: 1px solid silver;
}
QTabWidget QTabBar::tab:first {
	border-left: 1px solid silver;
}
QTabWidget QTabBar::tab:last {
	border-right: 1px solid silver;
}

QToolBar {
	border: 2px solid transparent;
	margin: 2px;
	spacing: 2px;
}
QToolBar::separator {
	width: 1px;
	background-color: #555555;
}
QToolBar QLabel {
	padding: 4px;
}

QToolButton:hover {
	background-color: white;
}
QToolButton::menu-indicator {
	image: none;
}

QToolTip {
	font-family: "Roboto Mono", "Fira Code";
	font-size: 9pt;
	border: 1px solid #f5f5f5;
	padding: 4px;
}

AddressBar > Completer > QListView {
	border: none;
	border-bottom: 1px solid silver;
	selection-background-color: white;
}
AddressBar > Completer > QListView #CategoryLabel {
	border: none;
	border-right: 1px solid silver;
	padding-right: 2px;
}
EditorWindow #CategoryLabel {
	min-width: 130px;
	max-width: 130px;
}
TerminalWindow #CategoryLabel {
	min-width: 104px;
	max-width: 104px;
}
AddressBar > Completer > QListView #CategoryLabel[category = "Opened"] {
	color: mediumseagreen;
}
AddressBar > Completer > QListView #CategoryLabel[category = "Recent"] {
	color: darkorange;
}
AddressBar > Completer > QListView #IconLabel {
	padding: 0px;
}
AddressBar > Completer > QListView #SubtitleLabel {
	color: #007acc;
}
AddressBar > Completer > QListView #TitleLabel {
	margin-top: 0px;
	margin-bottom: 0px;
}
AddressBar > QLineEdit {
	padding: 4px;
	border: 1px solid silver;
	background-color: white;
}
AddressBar > QLineEdit > BreadButton {
	background-color: transparent;
}
AddressBar > QLineEdit > BreadButton[type = "ArrowButton"], AddressBar > QLineEdit > BreadButton[type = "ChevronButton"] {
	padding-left: -4px;
	padding-right: -4px;
}
AddressBar > QLineEdit > BreadButton[type = "TextButton"] {
	padding-right: -4px;
}
AddressBar > QLineEdit > BreadButton[partial = true] {
	color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0.0 black stop: 0.5 darkgray stop: 1.0 white);
}
AddressBar > QLineEdit > BreadButton[important = false] {
	color: black;
}
AddressBar > QLineEdit > BreadButton:hover, AddressBar > QLineEdit > BreadButton:pressed {
	background-color: #007acc;
	color: white;
}

AppletBar QToolButton:checked {
	border: none;
	background-color: whitesmoke;
}

ButtonBarPanel {
	background: white;
	border: 1px solid silver;
}
ButtonBarPanel ButtonBar {
	border-top: 1px solid silver; 
}
ButtonBarPanel QAbstractItemView,
ButtonBarPanel QPlainTextEdit,
ButtonBarPanel QToolButton {
	border: none;
}
ButtonBarPanel QToolButton:hover {
	background: #80C0C0C0;
}
ButtonBarPanel QToolButton:checked {
	background: #60acacac;
}

CommandPalette {
	background-color: #f5f5f5;
	border: 1px solid silver;
	border-top: none;
}
CommandPalette > QLineEdit {
	font-size: 10pt;
}
CommandPalette > QLineEdit #CountLabel {
	padding-right: 2px;
	background-color: transparent;
}
CommandPalette > QListView {
	background-color: transparent;
	border: none;
}
CommandPalette > QListView::item::selected {
	background-color: #80C0C0C0;
}
CommandPalette > QListView[singleLine=true] #SubtitleLabel {
	color: gray;
}
CommandPalette > QListView[singleLine=true] #TipLabel {
	margin-top: 4px;
	margin-bottom: 4px;
}

ExplorerPane QWidget,
ExplorerPane QToolButton:hover {
	border: none;
	background-color: transparent;
}
ExplorerPane #StateLabel {
	background-color: transparent;
	min-width: 6px;
	max-width: 6px;
}
ExplorerPane #TitleLabel[state = "Warning"] {
	color: darkorange;
}
ExplorerPane #TitleLabel[state = "Error"] {
	color: red;
}
ExplorerPane #TitleLabel[state = "Changed"] {
	color: dodgerblue;
}
ExplorerPane #TitleLabel[state = "Saved"] {
	color: mediumseagreen;
}
ExplorerPane #StateLabel[channel = "A"] {
	background-color: deeppink;
}
ExplorerPane #StateLabel[channel = "B"] {
	background-color: dodgerblue;
}
ExplorerPane #StateLabel[channel = "C"] {
	background-color: darkorange;
}
ExplorerPane #StateLabel[channel = "D"] {
	background-color: mediumseagreen;
}

FocusModeDialog QToolButton:checked {
	background-color: #007acc;
}

KitBar {
	qproperty-iconSize: 20px;
}
KitBar, KitBar QWidget {
	border: none;
	background-color: #dcdcdc;
}
KitBar QToolButton {
	padding: 6px 6px 6px 6px;
	border-left: 4px solid transparent;
}
KitBar QToolButton:checked {
	border-left: 4px solid #007acc;
}

KitPane > QFrame {
	border: none;
}
KitPane FilterEdit {
	padding-top: 5px;
	padding-bottom: 5px;
	background-color: transparent;
	border: none;
	border-bottom: 1px solid silver;
}
KitPane FilterEdit:focus {
	border: none;
	border-bottom: 1px solid dodgerblue;
}

LockScreen #TitleLabel {
	color: #41adff;
	font-size: 11pt;
}

LoginWizard > QFrame {
	border: 1px solid silver;
	background-color: #f5f5f5;
}
LoginWizard > QLabel {
	color: #41adff;
	font-size: 11pt;
	padding: 8px;
}

#MenuBarCorner #FocusModeButton,
#MenuBarCorner #TunnelsButton {
	padding-right: -12px;
}
#MenuBarCorner #FocusModeButton:checked {
	border: none;
	background-color: #007acc;
	color: white;
}

MessageBar {
	border-top: 1px solid silver;
	background-color: #ffffe1;
}
MessageBar QLabel {
	background-color: transparent;
}
MessageBar #IconLabel[state = "Critical"] {
	qproperty-pixmap: url(${AppDir}/global/themes/default/images/critical.svg);
}
MessageBar #IconLabel[state = "Information"] {
	qproperty-pixmap: url(${AppDir}/global/themes/default/images/information.svg);
}
MessageBar #IconLabel[state = "Question"] {
	qproperty-pixmap: url(${AppDir}/global/themes/default/images/question.svg);
}
MessageBar #IconLabel[state = "Warning"] {
	qproperty-pixmap: url(${AppDir}/global/themes/default/images/warning.svg);
}
MessageBar #CloseButton {
	background-color: transparent;
	border: none;
	image: url(${AppDir}/global/themes/dige-black-and-white/images/close-normal.svg);
}
MessageBar #CloseButton:hover {
	image: url(${AppDir}/global/themes/default/images/close-hover.svg);
}
MessageBar #ExpandButton {
	background-color: transparent;
	border: none;
}
MessageBar #PauseButton[state = "Paused"] {
	color: deeppink;
}

#PaneGroup > QTabBar::tab {
	max-width: 160px;
}

PasswordDialog #IconLabel {
	font-size: 22pt;
}
PasswordDialog #TitleLabel {
	font-size: 10pt;
}

PopupWidget, PopupWidget QWidget {
	background-color: white;
}
PopupWidget QLabel[enabled=false] {
	color: darkgray;
}

QuickBar #CloseButton, QuickBar #ConfigButton {
	border: none;
}
QuickBar #QuickButton {
	border: none;
	padding: 4px 16px 4px 16px;
}
QuickBar #ActionButton {
	padding: 0px -10px 0px 0px;
}

SearchPane > QToolButton {
	background-color: transparent;
	border: none;
}

ShellPane > TabGroup > QTabBar::tab {
	background-color: #f0f0f0;
	border: none;
}
ShellPane > TabGroup > QTabBar::tab:selected,
ShellPane > TabGroup > QTabBar::tab:selected:last {
	border-bottom: 2px solid dodgerblue;
}

SideSeparator {
	background-color: silver;
}

SlipButton {
	qproperty-checkedColor: #007acc;
	qproperty-errorColor: red;
	qproperty-handleColor: white;
	qproperty-uncheckedColor: lightgray;
	qproperty-warningColor: orange;
}

TabBar {
	qproperty-highlightBottomRight: 2px 2px;
	qproperty-highlightTopLeft: 3px 1px;
	qproperty-showSelectedTabCloseButtonOnly: true;
}

TabGroup QToolButton {
	background-color: transparent;
	border: none;
}
TabGroup #TextLabel {
	padding-left: 4px;
	qproperty-alignment: 'AlignVCenter | AlignLeft';
}
TabGroup #TextLabel[state = "Unactivated"] {
	font-style: italic;
	color: gray;
}
TabGroup #TextLabel[state = "Warning"] {
	color: darkorange;
}
TabGroup #TextLabel[state = "Error"] {
	color: red;
}
TabGroup #TextLabel[state = "Changed"] {
	color: dodgerblue;
}
TabGroup #TextLabel[state = "Saved"] {
	color: mediumseagreen;
}
TabGroup #CloseButton[state = "Unactivated"] {
	image: url(${AppDir}/global/themes/dige-black-and-white/images/close-normal.svg);
}
TabGroup #CloseButton[state = "Normal"] {
	image: url(${AppDir}/global/themes/default/images/close-unactivated.svg);
}
TabGroup #CloseButton[state = "Warning"] {
	image: url(${AppDir}/global/themes/default/images/close-warning.svg);
}
TabGroup #CloseButton[state = "Error"] {
	image: url(${AppDir}/global/themes/default/images/close-error.svg);
}
TabGroup #CloseButton[state = "Changed"] {
	image: url(${AppDir}/global/themes/default/images/close-changed.svg);
}
TabGroup #CloseButton[state = "Saved"] {
	image: url(${AppDir}/global/themes/default/images/close-saved.svg);
}

#TabListMenu {
	background-color: white;
}
#TabListMenu QWidget {
	background-color: transparent;
}
#TabListMenu #CloseButton {
	border: none;
	image: url(${AppDir}/global/themes/default/images/close-normal.svg);
}
#TabListMenu #CloseButton:hover {
	image: url(${AppDir}/global/themes/default/images/close-hover.svg);
}
#TabListMenu #MenuItemWidget[state = "Active"] {
	background-color: #007acc;
}
#TabListMenu #TextLabel[default = "true"] {
	font-weight: bold;
}
#TabListMenu #TextLabel[state = "Active"] {
	color: white;
}

#ToolBarPopup QLabel {
	border: none;
	border-bottom: 1px solid darkgray;
	color: darkgray;
	padding-bottom: 4px;
	margin-bottom: 6px;
}
#ToolBarPopup QToolButton[popupMode="1"] {
	margin-left: 20px;
}

TextView CompleteList {
	border: 1px solid silver;
	selection-color: white;
}
TextView CompleteList::item:selected {
	background-color: #007acc;
}
TextView HoverButton {
	color: black;
	background-color: #80ffffff;
}
TextView HoverButton:hover {
	background-color: white;
}
TextView > QFrame {
	border: none;
	border-top: 1px solid silver;
}
TextView > QAbstractScrollArea QScrollBar {
	background-color: #f5f5f5;
	border: none;
	margin: 0px;
}
TextView > QAbstractScrollArea QScrollBar:vertical {
	width: 16px;
}

#TextPreview {
	background-color: #20FFD700;
	border-left: none;
	border-top: 2px solid silver;
	border-right: none;
	border-bottom: 2px solid silver;
}

TransferBar #CloseButton {
	border: none;
}

TransferLabel {
	border: none;
	border-right: 1px solid silver;
	qproperty-progressColor: #0183EA; /* Should same as the normal background color of progressbar chunk. */
}
TransferLabel[state = "Error"] {
	qproperty-progressColor: red; /* Should same as the error background color of progressbar chunk. */
}
TransferLabel:hover {
	background-color: white;
}
TransferLabel QWidget {
	background-color: transparent;
}
TransferLabel QToolButton:hover {
	background-color: #60acacac;
}

#ViewGroup > QTabBar::tab {
	max-width: 200px;
	min-width: 80px;
}
#ViewGroup > QTabBar::tab:selected {
	border-top: 2px solid dodgerblue;
}