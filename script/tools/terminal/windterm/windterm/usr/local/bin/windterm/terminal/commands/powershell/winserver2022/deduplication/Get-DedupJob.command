description: Returns status and information for currently running or queued deduplication
  jobs
synopses:
- Get-DedupJob [[-Type] <Type[]>] [[-Volume] <String[]>] [-CimSession <CimSession[]>]
  [-ThrottleLimit <Int32>] [-<PERSON><PERSON>ob] [<CommonParameters>]
options:
  -AsJob Switch: ~
  -CimSession,-Session CimSession[]: ~
  -ThrottleLimit Int32: ~
  -Type Type[]:
    values:
    - Optimization
    - GarbageCollection
    - Scrubbing
    - Unoptimization
  -Volume,-Path,-Name String[]: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
