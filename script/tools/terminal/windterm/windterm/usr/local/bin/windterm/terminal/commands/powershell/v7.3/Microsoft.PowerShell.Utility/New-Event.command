description: Creates a new event
synopses:
- New-Event [-SourceIdentifier] <String> [[-Sender] <PSObject>] [[-EventArguments]
  <PSObject[]>] [[-MessageData] <PSObject>] [<CommonParameters>]
options:
  -EventArguments System.Management.Automation.PSObject[]: ~
  -MessageData System.Management.Automation.PSObject: ~
  -Sender System.Management.Automation.PSObject: ~
  -SourceIdentifier System.String:
    required: true
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
