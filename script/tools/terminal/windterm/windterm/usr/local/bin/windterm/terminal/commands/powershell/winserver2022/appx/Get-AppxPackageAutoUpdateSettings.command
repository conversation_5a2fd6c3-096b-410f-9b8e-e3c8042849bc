description: Available in the Windows Insider Preview Builds of Windows 10, is the
  `Get-AppxPackageAutoUpdateSettings` PowerShell cmdlet. The `Get-AppxPackageAutoUpdateSettings`
  PowerShell cmdlet provides visibility to the settings configured on a Windows 10
  client device for a particular Windows App
synopses:
- Get-AppxPackageAutoUpdateSettings [[-PackageFullName] <String>] [-ShowUpdateAvailability]
  [-AllUsers] [<CommonParameters>]
options:
  -PackageFullName String: ~
  -ShowUpdateAvailability Switch: ~
  -AllUsers Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
