description: Applies configuration settings to the App-V Client
synopses:
- Set-AppvClientConfiguration [-AllowHighCostLaunch <Boolean>] [-AutoLoad <UInt32>]
  [-CertFilterForClientSsl <String>] [-EnablePackageScripts <Boolean>] [-EnablePublishingRefreshUI
  <Boolean>] [-IntegrationRootGlobal <String>] [-IntegrationRootUser <String>] [-LocationProvider
  <String>] [-MigrationMode <Boolean>] [-PackageInstallationRoot <String>] [-PackageSourceRoot
  <String>] [-RequirePublishAsAdmin <Boolean>] [-ReestablishmentInterval <UInt32>]
  [-ReestablishmentRetries <UInt32>] [-ReportingDataBlockSize <UInt32>] [-ReportingDataCacheLimit
  <UInt32>] [-ReportingEnabled <Boolean>] [-ReportingInterval <UInt32>] [-ReportingRandomDelay
  <UInt32>] [-ReportingServerURL <String>] [-ReportingStartTime <UInt32>] [-RoamingFileExclusions
  <String>] [-RoamingRegistryExclusions <String>] [-SharedContentStoreMode <Boolean>]
  [-VerifyCertificateRevocationList <Boolean>] [-ExperienceImprovementOptIn <Boolean>]
  [-ProcessesUsingVirtualComponents <String[]>] [-EnableDynamicVirtualization <Boolean>]
  [-IgnoreLocationProvider <Boolean>] [-SupportBranchCache <Boolean>] [<CommonParameters>]
options:
  -AllowHighCostLaunch Boolean: ~
  -AutoLoad UInt32: ~
  -CertFilterForClientSsl String: ~
  -EnableDynamicVirtualization Boolean: ~
  -EnablePackageScripts Boolean: ~
  -EnablePublishingRefreshUI Boolean: ~
  -ExperienceImprovementOptIn Boolean: ~
  -IgnoreLocationProvider Boolean: ~
  -IntegrationRootGlobal String: ~
  -IntegrationRootUser String: ~
  -LocationProvider String: ~
  -MigrationMode Boolean: ~
  -PackageInstallationRoot String: ~
  -PackageSourceRoot String: ~
  -ProcessesUsingVirtualComponents String[]: ~
  -ReestablishmentInterval UInt32: ~
  -ReestablishmentRetries UInt32: ~
  -ReportingDataBlockSize UInt32: ~
  -ReportingDataCacheLimit UInt32: ~
  -ReportingEnabled Boolean: ~
  -ReportingInterval UInt32: ~
  -ReportingRandomDelay UInt32: ~
  -ReportingServerURL String: ~
  -ReportingStartTime UInt32: ~
  -RequirePublishAsAdmin Boolean: ~
  -RoamingFileExclusions String: ~
  -RoamingRegistryExclusions String: ~
  -SharedContentStoreMode Boolean: ~
  -SupportBranchCache Boolean: ~
  -VerifyCertificateRevocationList Boolean: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
