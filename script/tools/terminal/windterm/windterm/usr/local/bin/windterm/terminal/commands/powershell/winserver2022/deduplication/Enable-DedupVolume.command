description: Enables data deduplication on one or more volumes
synopses:
- Enable-DedupVolume [-Volume] <String[]> [-DataAccess] [-UsageType <UsageType>] [-CimSession
  <CimSession[]>] [-ThrottleLimit <Int32>] [-As<PERSON>ob] [<CommonParameters>]
options:
  -AsJob Switch: ~
  -CimSession,-Session CimSession[]: ~
  -DataAccess Switch: ~
  -ThrottleLimit Int32: ~
  -UsageType UsageType:
    values:
    - Default
    - HyperV
    - Backup
  -Volume,-DeviceId,-Path,-Name String[]:
    required: true
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
