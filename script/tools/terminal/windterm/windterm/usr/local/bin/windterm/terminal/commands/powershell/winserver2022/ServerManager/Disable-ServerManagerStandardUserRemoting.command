description: Disables access for specified standard users to event, service, performance
  counter, and role and feature inventory data that is collected by Server Manager
  for a server. This cmdlet performs the opposite action, for specified users, of
  the Enable-ServerManagerStandardUserRemoting cmdlet
synopses:
- Disable-ServerManagerStandardUserRemoting [-User] <String[]> [-Force] [-WhatIf]
  [-Confirm] [<CommonParameters>]
options:
  -Force Switch: ~
  -User String[]:
    required: true
  -Confirm,-cf Switch: ~
  -WhatIf,-wi Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
