description: Subscribes to the events that are generated by a Microsoft .NET Framework
  object
synopses:
- Register-ObjectEvent [-InputObject] <PSObject> [-EventName] <String> [[-SourceIdentifier]
  <String>] [[-Action] <ScriptBlock>] [-MessageData <PSObject>] [-SupportEvent] [-Forward]
  [-MaxTriggerCount <Int32>] [<CommonParameters>]
options:
  -Action System.Management.Automation.ScriptBlock: ~
  -EventName System.String:
    required: true
  -Forward Switch: ~
  -InputObject System.Management.Automation.PSObject:
    required: true
  -MaxTriggerCount System.Int32: ~
  -MessageData System.Management.Automation.PSObject: ~
  -SourceIdentifier System.String: ~
  -SupportEvent Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
