description: Removes the extension points for packages
synopses:
- Unpublish-AppvClientPackage [-Global] [-UserSID <String>] [-PackageId] <Guid> [-VersionId]
  <Guid> [<CommonParameters>]
- Unpublish-AppvClientPackage [-Global] [-UserSID <String>] [-Package] <AppvClientPackage>
  [<CommonParameters>]
- Unpublish-AppvClientPackage [-Global] [-UserSID <String>] [-Name] <String> [[-Version]
  <String>] [<CommonParameters>]
options:
  -Global Switch: ~
  -Name String:
    required: true
  -Package AppvClientPackage:
    required: true
  -PackageId Guid:
    required: true
  -UserSID String: ~
  -Version String: ~
  -VersionId Guid:
    required: true
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
