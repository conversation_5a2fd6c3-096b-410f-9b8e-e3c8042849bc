description: Subscribes to events that are generated by the PowerShell engine and
  by the `New-Event` cmdlet
synopses:
- Register-EngineEvent [-SourceIdentifier] <String> [[-Action] <ScriptBlock>] [-MessageData
  <PSObject>] [-SupportEvent] [-Forward] [-MaxTriggerCount <Int32>] [<CommonParameters>]
options:
  -Action System.Management.Automation.ScriptBlock: ~
  -Forward Switch: ~
  -MaxTriggerCount System.Int32: ~
  -MessageData System.Management.Automation.PSObject: ~
  -SourceIdentifier System.String:
    required: true
  -SupportEvent Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
