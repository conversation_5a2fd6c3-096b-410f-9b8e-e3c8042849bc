description: Removes a relying party web content object
synopses:
- Remove-AdfsRelyingPartyWebContent [[-Locale] <CultureInfo>] -TargetRelyingPartyName
  <String> [-WhatIf] [-Confirm] [<CommonParameters>]
- Remove-AdfsRelyingPartyWebContent [-TargetRelyingPartyWebContent] <AdfsRelyingPartyWebContent>
  [-WhatIf] [-Confirm] [<CommonParameters>]
options:
  -Locale CultureInfo: ~
  -TargetRelyingPartyName,-Name String:
    required: true
  -TargetRelyingPartyWebContent,-TargetWebContent AdfsRelyingPartyWebContent:
    required: true
  -Confirm,-cf Switch: ~
  -WhatIf,-wi Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
