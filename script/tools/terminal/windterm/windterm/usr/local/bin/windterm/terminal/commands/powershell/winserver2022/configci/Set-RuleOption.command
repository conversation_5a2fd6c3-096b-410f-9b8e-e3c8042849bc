description: Modifies rule options in a Code Integrity policy
synopses:
- Set-RuleOption [-Delete] [-FilePath] <String> [-Option] <Int32> [<CommonParameters>]
- Set-RuleOption [-Help] [<CommonParameters>]
options:
  -Delete,-d Switch: ~
  -FilePath,-f String:
    required: true
  -Help,-h Switch:
    required: true
  -Option,-o Int32:
    required: true
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
