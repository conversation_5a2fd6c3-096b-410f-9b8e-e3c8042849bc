description: Formats the output as a table
synopses:
- Format-Table [-AutoSize] [-RepeatHeader] [-HideTableHeaders] [-Wrap] [[-Property]
  <Object[]>] [-GroupBy <Object>] [-View <String>] [-ShowError] [-DisplayError] [-Force]
  [-Expand <String>] [-InputObject <PSObject>] [<CommonParameters>]
options:
  -AutoSize Switch: ~
  -DisplayError Switch: ~
  -Expand System.String:
    values:
    - CoreOnly
    - EnumOnly
    - Both
  -Force Switch: ~
  -GroupBy System.Object: ~
  -HideTableHeaders Switch: ~
  -InputObject System.Management.Automation.PSObject: ~
  -Property System.Object[]: ~
  -RepeatHeader Switch: ~
  -ShowError Switch: ~
  -View System.String: ~
  -Wrap Switch: ~
  -Debug,-db Switch: ~
  -ErrorAction,-ea ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -ErrorVariable,-ev String: ~
  -InformationAction,-ia ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -InformationVariable,-iv String: ~
  -OutVariable,-ov String: ~
  -OutBuffer,-ob Int32: ~
  -PipelineVariable,-pv String: ~
  -Verbose,-vb Switch: ~
  -WarningAction,-wa ActionPreference:
    values:
    - Break
    - Suspend
    - Ignore
    - Inquire
    - Continue
    - Stop
    - SilentlyContinue
  -WarningVariable,-wv String: ~
