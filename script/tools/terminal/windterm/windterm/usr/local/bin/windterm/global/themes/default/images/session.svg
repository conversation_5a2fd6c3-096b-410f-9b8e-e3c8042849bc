<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="2462px" height="932px" version="1.1" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd"
viewBox="0 0 14822 5034"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <g>
   <g id="square-firebrick">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="firebrick" stroke="firebrick" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-firebrick">
    <circle fill="firebrick" stroke="firebrick" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-firebrick">
    <path fill="firebrick" stroke="firebrick" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="firebrick" stroke="firebrick" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="firebrick" stroke="firebrick" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="firebrick" stroke="firebrick" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-firebrick">
    <polygon fill="firebrick" stroke="firebrick" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-firebrick">
    <path fill="firebrick" fill-rule="nonzero" stroke="firebrick" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-firebrick">
    <polygon fill="firebrick" stroke="firebrick" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-firebrick">
    <path fill="firebrick" fill-rule="nonzero" stroke="firebrick" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-firebrick">
   <path fill="firebrick" stroke="firebrick" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-firebrick">
    <path fill="firebrick" fill-rule="nonzero" stroke="firebrick" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-firebrick">
   <path fill="firebrick" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  </g>
  <g id="square-gold">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="gold" stroke="gold" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-gold">
    <circle fill="gold" stroke="gold" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-gold">
    <path fill="gold" stroke="gold" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="gold" stroke="gold" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="gold" stroke="gold" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="gold" stroke="gold" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-gold">
    <polygon fill="gold" stroke="gold" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-gold">
    <path fill="gold" fill-rule="nonzero" stroke="gold" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-gold">
    <polygon fill="gold" stroke="gold" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-gold">
    <path fill="gold" fill-rule="nonzero" stroke="gold" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-gold">
   <path fill="gold" stroke="gold" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-gold">
    <path fill="gold" fill-rule="nonzero" stroke="gold" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-gold">
   <path fill="gold" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-forestgreen">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="forestgreen" stroke="forestgreen" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-forestgreen">
    <circle fill="forestgreen" stroke="forestgreen" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-forestgreen">
    <path fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-forestgreen">
    <polygon fill="forestgreen" stroke="forestgreen" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-forestgreen">
    <path fill="forestgreen" fill-rule="nonzero" stroke="forestgreen" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-forestgreen">
    <polygon fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-forestgreen">
    <path fill="forestgreen" fill-rule="nonzero" stroke="forestgreen" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-forestgreen">
   <path fill="forestgreen" stroke="forestgreen" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-forestgreen">
    <path fill="forestgreen" fill-rule="nonzero" stroke="forestgreen" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-forestgreen">
   <path fill="forestgreen" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-dodgerblue">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="dodgerblue" stroke="dodgerblue" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-dodgerblue">
    <circle fill="dodgerblue" stroke="dodgerblue" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-dodgerblue">
    <path fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-dodgerblue">
    <polygon fill="dodgerblue" stroke="dodgerblue" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-dodgerblue">
    <path fill="dodgerblue" fill-rule="nonzero" stroke="dodgerblue" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-dodgerblue">
    <polygon fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-dodgerblue">
    <path fill="dodgerblue" fill-rule="nonzero" stroke="dodgerblue" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-dodgerblue">
   <path fill="dodgerblue" stroke="dodgerblue" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-dodgerblue">
    <path fill="dodgerblue" fill-rule="nonzero" stroke="dodgerblue" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-dodgerblue">
   <path fill="dodgerblue" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-darkturquoise">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="darkturquoise" stroke="darkturquoise" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-darkturquoise">
    <circle fill="darkturquoise" stroke="darkturquoise" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-darkturquoise">
    <path fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-darkturquoise">
    <polygon fill="darkturquoise" stroke="darkturquoise" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-darkturquoise">
    <path fill="darkturquoise" fill-rule="nonzero" stroke="darkturquoise" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-darkturquoise">
    <polygon fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-darkturquoise">
    <path fill="darkturquoise" fill-rule="nonzero" stroke="darkturquoise" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-darkturquoise">
   <path fill="darkturquoise" stroke="darkturquoise" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-darkturquoise">
    <path fill="darkturquoise" fill-rule="nonzero" stroke="darkturquoise" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-darkturquoise">
   <path fill="darkturquoise" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-mediumslateblue">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="mediumslateblue" stroke="mediumslateblue" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-mediumslateblue">
    <circle fill="mediumslateblue" stroke="mediumslateblue" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-mediumslateblue">
    <path fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-mediumslateblue">
    <polygon fill="mediumslateblue" stroke="mediumslateblue" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-mediumslateblue">
    <path fill="mediumslateblue" fill-rule="nonzero" stroke="mediumslateblue" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-mediumslateblue">
    <polygon fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-mediumslateblue">
    <path fill="mediumslateblue" fill-rule="nonzero" stroke="mediumslateblue" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-mediumslateblue">
   <path fill="mediumslateblue" stroke="mediumslateblue" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-mediumslateblue">
    <path fill="mediumslateblue" fill-rule="nonzero" stroke="mediumslateblue" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-mediumslateblue">
   <path fill="mediumslateblue" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-mediumorchid">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="mediumorchid" stroke="mediumorchid" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-mediumorchid">
    <circle fill="mediumorchid" stroke="mediumorchid" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-mediumorchid">
    <path fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-mediumorchid">
    <polygon fill="mediumorchid" stroke="mediumorchid" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-mediumorchid">
    <path fill="mediumorchid" fill-rule="nonzero" stroke="mediumorchid" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-mediumorchid">
    <polygon fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-mediumorchid">
    <path fill="mediumorchid" fill-rule="nonzero" stroke="mediumorchid" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-mediumorchid">
   <path fill="mediumorchid" stroke="mediumorchid" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-mediumorchid">
    <path fill="mediumorchid" fill-rule="nonzero" stroke="mediumorchid" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-mediumorchid">
   <path fill="mediumorchid" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-rosybrown">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="rosybrown" stroke="rosybrown" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-rosybrown">
    <circle fill="rosybrown" stroke="rosybrown" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-rosybrown">
    <path fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-rosybrown">
    <polygon fill="rosybrown" stroke="rosybrown" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-rosybrown">
    <path fill="rosybrown" fill-rule="nonzero" stroke="rosybrown" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-rosybrown">
    <polygon fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-rosybrown">
    <path fill="rosybrown" fill-rule="nonzero" stroke="rosybrown" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-rosybrown">
   <path fill="rosybrown" stroke="rosybrown" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-rosybrown">
    <path fill="rosybrown" fill-rule="nonzero" stroke="rosybrown" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-rosybrown">
   <path fill="rosybrown" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-coral">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="coral" stroke="coral" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-coral">
    <circle fill="coral" stroke="coral" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-coral">
    <path fill="coral" stroke="coral" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="coral" stroke="coral" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="coral" stroke="coral" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="coral" stroke="coral" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-coral">
    <polygon fill="coral" stroke="coral" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-coral">
    <path fill="coral" fill-rule="nonzero" stroke="coral" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-coral">
    <polygon fill="coral" stroke="coral" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-coral">
    <path fill="coral" fill-rule="nonzero" stroke="coral" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-coral">
   <path fill="coral" stroke="coral" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-coral">
    <path fill="coral" fill-rule="nonzero" stroke="coral" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-coral">
   <path fill="coral" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="square-deeppink">
    <rect fill="none" width="1031" height="1031"/>
    <rect fill="deeppink" stroke="deeppink" stroke-width="4.8588" x="259" y="259" width="513" height="513"/>
  </g>
  <g id="circle-deeppink">
    <circle fill="deeppink" stroke="deeppink" stroke-width="4.8588" cx="2048" cy="516" r="283"/>
    <rect fill="none" x="1533" width="1031" height="1031"/>
  </g>
  <g id="pinwheel-deeppink">
    <path fill="deeppink" stroke="deeppink" stroke-width="12.7527" d="M3581 354l0 162c-70,0 -127,-73 -127,-162 0,-90 57,-162 127,-162l0 162z"/>
    <path fill="deeppink" stroke="deeppink" stroke-width="12.7527" d="M3419 516l162 0c0,69 -73,126 -162,126 -90,0 -162,-57 -162,-126l162 0z"/>
    <path fill="deeppink" stroke="deeppink" stroke-width="12.7527" d="M3581 678l0 -162c69,0 126,72 126,162 0,89 -57,161 -126,161l0 -161z"/>
    <path fill="deeppink" stroke="deeppink" stroke-width="12.7527" d="M3743 516l-162 0c0,-70 72,-127 162,-127 89,0 162,57 162,127l-162 0z"/>
    <rect fill="none" x="3065" width="1031" height="1031"/>
  </g>
  <g id="star-deeppink">
    <polygon fill="deeppink" stroke="deeppink" stroke-width="4.8588" points="5113,207 5189,443 5437,443 5236,588 5313,824 5113,678 4913,824 4990,588 4789,443 5037,443 "/>
    <rect fill="none" x="4598" width="1031" height="1031"/>
  </g>
  <g id="waterdrop-deeppink">
    <path fill="deeppink" fill-rule="nonzero" stroke="deeppink" stroke-width="12.7527" d="M6646 826c-48,-207 -189,-310 -189,-414 0,-103 47,-207 189,-207 141,0 189,104 189,207 0,104 -142,207 -189,414z"/>
    <rect fill="none" x="6130" width="1031" height="1031"/>
  </g>
  <g id="arrow-deeppink">
    <polygon fill="deeppink" stroke="deeppink" stroke-width="12.7527" points="7881,479 7881,479 8178,349 8475,219 8345,516 8215,812 8215,812 8157,537 "/>
    <rect fill="none" x="7663" width="1031" height="1031"/>
  </g>
  <g id="tag-deeppink">
    <path fill="deeppink" fill-rule="nonzero" stroke="deeppink" stroke-width="12.7527" d="M9555 812l0 -445 156 -148 155 148 0 445 -155 -148 -156 148zm0 -222m78 -297m156 0m77 297m-77 148m-156 0"/>
    <rect fill="none" x="9195" width="1031" height="1031"/>
  </g>
  <g id="wifi-deeppink">
   <path fill="deeppink" stroke="deeppink" stroke-width="12.7527" d="M11243 745l-88 -89c23,-23 55,-36 88,-36 33,0 65,13 89,36l-89 89zm207 -207l-59 59c-39,-39 -92,-61 -148,-61 -55,0 -108,22 -147,61l-59 -59c55,-54 129,-85 206,-85 78,0 152,31 207,85zm117 -117l-60 59c-70,-70 -165,-109 -264,-109 -99,0 -194,39 -264,109l-60 -59c86,-86 203,-134 324,-134 122,0 238,48 324,134z"/>
   <rect fill="none" x="10728" width="1031" height="1031"/>
  </g>
  <g id="heart-deeppink">
    <path fill="deeppink" fill-rule="nonzero" stroke="deeppink" stroke-width="12.7527" d="M12776 330c37,-74 74,-111 148,-111 82,0 149,66 149,148 0,149 -149,297 -297,445 -149,-148 -297,-296 -297,-445 0,-82 66,-148 148,-148 75,0 112,37 149,111z"/>
    <rect fill="none" x="12260" width="1031" height="1031"/>
  </g>
  <g id="knot-deeppink">
   <path fill="deeppink" d="M14496 473l-63 0 0 85 63 0c82,0 148,66 148,147 0,82 -66,148 -148,148 -81,0 -147,-66 -147,-148l0 -63 -85 0 0 63c0,82 -66,148 -147,148 -82,0 -148,-66 -148,-148 0,-81 66,-147 148,-147l63 0 0 -85 -63 0c-82,0 -148,-66 -148,-147 0,-82 66,-148 148,-148 81,0 147,66 147,148l0 63 85 0 0 -63c0,-82 66,-148 147,-148 82,0 148,66 148,148 0,81 -66,147 -148,147l0 0zm-316 -147c0,-35 -28,-63 -63,-63 -35,0 -63,28 -63,63 0,35 28,63 63,63l63 0 0 -63zm0 358l0 -42 -63 0c-35,0 -63,28 -63,63 0,35 28,64 63,64 35,0 63,-29 63,-64l0 -21zm253 21c0,35 29,64 63,64 35,0 64,-29 64,-64 0,-35 -29,-63 -64,-63l-63 0 0 63zm-84 -232l-85 0 0 85 85 0 0 -85zm147 -210c-34,0 -63,28 -63,63l0 63 63 0c35,0 64,-28 64,-63 0,-35 -29,-63 -64,-63z"/>
   <rect fill="none" x="13785" width="1031" height="1031"/>
  </g>
  <g id="windows">
    <polygon fill="#1C75BC" points="475,2479 999,2475 1001,2015 476,2092 "/>
    <polygon fill="#1C75BC" points="477,2915 1001,2986 999,2529 476,2528 "/>
    <polygon fill="#1C75BC" points="426,2907 427,2524 31,2523 41,2855 "/>
    <polygon fill="#1C75BC" points="430,2480 428,2101 31,2153 30,2483 "/>
    <rect fill="none" y="1985" width="1031" height="1031"/>
  </g>
  <g id="apple">
    <path fill="gray" d="M2440 2726c-64,-44 -113,-80 -124,-175 -4,-42 9,-91 26,-123 9,-16 20,-29 32,-42 18,-20 31,-25 39,-46 -16,-31 -60,-64 -96,-76 -43,-14 -97,-24 -148,-7 -37,11 -92,42 -137,31 -49,-12 -81,-37 -140,-36 -98,1 -176,72 -207,143 -102,232 91,585 220,591 43,2 89,-29 137,-36 114,-18 175,90 271,-2 41,-40 126,-162 127,-222z"/>
    <path fill="gray" d="M2052 2236c117,1 195,-104 191,-221 -118,13 -192,100 -191,221z"/>
    <rect fill="none" x="1533" y="1985" width="1031" height="1031"/>
  </g>
  <g id="android">
    <path fill="#97C52D" d="M3844 2323c-19,-3 -165,-1 -198,-1l-324 0c-9,2 -6,9 -6,19l0 349c0,37 -4,60 17,83 24,26 36,21 77,23 11,1 9,3 9,15l0 75c0,29 -7,70 13,86 32,27 93,15 95,-28l0 -142c3,-11 34,-6 53,-6l48 1c5,1 1,-1 4,1 5,8 2,103 2,121 0,23 -3,36 11,50 20,23 65,24 87,-1 13,-14 10,-28 10,-51 -1,-20 -4,-108 1,-119 18,-7 40,4 65,-9 17,-8 27,-19 34,-39 7,-22 3,-109 3,-140 0,-22 2,-273 -1,-287z"/>
    <path fill="#97C52D" d="M3488 2198l0 1c-15,30 -73,-5 -43,-38 20,-23 58,-3 50,29 -2,7 -2,6 -7,8zm193 -44c32,-18 61,35 26,53 -33,16 -64,-31 -26,-53zm-242 -74c3,1 1,-3 5,4 2,2 2,4 3,8l-40 23c-21,8 -50,48 -62,67 -23,37 -25,67 -30,103 49,2 517,3 528,-1 4,-7 2,-10 1,-18 -1,-7 -2,-13 -3,-19 -6,-27 -21,-61 -37,-83 -35,-50 -78,-63 -90,-72 2,-7 17,-29 22,-36 2,-3 34,-44 10,-41 -5,0 -3,0 -6,3 -1,1 -5,7 -6,9 -6,8 -8,12 -13,19 -30,45 -20,41 -54,32 -31,-8 -56,-9 -88,-9 -73,0 -96,15 -114,15l-17 -27c-7,-6 -13,-18 -18,-25 -7,-9 -11,-22 -23,-15 -10,11 27,46 32,63z"/>
    <path fill="#97C52D" d="M3201 2328c-17,8 -22,7 -31,26 -6,14 -4,35 -4,52 0,41 -2,200 1,226 5,51 84,55 104,16 6,-11 4,-41 4,-56 0,-62 2,-169 0,-226 -1,-12 -3,-15 -9,-21 -4,-5 -9,-11 -15,-13 -11,-5 -36,-11 -50,-4z"/>
    <path fill="#97C52D" d="M3978 2334c-4,0 -8,0 -10,-4 -37,-6 -67,-5 -79,27 -4,10 -2,193 -2,220 0,50 -8,68 23,89 16,12 62,13 80,-17 9,-15 5,-83 5,-106l0 -167c-1,-33 -11,-29 -17,-42z"/>
    <rect fill="none" x="3065" y="1985" width="1031" height="1031"/>
  </g>
  <g id="linux">
    <path fill="#E8EDF2" d="M5144 2343c-4,6 -7,6 -14,10 -24,11 -29,12 -58,11 -19,-1 -20,-5 -31,-15 -10,-9 -12,-8 -21,-20 -4,2 -4,1 -6,4 -4,115 -63,141 -92,219 -5,14 -9,25 -12,40 -2,6 -3,15 -4,22 -3,27 -2,18 -7,20 -17,-33 18,-113 20,-133 -16,21 -35,65 -40,95 -10,56 12,71 38,96 23,21 48,41 76,62 12,10 29,22 33,35 8,28 -13,30 -29,39 12,28 80,50 126,46 50,-5 85,-30 120,-52 15,-22 6,-70 8,-97 2,-25 18,-47 41,-37 10,4 7,10 16,6 6,-6 12,-9 22,-11 20,-3 10,1 23,-8 1,-18 30,-69 15,-123 -10,-35 -42,-64 -66,-77 4,6 22,21 33,38 24,36 25,76 7,115l-19 33c-6,9 -3,7 -14,11 7,-83 -6,-131 -38,-199 -13,-28 -23,-53 -35,-83 -10,-26 -21,-61 -32,-84 -6,2 -7,6 -14,12 -4,3 -9,7 -14,11 -6,3 -27,13 -32,14z"/>
    <path fill="#262F34" d="M4838 2602c-4,25 -9,32 -9,62 25,-3 31,-4 47,11 26,26 51,67 70,98 12,21 28,48 44,70 37,51 23,41 28,83 21,-6 82,-2 109,-4 41,-2 68,1 107,4 4,-7 3,-41 5,-52 2,-9 10,-43 4,-52 -35,22 -70,47 -120,52 -46,4 -114,-18 -126,-46 16,-9 37,-11 29,-39 -4,-13 -21,-25 -33,-35 -28,-21 -53,-41 -76,-62 -26,-25 -48,-40 -38,-96 5,-30 24,-74 40,-95 -2,20 -37,100 -20,133 5,-2 4,7 7,-20 1,-7 2,-16 4,-22 3,-15 7,-26 12,-40 29,-78 88,-104 92,-219 -3,-15 -5,-10 -14,-21 -24,-31 20,-41 17,-58 -24,-19 -17,-72 3,-89 17,-15 33,-3 41,9 9,13 11,32 9,51 1,5 0,3 2,6 5,-1 11,-1 16,-1l8 3c0,-1 1,0 1,1l17 1 1 -4c-6,-23 -2,-47 9,-63 10,-16 30,-28 50,-17 35,19 37,80 12,105 1,10 -1,4 7,9 7,4 4,0 11,8l0 33c11,23 22,58 32,84 12,30 22,55 35,83 32,68 45,116 38,199 11,-4 8,-2 14,-11l19 -33c18,-39 17,-79 -7,-115 -11,-17 -29,-32 -33,-38 24,13 56,42 66,77 15,54 -14,105 -15,123 -1,9 0,5 1,7 15,3 46,5 44,23l9 -3 32 -7c23,-82 8,-126 -25,-193 -27,-55 -64,-95 -94,-145 -21,-33 -43,-89 -46,-132 -2,-37 14,-76 -29,-147 -52,-84 -197,-81 -243,-12 -35,52 -21,142 -17,209 7,95 -12,84 -68,164 -18,27 -29,48 -43,77 -18,36 -25,62 -36,86z"/>
    <path fill="#F9BE14" d="M5018 2926c-5,-42 9,-32 -28,-83 -16,-22 -32,-49 -44,-70 -19,-31 -44,-72 -70,-98 -16,-15 -22,-14 -47,-11 0,-30 5,-37 9,-62 -5,15 -9,29 -9,48 -1,31 -2,14 -19,38 -12,17 -25,35 -43,45 -12,7 -57,6 -65,24 -9,19 24,64 7,97 -6,11 -18,29 -12,46 8,23 52,31 78,37 44,8 53,11 94,25 24,8 94,46 131,7 4,-5 10,-12 12,-18 3,-9 1,-18 6,-25z"/>
    <path fill="#F9BE14" d="M5243 2822c6,9 -2,43 -4,52 -2,11 -1,45 -5,52 12,21 -4,17 21,41 21,19 78,14 101,-2 13,-8 22,-21 34,-30 31,-24 92,-57 116,-73 16,-11 27,-14 23,-42 -21,-21 -42,-16 -60,-47 -15,-25 -6,-34 -18,-62 -5,-13 -6,-8 -12,-16l-32 7c0,1 -1,2 -1,2l-5 5c-2,3 -2,3 -5,6 -2,3 -5,7 -7,10 -30,44 -69,24 -73,15 0,-9 1,-17 -1,-26 -2,-13 -4,-9 -7,-20 -9,4 -6,-2 -16,-6 -23,-10 -39,12 -41,37 -2,27 7,75 -8,97z"/>
    <path fill="#F6BD0F" d="M5097 2234c-33,7 -12,-9 -59,19l-33 28c-5,7 -2,8 2,12 70,45 84,28 155,-11 9,-5 14,-10 22,-4 -9,15 -64,38 -84,48 -2,1 -2,1 -6,3 -2,0 -7,1 -9,2 -37,5 -34,-5 -50,-2l-3 1c7,13 27,30 48,30 48,-2 32,-14 64,-17 5,-1 26,-11 32,-14 5,-4 10,-8 14,-11 7,-6 8,-10 14,-12l0 -33c-7,-8 -4,-4 -11,-8 -8,-5 -6,1 -7,-9 -4,2 -4,4 -12,1 -7,-3 -5,0 -6,-7 -14,6 -29,-3 -37,-13 -1,1 1,8 -10,0 -5,-3 -2,-1 -6,-6l-1 4 -17 -1z"/>
    <path fill="#E9EBEE" d="M5115 2231c4,5 1,3 6,6 11,8 9,1 10,0 -7,-25 3,-63 32,-50 24,9 23,46 5,63 1,7 -1,4 6,7 8,3 8,1 12,-1 25,-25 23,-86 -12,-105 -20,-11 -40,1 -50,17 -11,16 -15,40 -9,63z"/>
    <path fill="#475659" d="M5308 2694c3,11 5,7 7,20 2,9 1,17 1,26 4,9 43,29 73,-15 2,-3 5,-7 7,-10 3,-3 3,-3 5,-6l5 -5c0,0 1,-1 1,-2l-9 3c2,-18 -29,-20 -44,-23 -1,-2 -2,2 -1,-7 -13,9 -3,5 -23,8 -10,2 -16,5 -22,11z"/>
    <path fill="#E8EDEF" d="M5017 2254c8,3 15,0 13,-9 -13,-18 -18,-45 0,-58 17,-2 21,7 24,21 4,7 3,16 3,25 9,-1 9,-2 13,-8 2,-19 0,-38 -9,-51 -8,-12 -24,-24 -41,-9 -20,17 -27,70 -3,89z"/>
    <path fill="#242323" d="M5131 2237c8,10 23,19 37,13 18,-17 19,-54 -5,-63 -29,-13 -39,25 -32,50z"/>
    <path fill="#E7BC36" d="M5030 2245c2,9 -5,12 -13,9 3,17 -41,27 -17,58 9,11 11,6 14,21 2,-3 2,-2 6,-4 9,12 11,11 21,20 11,10 12,14 31,15 29,1 34,0 58,-11 7,-4 10,-4 14,-10 -32,3 -16,15 -64,17 -21,0 -41,-17 -48,-30l3 -1c16,-3 13,7 50,2 2,-1 7,-2 9,-2 4,-2 4,-2 6,-3 -52,9 -69,0 -97,-32l4 -1c-4,-4 -7,-5 -2,-12l33 -28c47,-28 26,-12 59,-19 0,-1 -1,-2 -1,-1l-8 -3c-5,0 -11,0 -16,1 -2,-3 -1,-1 -2,-6 -4,6 -4,7 -13,8 0,-9 1,-18 -3,-25 5,22 -2,40 -24,37z"/>
    <path fill="#704D30" d="M5007 2293l-4 1c28,32 45,41 97,32 20,-10 75,-33 84,-48 -8,-6 -13,-1 -22,4 -71,39 -85,56 -155,11z"/>
    <path fill="#242323" d="M5030 2245c22,3 29,-15 24,-37 -3,-14 -7,-23 -24,-21 -18,13 -13,40 0,58z"/>
    <rect fill="none" x="4597" y="1985" width="1031" height="1031"/>
  </g>
  <g id="ubuntu">
    <circle fill="#DC4A29" cx="6645" cy="2501" r="486"/>
    <path fill="#FEFEFE" d="M6489 2734l54 -81c-51,-34 -81,-91 -81,-153 0,-61 30,-119 81,-153l-54 -81c-52,35 -90,85 -109,142 24,23 40,56 40,92 0,37 -16,69 -40,92 19,57 57,108 109,142z"/>
    <circle fill="#FEFEFE" cx="6308" cy="2500" r="66"/>
    <circle fill="#FEFEFE" transform="matrix(-0.142584 -0.246962 0.246962 -0.142584 6834.53 2796.45)" r="231"/>
    <circle fill="#FEFEFE" transform="matrix(-0.142584 0.246962 -0.246962 -0.142584 6826.44 2204.77)" r="231"/>
    <path fill="#FEFEFE" d="M6943 2518l-99 1c-4,61 -38,116 -92,147 -53,31 -119,33 -175,6l-50 84c56,27 128,39 187,27 8,-33 28,-63 60,-81 31,-18 67,-21 99,-11 40,-46 66,-111 70,-173z"/>
    <path fill="#FEFEFE" d="M6525 2246l51 85c56,-27 120,-25 174,6 53,30 88,86 92,148l99 -3c-5,-62 -31,-125 -71,-171 -32,10 -68,7 -100,-11 -31,-18 -52,-48 -59,-80 -59,-12 -130,-1 -186,26z"/>
    <rect fill="none" x="6129" y="1985" width="1031" height="1031"/>
  </g>
  <g id="debian">
    <path fill="#D02454" d="M8108 2981c11,2 13,5 26,5 11,0 22,1 32,0 2,-2 2,0 0,-4 -29,0 -49,-33 -63,-43l0 7c-19,-5 -28,-15 -37,-20 -5,-3 -9,-4 -14,-6 1,-9 2,-4 3,-13 -15,-3 -26,-18 -35,-28 -6,-8 -7,-4 -17,-5 -20,-24 -26,-41 -26,-40l-8 -4 -31 -43c-7,-12 -7,-9 -3,-20 -7,-3 -12,-10 -15,-16 2,-4 3,-4 4,-8 -7,-13 -22,-29 -23,-46 7,4 5,7 15,9 -2,-12 -16,-36 -24,-42 -13,-9 -20,7 -34,-32 -10,-24 2,-9 -4,-24 -7,-14 -7,-9 -6,-27 2,-22 -10,-19 -12,-55 -2,-43 6,-83 9,-122 4,-56 29,-64 36,-97 0,-1 -3,-3 -3,-3 -2,-3 -1,1 -2,-4 9,-11 17,-22 28,-35 21,-24 57,-60 87,-72 14,-5 18,2 22,-12l-10 4c2,-8 23,-23 33,-28 17,-7 41,-3 50,-14 -5,-3 -9,2 -15,1l-2 0c0,0 -1,-1 -1,-1 6,-4 15,-5 23,-9 7,-3 14,-8 21,-12 34,-20 57,-12 65,-13 25,-4 13,-4 46,-4 97,1 166,43 215,115 19,28 32,55 41,91 2,8 2,21 4,28 6,22 1,76 0,97 0,17 0,-5 4,8 2,10 -7,32 -10,40 -5,14 -3,12 -15,19 -10,5 -9,12 -9,24 -1,13 -20,26 -30,35 -10,8 -26,24 -40,28 -2,4 -3,4 -8,7 -11,7 -15,16 -35,16l2 3c0,0 0,1 1,1l0 2c-9,1 -7,-3 -18,-2 -13,1 -10,12 -59,8l-52 -14c-14,-6 -8,-7 -7,-10 -14,3 -48,-27 -56,-39 -6,-9 -3,-4 -9,-9 -4,-4 -2,-7 -6,-12 -3,-5 -1,0 -5,-6l-14 -32c-2,-7 -1,-10 -3,-16 -3,-10 -9,-32 -8,-44 0,-15 -2,-18 5,-32 4,-10 -2,-6 9,-29 14,-30 12,-18 22,-34 3,-6 6,-6 9,-10 4,-4 2,-5 9,-9l36 -21c12,-5 16,-5 28,-9 24,-7 45,-2 66,-1 -7,-11 -54,-19 -75,-11 -5,2 -9,2 -15,4 -9,2 -14,7 -27,8 -14,2 -36,26 -45,40 -10,18 -14,5 -32,55 -9,25 -11,11 -13,46 -4,80 29,122 73,160 10,9 25,19 32,26 10,10 22,13 41,17 67,14 130,6 187,-31 23,-15 58,-52 63,-52 -4,3 -3,4 -1,8 8,-9 -1,-31 17,-42 2,3 4,7 8,9 2,-4 4,-15 7,-22 3,-7 6,-13 10,-20 8,-17 12,-26 12,-48 4,-8 2,3 5,-7 1,-4 0,-6 1,-10 2,2 2,3 3,4 0,1 1,1 1,2 0,0 0,0 1,1 2,3 -1,0 3,3 0,-12 1,-18 4,-28 3,-7 8,-16 9,-25 -10,-4 -10,2 -12,-13 -1,-12 -2,-13 3,-22 -3,-9 -16,-26 -12,-35 4,4 6,10 10,13 4,-6 -2,-19 -4,-27 -4,-17 2,-5 -7,-28 -8,1 -8,7 -12,-4 -2,-7 -4,-12 -3,-20 5,-1 8,0 11,-1 9,-2 2,-1 8,-4 3,5 7,11 10,18 10,21 20,55 24,61 -1,-22 -17,-43 -16,-79 0,0 2,1 3,1 0,1 0,1 0,1l4 3c-3,-39 -61,-109 -94,-137 -17,-15 -34,-24 -43,-32 -5,0 -2,-1 -4,1 4,5 4,3 5,9 -5,-2 -7,-3 -10,-5l-8 -8c-10,-4 -7,-9 -12,-14 -5,-4 -15,-6 -23,-4 -23,3 -5,11 -41,-6 -14,-7 -67,-26 -81,-28 0,10 -1,5 -2,7 -44,-5 -24,3 -65,2 2,-7 0,0 2,-4 10,-12 -1,8 3,-3 -9,0 -15,-2 -22,-2 -9,-1 -13,1 -21,1 6,-4 13,-7 18,-11 -9,-1 -22,3 -30,4 -28,7 0,-12 -80,25 -28,13 -71,33 -90,53 -3,-1 -1,-2 -6,-5 -5,9 -49,45 -66,65 -15,18 -10,15 -17,26 -5,8 -4,-2 -17,27 -6,14 -11,36 -24,41 -5,2 -5,-2 -10,11 -18,39 -23,52 -36,95 -9,28 -6,13 -4,33 1,14 0,30 0,44 -1,56 -3,113 7,167 30,154 141,303 283,368 10,4 23,8 30,12z"/>
    <path fill="#B42C53" d="M8173 2299c13,-1 18,-6 27,-8 6,-2 10,-2 15,-4 21,-8 68,0 75,11 -21,-1 -42,-6 -66,1 -12,4 -16,4 -28,9l-36 21c-7,4 -5,5 -9,9 -3,4 -6,4 -9,10 -10,16 -8,4 -22,34 -11,23 -5,19 -9,29 -7,14 -5,17 -5,32 -1,12 5,34 8,44 2,6 1,9 3,16l14 32c4,6 2,1 5,6 4,5 2,8 6,12 6,5 3,0 9,9 8,12 42,42 56,39 -1,3 -7,4 7,10l52 14c49,4 46,-7 59,-8 11,-1 9,3 18,2l0 -2c-1,0 -1,-1 -1,-1l-2 -3c20,0 24,-9 35,-16 5,-3 6,-3 8,-7 -3,1 0,0 -1,-1l6 -9c-9,3 -19,18 -34,25 -11,6 -11,4 -24,6 -5,2 -15,6 -24,8 -64,13 -130,-26 -164,-76 -28,-44 -24,-76 -29,-85 -4,-7 0,4 -3,-4 1,-62 28,-107 70,-132 35,-21 80,-28 121,-14 17,5 30,15 36,16 -2,-5 -1,-4 -6,-8 -3,-3 -5,-4 -8,-6 -31,-24 -69,-35 -109,-27 -10,2 -34,8 -41,16z"/>
    <path fill="#A4315B" d="M8312 2668c0,0 -1,-1 -1,-1 -1,1 -1,0 -2,0 -11,-4 -66,5 -102,-20 -22,-16 -5,-8 -20,-12 -17,-4 -52,-37 -55,-39 4,10 14,18 19,25 -16,-6 -40,-44 -51,-49 6,25 58,62 83,75 26,14 94,38 129,21z"/>
    <path fill="#AB2D53" d="M8275 2533l-23 -1c2,6 18,10 23,11 10,2 18,-6 22,-13l-22 3z"/>
    <path fill="#9E3861" d="M8106 2605c0,-6 -26,-48 -28,-49 -7,10 4,20 9,27 5,8 12,18 19,22z"/>
    <path fill="#936978" d="M8348 2507c6,-5 14,-18 14,-28l-9 13c-3,5 -6,8 -14,9 3,-11 8,-11 10,-23 -4,2 -1,0 -4,3 -6,5 -18,23 3,26z"/>
    <path fill="#A4315B" d="M8379 2428c1,-6 2,-14 -1,-21 0,0 -9,-4 -7,10 0,0 2,6 3,6 2,4 1,3 5,5z"/>
    <path fill="#913556" d="M7803 2242c9,-3 13,-9 12,-20l-8 1c-1,9 -2,10 -4,19z"/>
    <path fill="#D7C5CD" d="M8412 2077l8 8c3,2 5,3 10,5 -1,-6 -1,-4 -5,-9 2,-2 -1,-1 4,-1 -2,-3 4,-2 -11,-6 -1,1 0,-1 0,2l-1 3c0,0 0,1 0,1l-5 -3z"/>
    <path fill="#913556" d="M7780 2324c4,-4 4,-4 5,-11 1,-5 2,-10 3,-15 -5,3 -1,-3 -5,3 -4,6 -4,17 -3,23z"/>
    <rect fill="none" x="7662" y="1985" width="1031" height="1031"/>
  </g>
  <g id="centos">
    <polygon fill="#932279" points="9633,2465 9668,2500 9633,2535 9386,2535 9386,2651 9235,2500 9386,2351 9386,2465 9633,2465 "/>
    <polygon fill="#EFA724" points="9745,2424 9710,2459 9675,2424 9675,2177 9559,2177 9710,2026 9859,2177 9745,2177 9745,2424 "/>
    <polygon fill="#262577" points="9786,2536 9751,2501 9786,2466 10034,2466 10034,2350 10184,2501 10034,2650 10034,2536 9786,2536 "/>
    <polygon fill="#9CCD2A" points="9674,2577 9709,2542 9744,2577 9744,2825 9860,2825 9709,2975 9560,2825 9674,2825 9674,2577 "/>
    <polygon fill="#9CCD2A" points="9377,2168 9677,2168 9677,2468 9377,2468 "/>
    <path fill="white" d="M9366 2157l322 0 0 322 -322 0 0 -322zm22 301l279 0 0 -279 -279 0 0 279z"/>
    <polygon fill="#932279" points="9742,2168 10042,2168 10042,2468 9742,2468 "/>
    <path fill="white" d="M9731 2157l322 0 0 322 -322 0 0 -322zm22 301l279 0 0 -279 -279 0 0 279z"/>
    <polygon fill="#EFA724" points="9742,2533 10042,2533 10042,2833 9742,2833 "/>
    <path fill="white" d="M9731 2522l322 0 0 322 -322 0 0 -322zm22 301l279 0 0 -279 -279 0 0 279z"/>
    <polygon fill="#262577" points="9377,2533 9677,2533 9677,2833 9377,2833 "/>
    <path fill="white" d="M9366 2522l322 0 0 322 -322 0 0 -322zm22 301l279 0 0 -279 -279 0 0 279z"/>
    <path fill="white" d="M9452 2728l-228 -227 228 -228 227 228 -227 227 0 0zm-198 -227l198 197 197 -197 -197 -198 -198 198 0 0z"/>
    <path fill="white" d="M9710 2470l-228 -228 228 -227 227 227 -227 228 0 0zm-198 -228l198 198 197 -198 -197 -197 -198 197 0 0z"/>
    <path fill="white" d="M9968 2728l-228 -227 228 -228 228 228 -228 227 0 0zm-198 -227l198 197 197 -197 -197 -198 -198 198 0 0z"/>
    <path fill="white" d="M9710 2986l-228 -227 228 -228 227 228 -227 227 0 0zm-198 -227l198 197 197 -197 -197 -198 -198 198 0 0z"/>
    <rect fill="none" x="9195" y="1985" width="1031" height="1031"/>
  </g>
  <g id="opensuse">
    <path fill="#3AB276" d="M11623 2353c75,-30 108,84 43,109 -71,27 -121,-78 -43,-109zm-133 -77l0 61c-53,-14 -128,-80 -307,-80 -124,0 -193,24 -284,77 -205,119 -165,335 -35,393 84,37 174,15 209,-50 45,-83 -13,-168 -75,-180 -104,-22 -134,86 -85,121 16,11 23,4 27,0 10,-14 5,-13 -3,-26 -36,-62 78,-83 99,-12 15,49 -23,87 -65,97 -99,23 -180,-87 -133,-173 39,-71 160,-91 239,-26 62,51 69,106 98,153 22,34 57,29 116,27l21 -1c-13,-38 -74,-9 -65,-75 44,-2 118,-10 151,19 19,17 22,35 37,55l104 1c-3,-27 -110,-47 -54,-104 16,-8 177,28 230,-42 -49,-3 -90,10 -137,-16 -15,-9 -22,-10 -25,-30 27,6 47,46 174,30 4,-36 -3,-81 -15,-109 -24,-59 -113,-86 -187,-121 -17,-8 -35,-19 -35,11z"/>
    <path fill="#FEFEFE" d="M11674 2437c-18,9 -30,21 -55,5 -12,-9 -23,-27 -16,-47 11,-39 75,-44 80,11 -15,-5 -16,-8 -31,-2l-1 20 20 11c1,-1 2,2 3,2zm-51 -84c-78,31 -28,136 43,109 65,-25 32,-139 -43,-109z"/>
    <path fill="#3AB276" d="M11674 2437c-1,0 -2,-3 -3,-2l-20 -11 1 -20c15,-6 16,-3 31,2 -5,-55 -69,-50 -80,-11 -7,20 4,38 16,47 25,16 37,4 55,-5z"/>
    <rect fill="none" x="10726" y="1985" width="1031" height="1031"/>
  </g>
  <g id="kali">
    <path fill="#567F98" d="M12476 2714l0 31c12,6 647,2 720,2 29,0 57,5 62,-27 3,-15 1,-243 1,-270 0,-202 17,-197 -73,-197l-815 0c-92,0 -82,0 -82,97l0 360c0,35 19,37 52,37 29,0 63,2 92,0l0 -33c-17,0 -97,2 -110,-2l-1 -420c20,-6 600,-2 674,-2 41,0 200,-2 227,1l1 422c-35,4 -607,0 -703,0 -6,0 -42,-1 -45,1z"/>
    <path fill="#527E99" d="M12474 2535l25 -18 76 109 45 0c-4,-12 -34,-53 -46,-69 -6,-10 -42,-59 -45,-69 1,-8 82,-93 88,-108l-43 0c-15,17 -91,111 -99,114l-1 -114 -38 0 -1 246 39 0 0 -91z"/>
    <path fill="#527E99" d="M12755 2432l4 -12c7,5 34,94 34,103l-67 0 29 -91zm-66 194l28 -68c0,-1 83,0 89,0l24 67 42 1 -90 -246 -43 0c-7,9 -89,231 -92,246l42 0z"/>
    <polygon fill="#517C97" points="12901,2625 13044,2626 13044,2592 12942,2592 12941,2381 12901,2380 "/>
    <polygon fill="#527E99" points="13075,2380 13073,2626 13112,2626 13111,2380 "/>
    <rect fill="none" x="12259" y="1985" width="1031" height="1031"/>
  </g>
  <g id="gnome">
    <path fill="#4A86CF" stroke="#00A2E9" stroke-width="4.8588" d="M14563 2471c6,-34 -7,-61 -25,-79 -20,-20 -39,-27 -71,-36 -88,-25 -213,-8 -298,29 -63,27 -127,70 -161,128 -14,24 -24,50 -29,80 -11,71 18,150 57,207 57,86 164,181 291,186 52,2 89,-9 130,-33 31,-18 63,-48 83,-76 8,-12 13,-20 21,-33 3,-6 5,-12 8,-19 1,-3 2,-6 3,-9 3,-8 1,0 4,-6 3,-9 8,-34 6,-47 -3,-14 -15,-22 -27,-27 -30,-12 -62,-8 -92,-1 -94,21 -60,59 -71,89 -19,52 -68,50 -103,25 -59,-41 -61,-83 -8,-129 19,-16 41,-30 62,-44 19,-13 44,-28 66,-41 61,-39 140,-88 154,-164z"/>
    <path fill="#4A86CF" stroke="#4A86CF" stroke-width="4.8588" d="M14601 2016c-21,3 -36,5 -56,14 -15,6 -31,16 -43,27 -31,27 -42,44 -58,82 -14,31 -21,85 -8,119 54,148 300,-107 263,-203 -7,-18 -19,-27 -36,-34 -15,-6 -45,-8 -62,-5z"/>
    <path fill="#4A86CF" stroke="#00A2E9" stroke-width="4.8588" d="M14294 2052c-23,4 -41,13 -54,34 -24,41 -20,105 1,145 8,16 27,42 50,36 60,-15 114,-235 3,-215z"/>
    <path fill="#4A86CF" stroke="#00A2E9" stroke-width="4.8588" d="M14097 2131c-93,24 -40,171 16,185 43,11 49,-64 49,-100 -1,-41 -17,-97 -65,-85z"/>
    <path fill="#4A86CF" stroke="#00A2E9" stroke-width="4.8588" d="M13952 2253c-34,10 -52,49 -34,92 10,25 41,63 68,69 70,18 35,-182 -34,-161z"/>
    <rect fill="none" x="13791" y="1985" width="1031" height="1031"/>
  </g>
  <g id="logo">
    <polygon fill="#E62129" points="57,4026 1018,4026 1018,4987 57,4987 846,4845 846,4171 "/>
    <polygon fill="#E62129" stroke="#E62129" stroke-width="63.7637" points="737,4757 215,4822 215,4193 736,4258 "/>
    <rect fill="none" x="22" y="3990" width="1031" height="1031"/>
  </g>
  <g id="cmd">
    <polygon fill="#B6B6B7" points="1644,4183 2616,4183 2616,4830 1644,4830 "/>
    <polygon fill="#474443" points="1708,4227 2552,4227 2552,4786 1708,4786 "/>
    <polygon fill="#FEFEFE" fill-rule="nonzero" points="2246,4604 2470,4604 2470,4642 2246,4642 "/>
    <polygon fill="#FEFEFE" fill-rule="nonzero" points="2078,4371 2132,4371 2217,4626 2163,4626 "/>
    <polygon fill="#FEFEFE" fill-rule="nonzero" points="2057,4571 2057,4622 2003,4622 2003,4571 "/>
    <polygon fill="#FEFEFE" fill-rule="nonzero" points="2057,4461 2057,4511 2003,4511 2003,4461 "/>
    <path fill="#FEFEFE" fill-rule="nonzero" d="M1966 4483l-75 0 0 -43c0,-12 -1,-20 -2,-23 -2,-3 -5,-5 -11,-5 -6,0 -10,2 -11,6 -2,4 -3,12 -3,24l0 114c0,12 1,20 3,24 1,3 5,5 11,5 5,0 9,-2 11,-5 1,-4 2,-13 2,-26l0 -31 75 0 0 10c0,25 -3,43 -7,54 -4,10 -13,20 -27,28 -15,8 -32,12 -53,12 -22,0 -40,-4 -54,-10 -14,-7 -24,-17 -28,-29 -5,-12 -7,-30 -7,-54l0 -72c0,-17 1,-31 2,-39 1,-9 6,-18 13,-26 7,-8 16,-15 29,-19 12,-5 26,-7 43,-7 21,0 39,3 54,11 14,7 23,16 28,27 4,11 7,28 7,50l0 24z"/>
    <rect fill="none" x="1614" y="3991" width="1031" height="1031"/>
  </g>
  <g id="powershell">
    <path fill="#1470B8" d="M3291 4821l737 0c37,0 39,-34 46,-63 43,-169 85,-338 130,-506 12,-43 10,-60 -47,-60l-713 0c-62,0 -52,8 -68,63l-44 170c-3,15 -8,26 -11,41l-78 300c-3,15 -10,29 2,42 12,14 21,13 46,13z"/>
    <path fill="#FEFEFE" d="M3734 4492c-3,-9 -130,-123 -145,-139 -11,-11 -25,-21 -37,-33 -38,-38 4,-74 45,-65 16,4 30,21 40,31l182 169c10,10 28,22 29,40 1,21 -14,32 -29,40 -17,10 -28,18 -44,27 -61,38 -112,72 -174,109l-88 55c-9,6 -34,22 -47,25 -46,9 -76,-39 -39,-66l220 -137c14,-10 81,-49 87,-56z"/>
    <path fill="#FEFEFE" d="M3719 4739c-73,0 -73,-75 2,-75l166 0c65,0 64,75 2,75l-170 0z"/>
    <rect fill="none" x="3209" y="3991" width="1031" height="1031"/>
  </g>
  <g id="aws">
    <rect fill="#232E3D" x="4688" y="4123" width="972" height="766"/>
    <path fill="#FBFDFC" d="M5285 4505c15,-26 60,-179 71,-217 12,-40 23,-51 -35,-42 -8,5 -42,150 -50,179 -3,11 -3,18 -9,25l-47 -195c-4,-10 0,-4 -6,-8 -10,-4 -34,-4 -44,0 -8,6 -7,10 -10,23l-37 155c-7,29 -2,20 -8,21l-26 -102c-7,-25 -17,-80 -29,-98 -12,-2 -34,-4 -44,1 -3,4 -1,-4 -2,6 -1,7 34,117 36,125 6,18 32,120 43,126 11,5 32,3 45,1 13,-6 44,-176 51,-192 2,-4 1,-3 3,-5 7,32 16,68 25,100 6,25 15,80 27,98l46 -1z"/>
    <path fill="#F39814" d="M5374 4737c63,-21 103,-40 155,-75 8,-5 11,-10 9,-23 -11,-6 -14,-5 -26,0 -145,62 -328,81 -485,52 -85,-15 -170,-45 -245,-82 -9,-5 -38,-23 -45,-21 -20,5 16,30 21,34 62,50 98,69 167,99 67,29 135,39 216,47 72,7 173,-11 233,-31z"/>
    <path fill="#FBFDFC" d="M4931 4393c11,89 -85,92 -104,67 -18,-25 -6,-56 15,-67 22,-11 65,-4 89,0zm-145 -129c-7,44 6,32 31,24 20,-6 36,-10 58,-9 53,1 59,29 57,80 -30,-1 -70,-19 -117,2 -31,14 -51,44 -47,86 8,72 100,88 156,37 2,-2 3,-3 5,-4 1,-1 2,-1 3,-3 0,-1 1,-1 2,-1 4,3 1,0 5,4 2,3 11,24 21,27 13,0 29,-11 35,-20 0,-16 -12,-26 -14,-47 -4,-50 10,-129 -14,-166 -39,-61 -162,-34 -181,-10z"/>
    <path fill="#FBFBFD" d="M5475 4514c104,-1 121,-79 95,-120 -32,-50 -159,-39 -128,-98 9,-18 36,-19 59,-18 20,1 31,4 48,11 27,12 16,-27 16,-28 -3,-7 -10,-10 -19,-14 -92,-34 -176,20 -154,87 19,59 83,53 121,75 27,15 25,46 -3,59 -49,22 -111,-16 -120,-11 -6,3 -4,14 -4,21 0,10 1,13 8,18 18,12 55,18 81,18z"/>
    <path fill="#353228" d="M5538 4639c2,13 -1,18 -9,23 -52,35 -92,54 -155,75 -60,20 -161,38 -233,31 -81,-8 -149,-18 -216,-47 -69,-30 -105,-49 -167,-99 -5,-4 -41,-29 -21,-34 7,-2 36,16 45,21 75,37 160,67 245,82 92,17 218,18 307,0 61,-12 122,-28 178,-52 12,-5 15,-6 26,0zm-126 92c9,-5 18,-4 25,-8 12,-7 8,-5 23,-10 14,-5 51,-28 63,-38 15,-12 0,-4 20,-12 3,-15 1,-23 -3,-34 -25,0 -41,4 -57,13 -13,6 -62,21 -79,25 -6,2 -20,5 -26,7 -17,5 -36,6 -55,11 -70,17 -180,12 -251,5 -59,-5 -70,-15 -113,-22 -21,-3 -35,-14 -51,-16 -14,-2 -69,-23 -74,-28l-44 -22c-7,-3 -1,-2 -6,-3 -26,-6 -36,-31 -56,-15 -18,16 12,28 18,37 13,20 28,22 35,28 30,20 74,54 106,63 16,4 12,7 21,11l39 13c6,2 6,4 11,5 17,6 34,10 51,15 9,3 20,5 28,7 9,3 20,2 29,4 72,15 116,7 187,5 15,0 42,-7 55,-11 10,-4 19,-2 29,-5l75 -25z"/>
    <path fill="#E99C18" d="M5453 4608c27,4 105,-17 120,7 14,26 -26,93 -25,116 39,2 80,-118 69,-146 -10,-23 -94,-19 -135,-3 -12,5 -31,11 -29,26z"/>
    <path fill="#232C38" d="M4931 4393c-24,-4 -67,-11 -89,0 -21,11 -33,42 -15,67 19,25 115,22 104,-67z"/>
    <path fill="#353228" d="M5453 4608c-2,-15 17,-21 29,-26 41,-16 125,-20 135,3 11,28 -30,148 -69,146 -1,-23 39,-90 25,-116 -15,-24 -93,-3 -120,-7zm104 134c4,-4 4,-5 8,-8 14,-14 24,-23 34,-42 4,-7 5,-11 8,-19 3,-9 6,-14 9,-23 5,-17 14,-62 5,-74 -7,-9 -10,-9 -21,-9 -11,-1 -16,-5 -26,-5 -25,-3 -150,7 -124,54 36,6 84,-18 117,6 3,16 1,22 -4,34l-12 44c-4,10 -15,16 -4,41l10 1z"/>
    <rect fill="none" x="4658" y="3991" width="1031" height="1031"/>
  </g>
  <g id="docker">
    <path fill="#036AB3" d="M6919 4644c42,-55 38,-67 62,-110 47,-4 65,-2 100,-24 17,-11 49,-48 51,-72 -50,-42 -112,-24 -131,-28 -5,-31 -14,-46 -30,-65 -12,-13 -34,-36 -53,-42 -6,5 -3,2 -8,9 -2,3 -4,6 -6,9 -51,80 -11,144 -9,159 -34,23 -122,15 -170,15 -185,0 -376,-1 -559,1 -13,36 -2,98 7,133 31,120 119,189 245,209 89,14 194,-3 270,-27 52,-17 104,-46 147,-80 32,-25 59,-54 84,-87z"/>
    <rect fill="#036AB3" x="6237" y="4385" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6357" y="4385" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6476" y="4385" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6596" y="4385" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6716" y="4385" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6357" y="4277" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6476" y="4277" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6596" y="4277" width="95.6016" height="95.602"/>
    <rect fill="#036AB3" x="6596" y="4169" width="95.6016" height="95.602"/>
    <rect fill="none" x="6130" y="3990" width="1031" height="1031"/>
  </g>
  <g id="gitbash">
    <path fill="#80B3FF" d="M7701 4500l0 27 5 9c2,3 3,6 5,9 6,9 32,34 40,42 19,18 37,36 55,54 4,4 9,11 13,15l96 93c9,-3 101,-98 115,-113 19,-19 38,-38 57,-57 19,-19 45,-37 45,-68 0,-16 -6,-32 -16,-43 -25,-27 -56,-57 -84,-85 -10,-10 -19,-19 -29,-29 -8,-9 -82,-84 -87,-83 -2,0 -103,100 -112,110l-83 84c-17,17 -15,30 -20,35z"/>
    <path fill="#FFE681" d="M8176 4998l24 0c2,-2 15,-6 20,-9 13,-7 31,-28 44,-40 12,-13 163,-163 166,-168l-51 -51c-17,-18 -35,-35 -51,-52l-77 -77c-7,-7 -18,-22 -28,-24l0 74c17,14 21,13 32,34 25,48 -9,111 -69,109 -31,-1 -67,-30 -69,-62 -1,-9 -2,-24 1,-33 13,-43 41,-42 44,-51 0,-13 2,-82 -1,-88 -3,-3 -12,4 -13,5 -1,0 -3,1 -5,2l-96 95c-11,11 -109,107 -108,111 0,3 157,158 173,175 10,9 19,19 29,29 20,20 30,15 35,21z"/>
    <path fill="#8DD25F" d="M8674 4527l0 -27c-5,-2 -3,-4 -5,-11 -8,-18 -31,-39 -45,-52 -20,-18 -37,-39 -57,-57 -13,-12 -109,-111 -114,-111 -10,2 -55,54 -66,63l-48 48c-18,20 -23,15 -7,30l34 34c5,2 29,-7 56,5 14,6 26,16 33,29 9,17 14,31 10,52 -16,89 -148,76 -147,-15 0,-31 12,-20 -8,-39 -3,-3 -6,-7 -10,-11 -7,-6 -16,-17 -22,-21 -12,5 -29,26 -36,37 -11,16 -9,41 0,58 8,17 114,117 141,146l36 36c8,8 33,35 37,34 3,-1 50,-49 56,-55 19,-19 38,-37 56,-56l84 -83c19,-20 14,-26 22,-34z"/>
    <path fill="#FF8080" d="M8200 4025l-28 0c-3,6 -4,0 -19,10 -11,8 -96,92 -98,97 4,7 35,35 42,43 4,4 9,10 13,14l42 41c5,4 8,9 14,14 15,-1 28,-8 44,-1 7,3 13,4 19,8 4,3 10,7 13,11 9,8 15,20 19,32 6,20 0,26 -1,44 7,9 29,33 38,35 2,0 130,-126 130,-131 -4,-8 -47,-47 -56,-56 -17,-17 -142,-145 -153,-151l-19 -10z"/>
    <path fill="#FF8080" d="M7943 4245c3,6 143,145 160,162 11,10 44,48 58,48l0 -71c-5,-4 -29,-12 -38,-37 -5,-13 -7,-14 -7,-30 0,-28 4,-18 5,-32l-27 -28c-4,-4 -10,-8 -14,-13 -11,-11 -64,-67 -70,-67 -2,1 -64,61 -67,68z"/>
    <path fill="#FF8080" d="M8222 4446c7,1 26,-21 30,-27 -2,-5 -24,-27 -29,-29 -3,5 -2,46 -1,56z"/>
    <rect fill="none" x="7671" y="3995" width="1031" height="1031"/>
  </g>
  <g id="cygwin">
    <path fill="#373737" d="M9825 4778c-20,9 -328,6 -306,-10 9,-6 301,-12 306,10zm-460 -603l4 -1c4,-10 27,-34 38,-36l50 -21c17,-9 280,-5 320,-3 23,0 247,55 256,64 -52,24 -177,-35 -242,-36 -80,-1 -210,-7 -286,1 -134,23 -150,101 -150,222 0,49 6,372 -3,392 -22,-23 -12,-256 -12,-302 0,-55 -17,-248 25,-280zm-58 79c-12,115 -2,289 -2,411 0,118 12,228 149,263 55,13 341,10 397,-4 56,-13 112,-27 167,-40 45,-10 156,-16 115,-72l-305 -74 -309 -7c-27,-10 -17,-109 -17,-136 0,-51 0,-102 0,-152 0,-177 -28,-157 147,-157 121,0 159,3 276,-27 41,-10 82,-19 123,-29 37,-9 107,-10 94,-56l-6 -9c-72,-16 -144,-34 -216,-51 -105,-26 -133,-26 -242,-26 -141,0 -344,-27 -371,166z"/>
    <path fill="#39EB39" d="M9684 4594l-26 10c-5,6 -14,8 -22,8 1,-7 56,-61 70,-75 9,-10 16,-24 30,-23 1,18 0,40 -13,52 -11,10 -25,23 -39,28zm-89 -199c4,-14 55,18 70,34 5,6 9,8 15,14 27,31 25,27 23,58l-8 1c8,53 -133,104 -97,154l7 2c2,-30 235,-80 269,-86 38,-6 90,-19 127,-31 33,-10 110,-31 143,-36l0 -4c-13,-26 -243,-71 -285,-81 -49,-13 -97,-25 -146,-37 -25,-6 -50,-12 -74,-18 -35,-9 -65,10 -44,30z"/>
    <path fill="#505050" d="M9365 4175c-42,32 -25,225 -25,280 0,46 -10,279 12,302 9,-20 3,-343 3,-392 0,-121 16,-199 150,-222 76,-8 206,-2 286,-1 65,1 190,60 242,36 -9,-9 -233,-64 -256,-64 -40,-2 -303,-6 -320,3 41,0 362,-9 368,15 -9,3 -267,1 -301,1 -145,1 -143,105 -180,126l-6 -2 27 -82z"/>
    <path fill="#29AE29" d="M9605 4658c22,10 239,-54 280,-63 46,-12 93,-24 140,-35 32,-8 120,-13 119,-55 -33,5 -110,26 -143,36 -37,12 -89,25 -127,31 -34,6 -267,56 -269,86z"/>
    <path fill="#7B7B7B" d="M9407 4138c10,-1 20,-8 31,-12 6,-2 22,-5 29,-4 10,2 2,-2 10,4 -6,11 -33,11 -70,40 -38,29 -38,53 -55,57 0,-27 10,-29 17,-49l-4 1 -27 82 6 2c37,-21 35,-125 180,-126 34,0 292,2 301,-1 -6,-24 -327,-15 -368,-15l-50 21z"/>
    <path fill="#7B7B7B" d="M9825 4778c-5,-22 -297,-16 -306,-10 -22,16 286,19 306,10z"/>
    <path fill="#30CA30" d="M9595 4395c1,12 86,89 100,107l8 -1c2,-31 4,-27 -23,-58 -6,-6 -10,-8 -15,-14 -15,-16 -66,-48 -70,-34z"/>
    <path fill="#A3A3A3" d="M9407 4138c-11,2 -34,26 -38,36 -7,20 -17,22 -17,49 17,-4 17,-28 55,-57 37,-29 64,-29 70,-40 -8,-6 0,-2 -10,-4 -7,-1 -23,2 -29,4 -11,4 -21,11 -31,12z"/>
    <path fill="#ABF9AB" d="M9658 4604l26 -10c7,-13 53,-38 42,-61 -1,0 -2,-7 -12,3l-28 29c-9,10 -33,29 -36,39l8 0z"/>
    <path fill="#78F678" d="M9658 4604l-8 0c3,-10 27,-29 36,-39l28 -29c10,-10 11,-3 12,-3 11,23 -35,48 -42,61 14,-5 28,-18 39,-28 13,-12 14,-34 13,-52 -14,-1 -21,13 -30,23 -14,14 -69,68 -70,75 8,0 17,-2 22,-8z"/>
    <rect fill="none" x="9206" y="3995" width="1031" height="1031"/>
  </g>
</svg>
