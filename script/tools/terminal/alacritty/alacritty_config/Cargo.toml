[package]
name = "alacritty_config"
version = "0.2.3-dev"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = "Alacritty configuration abstractions"
homepage = "https://alacritty.org"
repository = "https://github.com/alacritty/alacritty"
edition = "2021"
rust-version = "1.74.0"

[dependencies]
log = { version = "0.4.17", features = ["serde"] }
serde = "1.0.163"
toml = "0.8.2"

[dev-dependencies]
alacritty_config_derive = { version = "0.2.5-dev", path = "../alacritty_config_derive" }
serde = { version = "1.0.163", features = ["derive"] }
