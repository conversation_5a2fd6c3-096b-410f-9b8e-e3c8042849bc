#!/usr/bin/env bash

printf "Fg=Black  Bg=Black           \e[30;40mTEST\e[m\n"
printf "Fg=Black  Bg=White           \e[30;107mTEST\e[m\n"
printf "Fg=Black  Bg=Red             \e[30;41mTEST\e[m\n"
printf "Fg=Black  Bg=BG              \e[30;49mTEST\e[m\n"
printf "Fg=White  Bg=Black           \e[97;40mTEST\e[m\n"
printf "Fg=White  Bg=White           \e[97;107mTEST\e[m\n"
printf "Fg=White  Bg=Red             \e[97;41mTEST\e[m\n"
printf "Fg=White  Bg=BG              \e[97;49mTEST\e[m\n"
printf "Fg=Red    Bg=Black           \e[31;40mTEST\e[m\n"
printf "Fg=Red    Bg=White           \e[31;107mTEST\e[m\n"
printf "Fg=Red    Bg=Red             \e[31;41mTEST\e[m\n"
printf "Fg=Red    Bg=BG              \e[31;49mTEST\e[m\n"
printf "\n"
printf "Fg=Black  Bg=Black  Inverse  \e[7;30;40mTEST\e[m\n"
printf "Fg=Black  Bg=White  Inverse  \e[7;30;107mTEST\e[m\n"
printf "Fg=Black  Bg=Red    Inverse  \e[7;30;41mTEST\e[m\n"
printf "Fg=Black  Bg=BG     Inverse  \e[7;30;49mTEST\e[m\n"
printf "Fg=White  Bg=Black  Inverse  \e[7;97;40mTEST\e[m\n"
printf "Fg=White  Bg=White  Inverse  \e[7;97;107mTEST\e[m\n"
printf "Fg=White  Bg=Red    Inverse  \e[7;97;41mTEST\e[m\n"
printf "Fg=White  Bg=BG     Inverse  \e[7;97;49mTEST\e[m\n"
printf "Fg=Red    Bg=Black  Inverse  \e[7;31;40mTEST\e[m\n"
printf "Fg=Red    Bg=White  Inverse  \e[7;31;107mTEST\e[m\n"
printf "Fg=Red    Bg=Red    Inverse  \e[7;31;41mTEST\e[m\n"
printf "Fg=Red    Bg=BG     Inverse  \e[7;31;49mTEST\e[m\n"
printf "\n"
printf "Fg=Black  Bg=Black  Hidden   \e[8;30;40mTEST\e[m\n"
printf "Fg=Black  Bg=White  Hidden   \e[8;30;107mTEST\e[m\n"
printf "Fg=Black  Bg=Red    Hidden   \e[8;30;41mTEST\e[m\n"
printf "Fg=Black  Bg=BG     Hidden   \e[8;30;49mTEST\e[m\n"
printf "Fg=White  Bg=Black  Hidden   \e[8;97;40mTEST\e[m\n"
printf "Fg=White  Bg=White  Hidden   \e[8;97;107mTEST\e[m\n"
printf "Fg=White  Bg=Red    Hidden   \e[8;97;41mTEST\e[m\n"
printf "Fg=White  Bg=BG     Hidden   \e[8;97;49mTEST\e[m\n"
printf "Fg=Red    Bg=Black  Hidden   \e[8;31;40mTEST\e[m\n"
printf "Fg=Red    Bg=White  Hidden   \e[8;31;107mTEST\e[m\n"
printf "Fg=Red    Bg=Red    Hidden   \e[8;31;41mTEST\e[m\n"
printf "Fg=Red    Bg=BG     Hidden   \e[8;31;49mTEST\e[m\n"
printf "\n"
printf "Fg=Black  Bg=Black  Hid+Inv  \e[7;8;30;40mTEST\e[m\n"
printf "Fg=Black  Bg=White  Hid+Inv  \e[7;8;30;107mTEST\e[m\n"
printf "Fg=Black  Bg=Red    Hid+Inv  \e[7;8;30;41mTEST\e[m\n"
printf "Fg=Black  Bg=BG     Hid+Inv  \e[7;8;30;49mTEST\e[m\n"
printf "Fg=White  Bg=Black  Hid+Inv  \e[7;8;97;40mTEST\e[m\n"
printf "Fg=White  Bg=White  Hid+Inv  \e[7;8;97;107mTEST\e[m\n"
printf "Fg=White  Bg=Red    Hid+Inv  \e[7;8;97;41mTEST\e[m\n"
printf "Fg=White  Bg=BG     Hid+Inv  \e[7;8;97;49mTEST\e[m\n"
printf "Fg=Red    Bg=Black  Hid+Inv  \e[7;8;31;40mTEST\e[m\n"
printf "Fg=Red    Bg=White  Hid+Inv  \e[7;8;31;107mTEST\e[m\n"
printf "Fg=Red    Bg=Red    Hid+Inv  \e[7;8;31;41mTEST\e[m\n"
printf "Fg=Red    Bg=BG     Hid+Inv  \e[7;8;31;49mTEST\e[m\n"
