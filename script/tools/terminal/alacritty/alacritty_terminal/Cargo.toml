[package]
name = "alacritty_terminal"
version = "0.25.1-dev"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
license = "Apache-2.0"
description = "Library for writing terminal emulators"
readme = "../README.md"
homepage = "https://alacritty.org"
repository = "https://github.com/alacritty/alacritty"
edition = "2021"
rust-version = "1.74.0"

[features]
default = ["serde"]
serde = ["dep:serde", "bitflags/serde", "vte/serde"]

[dependencies]
base64 = "0.22.0"
bitflags = "2.4.1"
home = "0.5.5"
libc = "0.2"
log = "0.4"
parking_lot = "0.12.0"
polling = "3.0.0"
regex-automata = "0.4.3"
unicode-width = "0.1"
vte = { version = "0.15.0", default-features = false, features = ["std", "ansi"] }
serde = { version = "1", features = ["derive", "rc"], optional = true }

[target.'cfg(unix)'.dependencies]
rustix-openpty = "0.1.1"
signal-hook = "0.3.10"

[target.'cfg(windows)'.dependencies]
piper = "0.2.1"
miow = "0.6.0"
windows-sys = { version = "0.59.0", features = [
    "Win32_System_Console",
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_LibraryLoader",
    "Win32_System_Threading",
    "Win32_System_WindowsProgramming",
]}

[dev-dependencies]
serde_json = "1.0.0"
