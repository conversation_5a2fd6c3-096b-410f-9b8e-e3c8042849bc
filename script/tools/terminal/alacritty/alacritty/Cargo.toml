[package]
name = "alacritty"
version = "0.16.0-dev"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
license = "Apache-2.0"
description = "A fast, cross-platform, OpenGL terminal emulator"
readme = "README.md"
homepage = "https://alacritty.org"
repository = "https://github.com/alacritty/alacritty"
edition = "2021"
rust-version = "1.74.0"

[dependencies.alacritty_terminal]
path = "../alacritty_terminal"
version = "0.25.1-dev"

[dependencies.alacritty_config_derive]
path = "../alacritty_config_derive"
version = "0.2.5-dev"

[dependencies.alacritty_config]
path = "../alacritty_config"
version = "0.2.3-dev"

[dependencies]
ahash = { version = "0.8.6", features = ["no-rng"] }
bitflags = "2.2.1"
clap = { version = "4.2.7", features = ["derive", "env"] }
copypasta = { version = "0.10.1", default-features = false }
crossfont = "0.8.0"
glutin = { version = "0.32.2", default-features = false, features = ["egl", "wgl"] }
home = "0.5.5"
libc = "0.2"
log = { version = "0.4", features = ["std", "serde"] }
notify = "6.1.1"
parking_lot = "0.12.0"
serde_json = "1"
serde = { version = "1", features = ["derive"] }
serde_yaml = "0.9.25"
tempfile = "3.12.0"
toml = "0.8.2"
toml_edit = "0.22.21"
unicode-width = "0.1"
winit = { version = "0.30.9", default-features = false, features = ["rwh_06", "serde"] }

[build-dependencies]
gl_generator = "0.14.0"

[dev-dependencies]
clap_complete = "4.2.3"

[target.'cfg(not(windows))'.dependencies]
xdg = "2.5.0"

[target.'cfg(not(target_os = "macos"))'.dependencies]
png = { version = "0.17.5", default-features = false, optional = true }

[target.'cfg(target_os = "macos")'.dependencies]
objc2 = "0.5.2"
objc2-foundation = { version = "0.2.2", default-features = false, features = [
    "std",
    "NSString",
    "NSLocale",
] }
objc2-app-kit = { version = "0.2.2", default-features = false, features = [
    "std",
    "NSColorSpace",
    "NSResponder",
    "NSView",
    "NSWindow",
] }

[target.'cfg(windows)'.dependencies]
dirs = "5.0.1"
windows-sys = { version = "0.52", features = [
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_Threading",
    "Win32_System_Console",
    "Win32_Foundation",
]}

[target.'cfg(windows)'.build-dependencies]
embed-resource = "2.2.0"

[features]
default = ["wayland", "x11"]
x11 = [
    "copypasta/x11",
    "winit/x11",
    "glutin/x11",
    "glutin/glx",
    "png",
]
wayland = [
    "copypasta/wayland",
    "glutin/wayland",
    "winit/wayland",
    "winit/wayland-dlopen",
    "winit/wayland-csd-adwaita-crossfont",
]
nightly = []
