# 网络工具集

这是一个综合的Linux网络管理工具集，包含路由管理和NAT网关两大核心功能。

## 🚀 主要组件

### 1. 智能NAT网关系统 🆕
全新的智能NAT网关解决方案，为局域网设备提供网络地址转换服务。
- **核心功能**: 智能NAT规则管理、外网接口自动/手动切换
- **高级特性**: 实时监控、性能测试、故障转移、配置备份
- **适用场景**: 多网卡环境、网络故障恢复、性能优化

### 2. 路由优先级管理系统
用于管理Linux系统静态路由表优先级(metric)的综合工具。
- **核心功能**: 路由优先级动态调整、网络接口自动优化
- **高级特性**: 路由状态监控、智能路径选择、服务管理
- **适用场景**: 多网卡路由优化、网络性能调优、路由故障排除

---

## 📁 目录结构

```
script/tools/network/
├── nat-gateway/                 # 🆕 智能NAT网关系统
│   ├── nat-gateway.sh          # 主控制脚本
│   ├── nat-service.sh          # NAT服务核心脚本 (优化版)
│   ├── wan-manager.sh          # 外网接口管理器 🆕
│   ├── install.sh              # 系统安装脚本
│   ├── config.sh               # 配置管理脚本
│   ├── monitor.sh              # 实时监控脚本
│   ├── test.sh                 # 功能测试脚本
│   ├── backup.sh               # 备份恢复脚本
│   ├── quick-install.sh        # 快速安装脚本
│   ├── demo-wan-switch.sh      # 外网切换功能演示脚本 🆕
│   └── README.md               # NAT网关详细文档
├── route-manager/               # 🆕 路由优先级管理系统
│   ├── route-priority-manager.sh  # 路由优先级管理器
│   ├── install-route-manager.sh   # 路由管理器安装脚本
│   ├── manage-route-service.sh    # 路由服务管理脚本
│   ├── route-config-example.txt   # 路由配置示例
│   ├── test-cleanup-routes.sh     # 路由清理测试脚本
│   ├── test-priority-logic.sh     # 优先级逻辑测试脚本
│   ├── test-route-fix.sh          # 路由修复测试脚本
│   ├── test-route-manager.sh      # 路由管理器测试脚本
│   ├── test-quick-install.sh      # 快速安装测试脚本
│   ├── ROUTE_LOGIC_FIX.md         # 路由逻辑修复文档
│   └── README.md                  # 路由管理详细文档
├── BUGFIX_SUMMARY.md           # 问题修复总结
├── INSTALLATION_GUIDE.md       # 安装指南
├── install-nat-gateway.sh      # NAT网关安装脚本
├── quick-install.sh            # 快速安装脚本
└── README.md                   # 本文档
```

---

## 🆕 NAT网关系统亮点

### 核心优化
- **智能检测**: 自动识别外网接口和内网网段
- **灵活配置**: 支持自动和手动配置模式
- **安全可靠**: 完善的错误处理和规则验证
- **系统集成**: 完整的systemd服务支持

### 功能特性
- 🔍 **自动网络检测**: 智能识别网络拓扑
- 🛡️ **安全NAT规则**: 自动配置MASQUERADE和FORWARD规则
- 📊 **实时监控**: 详细的状态监控和性能统计
- 💾 **备份恢复**: 完整的配置备份和恢复功能
- 🧪 **全面测试**: 内置完整的功能测试套件
- ⚙️ **配置管理**: 灵活的配置文件管理
- 🎨 **友好界面**: 彩色输出和清晰的状态显示

### 使用场景
- 家庭/办公室网关设备
- 虚拟机网络共享
- 容器网络代理
- 开发测试环境

---

## 🚀 快速开始

### NAT网关系统
```bash
# 进入NAT网关目录
cd nat-gateway

# 查看帮助
./nat-gateway.sh help

# 快速安装
sudo ./quick-install.sh

# 或者分步安装和使用
sudo ./install.sh install
sudo ./nat-gateway.sh start

# 外网接口管理
sudo ./nat-gateway.sh list-wan      # 列出外网接口
sudo ./nat-gateway.sh auto-wan      # 自动选择最佳接口
sudo ./nat-gateway.sh switch-wan    # 手动切换接口

# 监控和测试
./nat-gateway.sh monitor            # 实时监控
./nat-gateway.sh test               # 功能测试
```

### 路由优先级管理系统
```bash
# 进入路由管理器目录
cd route-manager

# 查看帮助
./route-priority-manager.sh help

# 显示当前路由表
./route-priority-manager.sh show

# 修改路由优先级
sudo ./route-priority-manager.sh modify default 100

# 自动选择最佳网卡
sudo ./route-priority-manager.sh auto

# 安装系统服务
sudo ./install-route-manager.sh install

# 服务管理
sudo ./manage-route-service.sh start
sudo ./manage-route-service.sh status
```

---

## 📖 详细文档

### NAT网关系统
详细使用说明请查看: [nat-gateway/README.md](nat-gateway/README.md)
- 完整的功能介绍和使用指南
- 外网接口切换详细说明
- 配置管理和故障排除
- 演示脚本和使用示例

### 路由优先级管理系统
详细使用说明请查看: [route-manager/README.md](route-manager/README.md)
- 路由优先级管理详细说明
- 自动优化算法介绍
- 测试脚本使用指南
- 故障排除和恢复方法

## 🎯 路由管理功能特性

- 🔍 **查看路由表**: 支持表格和简化两种显示格式
- 🔧 **修改路由优先级**: 单个或批量修改路由的metric值
- 🔎 **路由查找**: 快速查找特定网络或接口的路由
- 🏆 **网卡优先级管理**: 设置特定网卡为最高优先级
- 🤖 **智能网卡选择**: 自动选择最佳网卡并设置最高优先级
- 📊 **优先级排名**: 显示所有网络接口的优先级排名
- 💾 **自动备份**: 修改前自动备份当前路由表
- 📝 **详细日志**: 记录所有操作到日志文件
- 🎨 **彩色输出**: 使用颜色区分不同类型的信息
- ⚙️ **批量处理**: 支持配置文件批量修改路由

## 📁 文件结构

```
script/tools/network/
├── route-priority-manager.sh    # 主脚本
├── route-config-example.txt     # 配置文件示例
├── test-route-manager.sh        # 测试脚本
├── install-route-manager.sh     # 系统安装脚本
├── quick-install.sh             # 快速安装脚本
├── manage-route-service.sh      # 服务管理脚本
├── test-cleanup-routes.sh       # 路由清理测试脚本
└── README.md                    # 说明文档
```

## 🚀 快速开始

### 🎯 系统安装 (推荐)

#### 快速安装
```bash
# 一键安装并配置systemd自启动
sudo ./quick-install.sh
```

#### 完整安装
```bash
# 完整安装向导
sudo ./install-route-manager.sh

# 卸载系统
sudo ./install-route-manager.sh --uninstall
```

#### 服务管理
```bash
# 查看服务状态
sudo ./manage-route-service.sh status

# 启动/停止服务
sudo ./manage-route-service.sh start
sudo ./manage-route-service.sh stop

# 查看日志
sudo ./manage-route-service.sh logs

# 编辑配置
sudo ./manage-route-service.sh config
```

### 基本用法

```bash
# 显示当前路由表
./route-priority-manager.sh show

# 查看帮助信息
./route-priority-manager.sh help

# 查找默认路由
./route-priority-manager.sh find default
```

### 修改路由优先级（需要root权限）

```bash
# 修改默认路由优先级为100
sudo ./route-priority-manager.sh modify default 100

# 修改特定网络路由，指定网关和接口
sudo ./route-priority-manager.sh modify ***********/24 50 *********** eth0

# 备份当前路由表
sudo ./route-priority-manager.sh backup
```

## 📋 命令详解

### 查看路由表
```bash
# 详细表格格式
./route-priority-manager.sh show table

# 简化格式，只显示带优先级的路由
./route-priority-manager.sh show simple
```

### 查找路由
```bash
# 查找默认路由
./route-priority-manager.sh find default

# 查找特定网络
./route-priority-manager.sh find 192.168.1

# 查找特定接口的路由
./route-priority-manager.sh find eth0
```

### 修改单个路由
```bash
# 基本语法
sudo ./route-priority-manager.sh modify <目标> <优先级> [网关] [接口]

# 示例
sudo ./route-priority-manager.sh modify default 100
sudo ./route-priority-manager.sh modify ***********/24 50 *********** eth0
```

### 批量修改路由
```bash
# 使用配置文件批量修改
sudo ./route-priority-manager.sh batch route-config-example.txt

# 创建示例配置文件
./route-priority-manager.sh sample my-config.txt
```

### 🏆 网卡优先级管理 (新功能)

#### 设置特定网卡为最高优先级
```bash
# 设置eth0为最高优先级网卡
sudo ./route-priority-manager.sh highest eth0

# 设置eth0为最高优先级，自定义基础metric值
sudo ./route-priority-manager.sh highest eth0 5
```

#### 自动选择最佳网卡
```bash
# 自动选择最佳网卡并设置为最高优先级
sudo ./route-priority-manager.sh auto

# 指定优先的接口类型和基础metric
sudo ./route-priority-manager.sh auto "eth,enp,ens" 10
```

#### 显示接口优先级排名
```bash
# 显示所有网络接口的优先级排名
./route-priority-manager.sh ranking
```

## 📄 配置文件格式

配置文件使用管道符(|)分隔字段：

```
# 格式: 目标网络|优先级|网关|接口|注释
default|100|***********|eth0|主要网络连接
************/24|50||eth1|内网高优先级
10.0.0.0/8|150|***********|eth0|VPN网络
```

### 字段说明
- **目标网络**: 可以是`default`、IP段(如`***********/24`)或单个IP
- **优先级**: 0-65535，数值越小优先级越高
- **网关**: 可选，留空则保持原有网关
- **接口**: 可选，留空则保持原有接口
- **注释**: 可选，用于说明

## 🔧 优先级规划建议

| 优先级范围 | 用途 | 示例 |
|-----------|------|------|
| 0-50 | 关键系统路由 | 本地回环、管理网络 |
| 51-100 | 主要业务路由 | 主网关、核心业务网段 |
| 101-200 | 一般业务路由 | 办公网络、服务器网段 |
| 201-300 | 备用和测试路由 | 备用网关、测试网络 |

## 🛠️ 高级功能

### 自动备份
每次修改路由前，脚本会自动创建备份：
```bash
# 备份位置
/etc/network/route-backups/routes_YYYYMMDD_HHMMSS.backup
```

### 日志记录
所有操作都会记录到日志文件：
```bash
# 查看日志
tail -f /var/log/route-priority-manager.log
```

### 测试功能
运行测试脚本验证功能：
```bash
./test-route-manager.sh
```

## 📊 使用场景

### 场景1: 双网卡负载均衡
```bash
# 有线网络优先级高
sudo ./route-priority-manager.sh modify default 100 *********** eth0

# 无线网络作为备用
sudo ./route-priority-manager.sh modify default 200 *********** wlan0
```

### 场景2: VPN网络优化
```bash
# VPN流量通过特定路由
sudo ./route-priority-manager.sh modify 10.0.0.0/8 50 ***********00 tun0

# 普通流量使用默认路由
sudo ./route-priority-manager.sh modify default 100 *********** eth0
```

### 场景3: 内网访问优化
```bash
# 内网高优先级
sudo ./route-priority-manager.sh modify 192.168.0.0/16 30 *********** eth0

# 外网正常优先级
sudo ./route-priority-manager.sh modify default 100 *********** eth0
```

### 🆕 场景4: 智能网卡优先级管理
```bash
# 自动选择最佳网卡（推荐）
sudo ./route-priority-manager.sh auto

# 手动设置有线网卡为最高优先级
sudo ./route-priority-manager.sh highest eth0

# 查看当前网卡优先级排名
./route-priority-manager.sh ranking
```

### 🆕 场景5: 多网卡环境优化
```bash
# 设置有线网卡为最高优先级
sudo ./route-priority-manager.sh highest eth0 10

# 设置无线网卡为备用
sudo ./route-priority-manager.sh modify default 200 *********** wlan0

# 设置移动网络为最后备用
sudo ./route-priority-manager.sh modify default 300 10.0.0.1 wwan0

# 查看优化结果
./route-priority-manager.sh ranking
```

## ⚠️ 注意事项

1. **权限要求**: 修改路由需要root权限
2. **备份重要**: 修改前会自动备份，建议保留备份文件
3. **持久化**: 重启后路由可能恢复，需要配置持久化
4. **测试环境**: 建议先在测试环境验证配置
5. **网络中断**: 修改默认路由可能导致短暂网络中断

## 🔄 持久化配置

### Ubuntu/Debian (使用netplan)
编辑 `/etc/netplan/*.yaml`:
```yaml
network:
  version: 2
  ethernets:
    eth0:
      routes:
        - to: default
          via: ***********
          metric: 100
```

### CentOS/RHEL (使用NetworkManager)
```bash
nmcli connection modify eth0 ipv4.route-metric 100
```

### 传统方式 (使用/etc/network/interfaces)
```bash
# 在 /etc/network/interfaces 中添加
up route add default gw *********** dev eth0 metric 100
```

## 🐛 故障排除

### 常见问题

1. **权限不足**
   ```bash
   sudo ./route-priority-manager.sh modify default 100
   ```

2. **路由不存在**
   ```bash
   # 先查看现有路由
   ./route-priority-manager.sh show
   ```

3. **网络中断**
   ```bash
   # 查看备份文件
   ls /etc/network/route-backups/
   ```

### 恢复操作
```bash
# 查看备份
sudo ./route-priority-manager.sh backup

# 手动恢复（需要根据备份文件内容手动操作）
ip route add default via *********** dev eth0 metric 100
```

## 📈 更新日志

- v1.0: 初始版本
  - 基本路由查看和修改功能
  - 自动备份和日志记录
  - 批量配置支持
  - 彩色输出和详细帮助

## 🤝 贡献

欢迎提交问题和改进建议！

---

## 🆕 NAT网关系统更新说明

### 原有问题
原始的 `iptables_wifi.sh` 脚本存在以下问题：
- 硬编码的网络配置
- 缺乏错误处理
- 没有日志记录
- 无法适应不同网络环境
- 缺乏状态监控

### 优化改进
新的NAT网关系统提供了以下改进：

#### 1. 智能化
- **自动网络检测**: 自动识别外网接口和内网网段
- **智能规则配置**: 根据网络环境自动配置NAT规则
- **动态适应**: 支持多种网络拓扑结构

#### 2. 可靠性
- **完善错误处理**: 详细的错误检查和处理机制
- **规则验证**: 添加规则前进行冲突检查
- **安全删除**: 安全地删除和更新iptables规则

#### 3. 可维护性
- **模块化设计**: 功能分离，便于维护和扩展
- **配置文件**: 支持配置文件管理
- **日志记录**: 详细的操作日志

#### 4. 用户体验
- **友好界面**: 彩色输出和清晰的状态显示
- **实时监控**: 详细的状态监控和性能统计
- **完整测试**: 内置功能测试套件

#### 5. 系统集成
- **systemd服务**: 完整的系统服务支持
- **开机自启**: 支持开机自动启动
- **服务管理**: 标准的服务管理命令

### 使用建议

#### 新用户
推荐根据需求选择相应的系统：

**NAT网关系统** (适用于网络共享和多接口管理)：
```bash
cd nat-gateway
sudo ./quick-install.sh
```

**路由管理系统** (适用于路由优化和优先级管理)：
```bash
cd route-manager
sudo ./install-route-manager.sh install
```

#### 现有用户
可以平滑迁移到新的独立系统：

**NAT网关迁移**：
```bash
# 备份现有配置
sudo iptables-save > /tmp/current_rules.backup

# 安装新系统
cd nat-gateway
sudo ./install.sh install

# 启动新服务
sudo ./nat-gateway.sh start

# 测试功能
./nat-gateway.sh test
```

**路由管理迁移**：
```bash
# 备份当前路由
ip route show > /tmp/current_routes.backup

# 安装新系统
cd route-manager
sudo ./install-route-manager.sh install

# 测试功能
./route-priority-manager.sh show
```

### 独立文件夹组织的优势

#### 🎯 模块化设计
- **独立功能**: 每个系统专注于特定的网络管理功能
- **清晰结构**: 文件组织更加清晰，便于维护和使用
- **独立部署**: 可以单独安装和使用任一系统

#### 📦 便于管理
- **版本控制**: 每个系统可以独立进行版本管理
- **功能扩展**: 便于为每个系统添加新功能
- **问题隔离**: 问题排查和修复更加精确

#### 🔧 使用灵活性
- **按需选择**: 用户可以根据需求选择使用特定系统
- **资源优化**: 避免不必要的功能加载
- **学习成本**: 降低单个系统的学习和使用成本

### 兼容性
- 两个系统完全兼容原有的网络配置
- 支持所有主流Linux发行版
- 向后兼容现有的网络规则和配置
- 可以同时使用两个系统而不冲突

---

## 🤝 贡献指南

欢迎贡献代码和建议！

### 贡献方式
1. 提交Issue报告问题
2. 提交Pull Request改进代码
3. 完善文档和示例
4. 分享使用经验

### 开发规范
- 遵循Shell脚本最佳实践
- 添加详细的注释和文档
- 包含相应的测试用例
- 保持代码风格一致

---

## 📄 许可证

MIT License
