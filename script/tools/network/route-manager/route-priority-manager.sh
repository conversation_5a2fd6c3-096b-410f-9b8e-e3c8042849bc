#!/bin/bash

# 静态路由表优先级管理脚本
# 功能：查看、修改、管理Linux系统的静态路由优先级(metric)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_NAME="$(basename "$0")"
LOG_FILE="${HOME}/.route-priority-manager.log"
BACKUP_DIR="${HOME}/.route-backups"
CONFIG_FILE="${HOME}/.route-priority-manager.conf"

# 如果是root用户，使用系统目录
if [ "$EUID" -eq 0 ]; then
    LOG_FILE="/var/log/route-priority-manager.log"
    BACKUP_DIR="/etc/network/route-backups"
    CONFIG_FILE="/etc/route-priority-manager.conf"
fi

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 尝试写入日志文件，如果失败则忽略
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE" 2>/dev/null || true

    case $level in
        "INFO")  echo -e "${BLUE}ℹ $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}⚠ $message${NC}" ;;
        "ERROR") echo -e "${RED}✗ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✓ $message${NC}" ;;
        "DEBUG") echo -e "${CYAN}🔍 $message${NC}" ;;
    esac
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_message "ERROR" "此脚本需要root权限运行"
        echo -e "${YELLOW}请使用: sudo $0 $*${NC}"
        exit 1
    fi
}

# 创建必要的目录
setup_directories() {
    mkdir -p "$BACKUP_DIR"
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"
}

# 备份当前路由表
backup_routes() {
    local backup_file="$BACKUP_DIR/routes_$(date +%Y%m%d_%H%M%S).backup"
    
    log_message "INFO" "备份当前路由表到: $backup_file"
    
    {
        echo "# 路由表备份 - $(date)"
        echo "# 使用 'ip route' 命令的输出"
        echo ""
        ip route show
        echo ""
        echo "# 使用 'route -n' 命令的输出"
        echo ""
        route -n 2>/dev/null || true
    } > "$backup_file"
    
    log_message "SUCCESS" "路由表备份完成"
    echo "$backup_file"
}

# 显示当前路由表
show_routes() {
    local format="${1:-table}"
    
    log_message "INFO" "显示当前路由表"
    
    echo -e "${BLUE}==================== 当前路由表 ====================${NC}"
    
    if [ "$format" = "simple" ]; then
        echo -e "${CYAN}目标网络${NC}\t\t${CYAN}网关${NC}\t\t${CYAN}接口${NC}\t${CYAN}优先级${NC}"
        echo "--------------------------------------------------------"
        ip route show | while read -r line; do
            if [[ $line == *"metric"* ]]; then
                # 解析包含metric的路由
                dest=$(echo "$line" | awk '{print $1}')
                gw=$(echo "$line" | grep -o 'via [^ ]*' | awk '{print $2}' || echo "直连")
                dev=$(echo "$line" | grep -o 'dev [^ ]*' | awk '{print $2}')
                metric=$(echo "$line" | grep -o 'metric [^ ]*' | awk '{print $2}')
                printf "%-20s\t%-15s\t%-10s\t%s\n" "$dest" "$gw" "$dev" "$metric"
            fi
        done
    else
        echo -e "${YELLOW}=== 详细路由信息 ===${NC}"
        ip route show table all | head -20
        
        echo -e "\n${YELLOW}=== 默认路由 ===${NC}"
        ip route show default
        
        echo -e "\n${YELLOW}=== 路由统计 ===${NC}"
        echo "总路由数: $(ip route show | wc -l)"
        echo "默认路由数: $(ip route show default | wc -l)"
        echo "带优先级的路由数: $(ip route show | grep -c metric)"
    fi
}

# 查找特定路由
find_route() {
    local target="$1"
    
    if [ -z "$target" ]; then
        log_message "ERROR" "请指定要查找的目标网络或接口"
        return 1
    fi
    
    log_message "INFO" "查找路由: $target"
    
    echo -e "${BLUE}查找包含 '$target' 的路由:${NC}"
    ip route show | grep -i "$target" --color=always
    
    if [ $? -ne 0 ]; then
        log_message "WARN" "未找到包含 '$target' 的路由"
        return 1
    fi
}

# 修改路由优先级
modify_route_priority() {
    local destination="$1"
    local new_metric="$2"
    local gateway="$3"
    local interface="$4"
    
    if [ -z "$destination" ] || [ -z "$new_metric" ]; then
        log_message "ERROR" "缺少必要参数: 目标网络和新优先级"
        show_help
        return 1
    fi
    
    # 验证metric值
    if ! [[ "$new_metric" =~ ^[0-9]+$ ]] || [ "$new_metric" -lt 0 ] || [ "$new_metric" -gt 65535 ]; then
        log_message "ERROR" "无效的优先级值: $new_metric (应为0-65535之间的整数)"
        return 1
    fi
    
    log_message "INFO" "修改路由优先级: $destination -> metric $new_metric"
    
    # 备份当前路由表
    local backup_file=$(backup_routes)
    
    # 查找现有路由
    local existing_route=$(ip route show "$destination" 2>/dev/null)
    
    if [ -z "$existing_route" ]; then
        log_message "ERROR" "未找到目标路由: $destination"
        return 1
    fi
    
    echo -e "${YELLOW}现有路由:${NC}"
    echo "$existing_route"
    
    # 构建新的路由命令
    local route_cmd="ip route replace $destination"
    
    # 如果指定了网关
    if [ -n "$gateway" ]; then
        route_cmd="$route_cmd via $gateway"
    elif echo "$existing_route" | grep -q "via"; then
        # 保持原有网关
        local old_gw=$(echo "$existing_route" | grep -o 'via [^ ]*' | awk '{print $2}')
        route_cmd="$route_cmd via $old_gw"
    fi
    
    # 如果指定了接口
    if [ -n "$interface" ]; then
        route_cmd="$route_cmd dev $interface"
    elif echo "$existing_route" | grep -q "dev"; then
        # 保持原有接口
        local old_dev=$(echo "$existing_route" | grep -o 'dev [^ ]*' | awk '{print $2}')
        route_cmd="$route_cmd dev $old_dev"
    fi
    
    # 添加新的metric
    route_cmd="$route_cmd metric $new_metric"
    
    echo -e "${CYAN}执行命令: $route_cmd${NC}"
    
    # 执行路由修改
    if eval "$route_cmd"; then
        log_message "SUCCESS" "路由优先级修改成功"
        echo -e "${GREEN}新路由:${NC}"
        ip route show "$destination"
        
        # 保存配置（如果系统支持）
        if command -v netplan >/dev/null 2>&1; then
            log_message "INFO" "检测到netplan，建议手动更新配置文件以持久化更改"
        elif [ -f /etc/network/interfaces ]; then
            log_message "INFO" "检测到传统网络配置，建议手动更新/etc/network/interfaces"
        fi
        
    else
        log_message "ERROR" "路由优先级修改失败"
        echo -e "${RED}恢复备份...${NC}"
        restore_routes "$backup_file"
        return 1
    fi
}

# 清理指定接口的冲突路由
cleanup_interface_routes() {
    local interface="$1"
    local route_type="${2:-default}"  # default, all

    if [ -z "$interface" ]; then
        log_message "ERROR" "请指定要清理的网络接口"
        return 1
    fi

    log_message "INFO" "清理接口 $interface 的 $route_type 路由"

    local cleaned_count=0

    if [ "$route_type" = "default" ] || [ "$route_type" = "all" ]; then
        # 清理默认路由
        local default_routes=$(ip route show default dev "$interface" 2>/dev/null)
        if [ -n "$default_routes" ]; then
            echo -e "${YELLOW}清理默认路由:${NC}"
            while IFS= read -r route; do
                if [ -n "$route" ]; then
                    echo -e "${CYAN}删除: $route${NC}"
                    if ip route del $route 2>/dev/null; then
                        log_message "SUCCESS" "已删除默认路由: $route"
                        ((cleaned_count++))
                    else
                        log_message "WARN" "删除默认路由失败: $route"
                    fi
                fi
            done <<< "$default_routes"
        fi
    fi

    if [ "$route_type" = "all" ]; then
        # 清理所有路由（除了直连路由）
        local all_routes=$(ip route show dev "$interface" | grep -v "proto kernel scope link")
        if [ -n "$all_routes" ]; then
            echo -e "${YELLOW}清理其他路由:${NC}"
            while IFS= read -r route; do
                if [ -n "$route" ] && [[ ! "$route" =~ proto\ kernel\ scope\ link ]]; then
                    echo -e "${CYAN}删除: $route${NC}"
                    local dest=$(echo "$route" | awk '{print $1}')
                    if ip route del "$dest" dev "$interface" 2>/dev/null; then
                        log_message "SUCCESS" "已删除路由: $route"
                        ((cleaned_count++))
                    else
                        log_message "WARN" "删除路由失败: $route"
                    fi
                fi
            done <<< "$all_routes"
        fi
    fi

    echo -e "${GREEN}清理完成，共删除 $cleaned_count 条路由${NC}"
    return 0
}

# 计算最佳的基础metric值
calculate_optimal_metric() {
    local interface="$1"
    local requested_metric="${2:-10}"

    # 获取所有其他接口的默认路由metric值
    local other_metrics=()
    local min_other_metric=999999

    while IFS= read -r route; do
        if [ -n "$route" ] && [[ ! "$route" =~ dev\ $interface ]]; then
            local metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')
            if [ -z "$metric" ]; then
                metric=0  # 没有metric的路由默认为0（最高优先级）
            fi
            other_metrics+=("$metric")
            if [ "$metric" -lt "$min_other_metric" ]; then
                min_other_metric="$metric"
            fi
        fi
    done < <(ip route show default)

    # 如果没有其他默认路由，使用请求的metric
    if [ ${#other_metrics[@]} -eq 0 ]; then
        echo "$requested_metric"
        return 0
    fi

    # 如果请求的metric已经比所有其他metric小，直接使用
    if [ "$requested_metric" -lt "$min_other_metric" ]; then
        echo "$requested_metric"
        return 0
    fi

    # 否则，使用比最小metric更小的值
    local optimal_metric=$((min_other_metric - 10))
    if [ "$optimal_metric" -lt 1 ]; then
        optimal_metric=1
    fi

    echo "$optimal_metric"
}

# 设置特定网卡为最高优先级
set_interface_highest_priority() {
    local interface="$1"
    local requested_metric="${2:-10}"

    if [ -z "$interface" ]; then
        log_message "ERROR" "请指定要设置为最高优先级的网络接口"
        return 1
    fi

    # 检查接口是否存在
    if ! ip link show "$interface" >/dev/null 2>&1; then
        log_message "ERROR" "网络接口不存在: $interface"
        return 1
    fi

    # 检查接口是否启用
    if ! ip link show "$interface" | grep -q "state UP"; then
        log_message "WARN" "网络接口 $interface 未启用，但仍将继续设置"
    fi

    # 计算最佳的metric值
    local base_metric=$(calculate_optimal_metric "$interface" "$requested_metric")

    if [ "$base_metric" != "$requested_metric" ]; then
        log_message "INFO" "调整基础metric: $requested_metric -> $base_metric (确保最高优先级)"
    fi

    log_message "INFO" "设置网卡 $interface 为最高优先级 (基础metric: $base_metric)"

    # 备份当前路由表
    local backup_file=$(backup_routes)

    # 自动调整其他接口的路由优先级，确保目标接口优先级最高
    echo -e "${BLUE}检查并调整其他接口的路由优先级...${NC}"

    # 获取所有默认路由，排除目标接口
    local other_default_routes=$(ip route show default | grep -v "dev $interface")
    local adjusted_count=0

    if [ -n "$other_default_routes" ]; then
        while IFS= read -r route; do
            if [ -n "$route" ]; then
                local other_dev=$(echo "$route" | grep -o 'dev [^ ]*' | awk '{print $2}')
                local other_metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')

                # 如果其他接口没有metric，默认为0（最高优先级）
                if [ -z "$other_metric" ]; then
                    other_metric=0
                fi

                # 如果其他接口的metric小于等于我们设置的基础metric，则需要调整
                if [ "$other_metric" -le "$base_metric" ]; then
                    # 计算新的metric值，确保比目标接口低优先级
                    local new_other_metric=$((base_metric + 100 + adjusted_count * 10))
                    local other_gw=$(echo "$route" | grep -o 'via [^ ]*' | awk '{print $2}')

                    echo -e "${CYAN}调整 $other_dev 的优先级: metric $other_metric -> $new_other_metric${NC}"
                    echo -e "${YELLOW}原因: 确保 $interface (metric $base_metric) 具有最高优先级${NC}"

                    # 使用replace命令直接替换路由，避免"File exists"错误
                    if ip route replace default via "$other_gw" dev "$other_dev" metric "$new_other_metric" 2>/dev/null; then
                        log_message "SUCCESS" "已调整 $other_dev 的默认路由优先级: $other_metric -> $new_other_metric"
                        ((adjusted_count++))
                    else
                        # 如果replace失败，尝试先删除再添加
                        log_message "WARN" "直接替换失败，尝试删除后重新添加..."
                        if ip route del $route 2>/dev/null && \
                           ip route add default via "$other_gw" dev "$other_dev" metric "$new_other_metric" 2>/dev/null; then
                            log_message "SUCCESS" "已调整 $other_dev 的默认路由优先级: $other_metric -> $new_other_metric"
                            ((adjusted_count++))
                        else
                            log_message "ERROR" "调整 $other_dev 的路由优先级失败"
                            # 尝试恢复原路由
                            ip route add $route 2>/dev/null || true
                        fi
                    fi
                else
                    echo -e "${GREEN}✓ $other_dev (metric $other_metric) 优先级已经合适，无需调整${NC}"
                fi
            fi
        done <<< "$other_default_routes"

        if [ $adjusted_count -gt 0 ]; then
            log_message "SUCCESS" "已调整 $adjusted_count 个其他接口的路由优先级"
        else
            log_message "INFO" "其他接口的路由优先级无需调整"
        fi
    else
        log_message "INFO" "没有发现其他接口的默认路由"
    fi

    local success_count=0
    local fail_count=0
    local modified_routes=()

    echo -e "${BLUE}正在分析 $interface 的路由...${NC}"

    # 获取该接口的所有路由
    local interface_routes=$(ip route show dev "$interface" 2>/dev/null)

    if [ -z "$interface_routes" ]; then
        log_message "WARN" "接口 $interface 没有找到任何路由"
        return 1
    fi

    echo -e "${YELLOW}找到以下路由:${NC}"
    echo "$interface_routes" | sed 's/^/  /'

    # 处理默认路由
    local default_routes=$(ip route show default dev "$interface" 2>/dev/null)
    if [ -n "$default_routes" ]; then
        echo -e "\n${BLUE}处理默认路由...${NC}"

        # 首先删除该接口的所有现有默认路由
        echo -e "${YELLOW}删除接口 $interface 的现有默认路由...${NC}"
        while IFS= read -r route; do
            if [ -n "$route" ]; then
                echo -e "${CYAN}删除: $route${NC}"
                if ip route del $route 2>/dev/null; then
                    log_message "SUCCESS" "已删除默认路由: $route"
                else
                    log_message "WARN" "删除默认路由失败或路由不存在: $route"
                fi
            fi
        done <<< "$default_routes"

        # 重新获取网关信息并添加新的高优先级路由
        local gw_info=$(echo "$default_routes" | head -1 | grep -o 'via [^ ]*' | awk '{print $2}')
        if [ -n "$gw_info" ]; then
            echo -e "${CYAN}添加新的高优先级默认路由: via $gw_info dev $interface metric $base_metric${NC}"

            # 使用replace命令避免"File exists"错误
            if ip route replace default via "$gw_info" dev "$interface" metric "$base_metric" 2>/dev/null; then
                log_message "SUCCESS" "默认路由优先级已设置: via $gw_info dev $interface metric $base_metric"
                modified_routes+=("default via $gw_info dev $interface metric $base_metric")
                ((success_count++))
            else
                # 如果replace失败，检查是否已经存在相同的路由
                local existing_route=$(ip route show default via "$gw_info" dev "$interface" metric "$base_metric" 2>/dev/null)
                if [ -n "$existing_route" ]; then
                    log_message "SUCCESS" "默认路由已存在且正确: via $gw_info dev $interface metric $base_metric"
                    modified_routes+=("default via $gw_info dev $interface metric $base_metric")
                    ((success_count++))
                else
                    # 尝试先检查是否有冲突的路由
                    local conflicting_routes=$(ip route show default via "$gw_info" dev "$interface" 2>/dev/null)
                    if [ -n "$conflicting_routes" ]; then
                        log_message "INFO" "发现冲突路由，尝试清理后重新添加..."
                        # 删除所有该接口到该网关的默认路由
                        while IFS= read -r conflict_route; do
                            if [ -n "$conflict_route" ]; then
                                ip route del $conflict_route 2>/dev/null || true
                            fi
                        done <<< "$conflicting_routes"
                    fi

                    # 重新尝试添加
                    if ip route add default via "$gw_info" dev "$interface" metric "$base_metric" 2>/dev/null; then
                        log_message "SUCCESS" "默认路由优先级已设置: via $gw_info dev $interface metric $base_metric"
                        modified_routes+=("default via $gw_info dev $interface metric $base_metric")
                        ((success_count++))
                    else
                        log_message "ERROR" "默认路由添加失败: via $gw_info dev $interface"
                        ((fail_count++))
                    fi
                fi
            fi
        else
            log_message "WARN" "无法获取网关信息，跳过默认路由设置"
        fi
    else
        # 如果该接口没有默认路由，尝试从其他接口获取网关信息
        echo -e "\n${YELLOW}接口 $interface 没有现有默认路由，尝试创建...${NC}"

        # 获取该接口的网络信息
        local interface_ip=$(ip addr show "$interface" | grep -o 'inet [0-9.]*' | awk '{print $2}' | head -1)
        if [ -n "$interface_ip" ]; then
            # 尝试从同网段的其他默认路由推断网关
            local network=$(echo "$interface_ip" | cut -d'.' -f1-3)
            local potential_gw="${network}.1"

            echo -e "${CYAN}尝试使用推断的网关: $potential_gw${NC}"
            if ping -c 1 -W 2 "$potential_gw" >/dev/null 2>&1; then
                # 使用replace命令避免冲突
                if ip route replace default via "$potential_gw" dev "$interface" metric "$base_metric" 2>/dev/null; then
                    log_message "SUCCESS" "创建新的默认路由: via $potential_gw dev $interface metric $base_metric"
                    modified_routes+=("default via $potential_gw dev $interface metric $base_metric")
                    ((success_count++))
                else
                    log_message "ERROR" "创建默认路由失败: via $potential_gw dev $interface"
                    ((fail_count++))
                fi
            else
                log_message "WARN" "推断的网关 $potential_gw 不可达，跳过默认路由创建"
            fi
        fi
    fi

    # 处理网络路由
    local network_routes=$(ip route show dev "$interface" | grep -v "^default")
    if [ -n "$network_routes" ]; then
        echo -e "\n${BLUE}处理网络路由...${NC}"
        local current_metric=$((base_metric + 10))

        while IFS= read -r route; do
            if [ -n "$route" ] && [[ ! "$route" =~ ^default ]]; then
                local dest=$(echo "$route" | awk '{print $1}')
                local current_route_metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')

                # 如果当前metric已经很低，跳过
                if [ -n "$current_route_metric" ] && [ "$current_route_metric" -le "$current_metric" ]; then
                    echo -e "${GREEN}跳过 $dest (当前metric $current_route_metric 已经足够低)${NC}"
                    continue
                fi

                echo -e "${CYAN}修改网络路由: $dest dev $interface metric $current_metric${NC}"

                # 构建路由命令
                local route_cmd="ip route replace $dest dev $interface metric $current_metric"

                # 保持其他属性
                if echo "$route" | grep -q "proto"; then
                    local proto=$(echo "$route" | grep -o 'proto [^ ]*' | awk '{print $2}')
                    route_cmd="$route_cmd proto $proto"
                fi

                if echo "$route" | grep -q "scope"; then
                    local scope=$(echo "$route" | grep -o 'scope [^ ]*' | awk '{print $2}')
                    route_cmd="$route_cmd scope $scope"
                fi

                if eval "$route_cmd"; then
                    log_message "SUCCESS" "网络路由优先级已设置: $dest dev $interface metric $current_metric"
                    modified_routes+=("$dest dev $interface metric $current_metric")
                    ((success_count++))
                else
                    log_message "ERROR" "网络路由修改失败: $dest dev $interface"
                    ((fail_count++))
                fi

                ((current_metric += 5))
            fi
        done <<< "$network_routes"
    fi

    # 显示结果
    echo -e "\n${BLUE}==================== 修改完成统计 ====================${NC}"
    echo -e "${GREEN}成功修改: $success_count 条路由${NC}"
    echo -e "${RED}修改失败: $fail_count 条路由${NC}"

    if [ $success_count -gt 0 ]; then
        echo -e "\n${BLUE}已修改的路由:${NC}"
        for route in "${modified_routes[@]}"; do
            echo -e "  ${GREEN}✓${NC} $route"
        done

        echo -e "\n${BLUE}当前 $interface 的路由状态:${NC}"
        ip route show dev "$interface"

        # 验证并显示默认路由优先级对比
        echo -e "\n${BLUE}验证路由优先级设置:${NC}"
        local target_metric=""
        local is_highest=true
        local all_routes=()

        # 收集所有默认路由信息
        while IFS= read -r route; do
            if [ -n "$route" ]; then
                local metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')
                local dev=$(echo "$route" | grep -o 'dev [^ ]*' | awk '{print $2}')

                # 如果没有metric，默认为0
                if [ -z "$metric" ]; then
                    metric=0
                fi

                all_routes+=("$metric:$dev:$route")

                if [ "$dev" = "$interface" ]; then
                    target_metric="$metric"
                fi
            fi
        done < <(ip route show default)

        # 检查目标接口是否真的是最高优先级
        for route_info in "${all_routes[@]}"; do
            local other_metric=$(echo "$route_info" | cut -d':' -f1)
            local other_dev=$(echo "$route_info" | cut -d':' -f2)
            local other_route=$(echo "$route_info" | cut -d':' -f3-)

            if [ "$other_dev" != "$interface" ] && [ "$other_metric" -le "$target_metric" ]; then
                is_highest=false
            fi
        done

        # 显示结果
        echo -e "${BLUE}所有默认路由优先级对比:${NC}"
        for route_info in "${all_routes[@]}"; do
            local route_metric=$(echo "$route_info" | cut -d':' -f1)
            local route_dev=$(echo "$route_info" | cut -d':' -f2)
            local route_full=$(echo "$route_info" | cut -d':' -f3-)

            if [ "$route_dev" = "$interface" ]; then
                if [ "$is_highest" = "true" ]; then
                    echo -e "  ${GREEN}★ $route_full${NC} (最高优先级 ✓)"
                else
                    echo -e "  ${YELLOW}★ $route_full${NC} (目标接口 ⚠)"
                fi
            else
                echo -e "  ${CYAN}  $route_full${NC}"
            fi
        done

        # 显示验证结果
        if [ "$is_highest" = "true" ]; then
            log_message "SUCCESS" "✓ $interface 已成功设置为最高优先级 (metric: $target_metric)"
        else
            log_message "WARN" "⚠ $interface 可能不是最高优先级，请检查其他路由设置"
        fi
    fi

    if [ $fail_count -gt 0 ]; then
        log_message "WARN" "部分路由修改失败，请检查系统日志"
    fi

    return $fail_count
}

# 自动选择最佳网卡并设置为最高优先级
auto_set_best_interface() {
    local preferred_types="${1:-eth,enp,ens}"  # 优先的接口类型
    local base_metric="${2:-10}"

    log_message "INFO" "自动选择最佳网络接口并设置为最高优先级"

    echo -e "${BLUE}分析可用网络接口...${NC}"

    # 获取所有UP状态的接口
    local available_interfaces=()
    while IFS= read -r line; do
        local iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
        if [ "$iface" != "lo" ] && echo "$line" | grep -q "state UP"; then
            available_interfaces+=("$iface")
        fi
    done < <(ip link show | grep -E '^[0-9]+:')

    if [ ${#available_interfaces[@]} -eq 0 ]; then
        log_message "ERROR" "没有找到可用的网络接口"
        return 1
    fi

    echo -e "${CYAN}可用接口: ${available_interfaces[*]}${NC}"

    # 按优先级排序接口
    local best_interface=""
    local interface_scores=()

    for iface in "${available_interfaces[@]}"; do
        local score=0
        local reason=""

        # 检查接口类型优先级
        IFS=',' read -ra PREFERRED <<< "$preferred_types"
        for i in "${!PREFERRED[@]}"; do
            if [[ "$iface" =~ ^${PREFERRED[$i]} ]]; then
                score=$((score + (10 - i) * 10))
                reason="$reason 类型优先级:+$((10 - i) * 10)"
                break
            fi
        done

        # 检查是否有默认路由
        if ip route show default dev "$iface" >/dev/null 2>&1; then
            score=$((score + 20))
            reason="$reason 有默认路由:+20"
        fi

        # 检查接口速度（如果可获取）
        local speed=$(cat "/sys/class/net/$iface/speed" 2>/dev/null || echo "0")
        if [ "$speed" -gt 1000 ]; then
            score=$((score + 15))
            reason="$reason 高速接口:+15"
        elif [ "$speed" -gt 100 ]; then
            score=$((score + 10))
            reason="$reason 中速接口:+10"
        fi

        # 检查是否为有线接口（通常更稳定）
        if [[ "$iface" =~ ^(eth|enp|ens) ]]; then
            score=$((score + 25))
            reason="$reason 有线接口:+25"
        fi

        # 检查接口状态
        local carrier=$(cat "/sys/class/net/$iface/carrier" 2>/dev/null || echo "0")
        if [ "$carrier" = "1" ]; then
            score=$((score + 10))
            reason="$reason 连接正常:+10"
        fi

        interface_scores+=("$score:$iface:$reason")
        echo -e "  ${YELLOW}$iface${NC}: 得分 $score ($reason )"
    done

    # 找到得分最高的接口
    local max_score=0
    for entry in "${interface_scores[@]}"; do
        local score=$(echo "$entry" | cut -d':' -f1)
        local iface=$(echo "$entry" | cut -d':' -f2)
        if [ "$score" -gt "$max_score" ]; then
            max_score=$score
            best_interface=$iface
        fi
    done

    if [ -z "$best_interface" ]; then
        log_message "ERROR" "无法确定最佳网络接口"
        return 1
    fi

    echo -e "\n${GREEN}选择最佳接口: $best_interface (得分: $max_score)${NC}"

    # 询问用户确认
    echo -e "${YELLOW}是否将 $best_interface 设置为最高优先级? (Y/n)${NC}"
    read -r confirm
    confirm=${confirm:-Y}

    if [[ $confirm =~ ^[Yy]$ ]]; then
        set_interface_highest_priority "$best_interface" "$base_metric"
    else
        log_message "INFO" "用户取消操作"
        return 0
    fi
}

# 显示接口优先级排名
show_interface_ranking() {
    log_message "INFO" "显示网络接口优先级排名"

    echo -e "${BLUE}==================== 网络接口优先级排名 ====================${NC}"

    # 获取所有接口的默认路由metric
    local interface_metrics=()

    while IFS= read -r route; do
        if [[ "$route" =~ ^default ]]; then
            local dev=$(echo "$route" | grep -o 'dev [^ ]*' | awk '{print $2}')
            local metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')
            local gw=$(echo "$route" | grep -o 'via [^ ]*' | awk '{print $2}' || echo "直连")

            if [ -n "$dev" ] && [ -n "$metric" ]; then
                interface_metrics+=("$metric:$dev:$gw")
            fi
        fi
    done < <(ip route show default)

    if [ ${#interface_metrics[@]} -eq 0 ]; then
        echo -e "${YELLOW}没有找到带优先级的默认路由${NC}"
        return 0
    fi

    # 排序并显示
    printf "${CYAN}%-4s %-15s %-8s %-15s %-10s${NC}\n" "排名" "接口" "优先级" "网关" "状态"
    echo "--------------------------------------------------------"

    local rank=1
    for entry in $(printf '%s\n' "${interface_metrics[@]}" | sort -n); do
        local metric=$(echo "$entry" | cut -d':' -f1)
        local dev=$(echo "$entry" | cut -d':' -f2)
        local gw=$(echo "$entry" | cut -d':' -f3)

        # 获取接口状态（检查UP标志而不是state）
        local status="DOWN"
        if ip link show "$dev" 2>/dev/null | grep -q "UP"; then
            status="UP"
        fi

        # 根据排名显示不同颜色
        local color=""
        case $rank in
            1) color="${GREEN}" ;;
            2) color="${YELLOW}" ;;
            *) color="${NC}" ;;
        esac

        printf "${color}%-4s %-15s %-8s %-15s %-10s${NC}\n" "#$rank" "$dev" "$metric" "$gw" "$status"
        ((rank++))
    done

    echo ""
    echo -e "${BLUE}说明:${NC}"
    echo -e "  - 优先级数值越小，路由优先级越高"
    echo -e "  - ${GREEN}绿色${NC}表示最高优先级，${YELLOW}黄色${NC}表示第二优先级"
    echo -e "  - 只显示有默认路由的接口"
}

# 批量修改路由优先级
batch_modify_priority() {
    local config_file="$1"
    
    if [ ! -f "$config_file" ]; then
        log_message "ERROR" "配置文件不存在: $config_file"
        return 1
    fi
    
    log_message "INFO" "批量修改路由优先级，配置文件: $config_file"
    
    local success_count=0
    local fail_count=0
    
    while IFS='|' read -r dest metric gw dev comment; do
        # 跳过注释行和空行
        [[ "$dest" =~ ^#.*$ ]] || [ -z "$dest" ] && continue
        
        echo -e "\n${BLUE}处理路由: $dest${NC}"
        if modify_route_priority "$dest" "$metric" "$gw" "$dev"; then
            ((success_count++))
        else
            ((fail_count++))
        fi
    done < "$config_file"
    
    echo -e "\n${BLUE}批量修改完成:${NC}"
    echo -e "成功: ${GREEN}$success_count${NC}"
    echo -e "失败: ${RED}$fail_count${NC}"
}

# 恢复路由表
restore_routes() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        log_message "ERROR" "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_message "WARN" "恢复路由表功能需要手动操作"
    echo -e "${YELLOW}备份文件位置: $backup_file${NC}"
    echo -e "${YELLOW}请手动检查并恢复路由配置${NC}"
}

# 创建示例配置文件
create_sample_config() {
    local sample_file="$1"
    
    cat > "$sample_file" << 'EOF'
# 路由优先级配置文件
# 格式: 目标网络|优先级|网关|接口|注释
# 示例:

# 默认路由优先级设置
default|100|***********|eth0|主要网络连接
default|200|***********|wlan0|备用无线连接

# 特定网络路由
************/24|50||eth1|内网高优先级
10.0.0.0/8|150|***********|eth0|VPN网络

# 注意：
# - 优先级数值越小，优先级越高
# - 网关和接口可以为空，系统会保持原有设置
# - 以#开头的行为注释行
EOF
    
    log_message "SUCCESS" "示例配置文件已创建: $sample_file"
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}静态路由表优先级管理脚本${NC}

${YELLOW}用法:${NC}
  $SCRIPT_NAME [选项] [参数]

${YELLOW}选项:${NC}
  show [simple|table]              显示当前路由表
  find <目标>                      查找特定路由
  modify <目标> <优先级> [网关] [接口]  修改单个路由优先级
  batch <配置文件>                 批量修改路由优先级
  highest <接口> [基础优先级]       设置指定网卡为最高优先级
  auto [接口类型] [基础优先级]      自动选择最佳网卡并设置最高优先级
  ranking                         显示网络接口优先级排名
  cleanup <接口> [类型]            清理指定接口的路由 (类型: default/all)
  backup                          备份当前路由表
  restore <备份文件>               恢复路由表
  sample <文件名>                  创建示例配置文件
  help                            显示此帮助信息

${YELLOW}参数说明:${NC}
  目标      - 目标网络 (如: default, ***********/24)
  优先级    - 路由优先级 (0-65535, 数值越小优先级越高)
  网关      - 网关地址 (可选)
  接口      - 网络接口 (可选)

${YELLOW}示例:${NC}
  # 显示路由表
  sudo $SCRIPT_NAME show

  # 查找默认路由
  sudo $SCRIPT_NAME find default

  # 修改默认路由优先级为100
  sudo $SCRIPT_NAME modify default 100

  # 设置eth0为最高优先级网卡
  sudo $SCRIPT_NAME highest eth0

  # 设置eth0为最高优先级，基础metric为5
  sudo $SCRIPT_NAME highest eth0 5

  # 自动选择最佳网卡并设置最高优先级
  sudo $SCRIPT_NAME auto

  # 自动选择最佳网卡，优先有线接口
  sudo $SCRIPT_NAME auto "eth,enp,ens" 10

  # 显示接口优先级排名
  sudo $SCRIPT_NAME ranking

  # 清理指定接口的默认路由
  sudo $SCRIPT_NAME cleanup eth0 default

  # 清理指定接口的所有路由
  sudo $SCRIPT_NAME cleanup wlan0 all

  # 修改特定网络路由，指定网关和接口
  sudo $SCRIPT_NAME modify ***********/24 50 *********** eth0

  # 批量修改路由优先级
  sudo $SCRIPT_NAME batch /etc/route-config.txt

  # 备份当前路由表
  sudo $SCRIPT_NAME backup

  # 创建示例配置文件
  sudo $SCRIPT_NAME sample /tmp/route-config-sample.txt

${YELLOW}注意事项:${NC}
  - 此脚本需要root权限运行
  - 修改前会自动备份当前路由表
  - 优先级数值越小，路由优先级越高
  - 建议在测试环境中先验证配置
  - 重启后路由可能恢复，需要配置持久化

${YELLOW}配置文件格式:${NC}
  目标网络|优先级|网关|接口|注释
  default|100|***********|eth0|主要连接
  ************/24|50||eth1|内网高优先级

EOF
}



# 主函数
main() {
    # 检查systemd模式参数
    if [ "$1" = "--systemd-mode" ]; then
        systemd_mode
        exit 0
    fi

    # 检查systemd模式参数
    if [ "$1" = "--systemd-mode" ]; then
        systemd_mode
        exit 0
    fi

    case "$1" in
        show)
            show_routes "$2"
            ;;
        find)
            find_route "$2"
            ;;
        modify)
            check_root
            setup_directories
            modify_route_priority "$2" "$3" "$4" "$5"
            ;;
        batch)
            check_root
            setup_directories
            batch_modify_priority "$2"
            ;;
        highest)
            check_root
            setup_directories
            set_interface_highest_priority "$2" "$3"
            ;;
        auto)
            check_root
            setup_directories
            auto_set_best_interface "$2" "$3"
            ;;
        ranking)
            show_interface_ranking
            ;;
        cleanup)
            check_root
            setup_directories
            cleanup_interface_routes "$2" "$3"
            ;;
        backup)
            check_root
            setup_directories
            backup_routes
            ;;
        restore)
            check_root
            restore_routes "$2"
            ;;
        sample)
            create_sample_config "${2:-/tmp/route-config-sample.txt}"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            if [ $# -eq 0 ]; then
                show_help
            else
                log_message "ERROR" "未知选项: $1"
                echo ""
                show_help
                exit 1
            fi
            ;;
    esac
}

# systemd模式函数
systemd_mode() {
    local config_file="/etc/route-manager/route-manager.conf"
    
    # 加载配置文件
    if [ -f "$config_file" ]; then
        source "$config_file"
    fi
    
    # 检查是否启用自动管理
    if [ "${ENABLE_AUTO_MANAGEMENT:-true}" != "true" ]; then
        log_message "INFO" "自动路由管理已禁用"
        return 0
    fi
    
    log_message "INFO" "systemd模式启动 - 自动配置网卡优先级"
    
    # 如果指定了优先级接口
    if [ -n "${PRIORITY_INTERFACES}" ]; then
        IFS="," read -ra INTERFACES <<< "${PRIORITY_INTERFACES}"
        local current_metric=${BASE_METRIC:-10}
        
        for interface in "${INTERFACES[@]}"; do
            interface=$(echo "$interface" | xargs)  # 去除空格
            if ip link show "$interface" >/dev/null 2>&1; then
                log_message "INFO" "设置 $interface 优先级: metric $current_metric"

                # 使用简单安全的路由设置方法
                # 先清理该接口的现有默认路由
                local existing_routes=$(ip route show default dev "$interface" 2>/dev/null)
                if [ -n "$existing_routes" ]; then
                    while IFS= read -r route; do
                        if [ -n "$route" ]; then
                            ip route del $route 2>/dev/null || true
                        fi
                    done <<< "$existing_routes"
                fi

                # 获取网关信息并添加新路由
                local gateway=""
                local interface_ip=$(ip addr show "$interface" | grep -o 'inet [0-9.]*/[0-9]*' | awk '{print $2}' | head -1)

                if [ -n "$interface_ip" ]; then
                    # 解析IP地址和子网掩码
                    local ip_with_mask="$interface_ip"
                    local ip_addr=$(echo "$ip_with_mask" | cut -d'/' -f1)
                    local subnet_mask=$(echo "$ip_with_mask" | cut -d'/' -f2)

                    if ip link show "$interface" | grep -q "POINTOPOINT"; then
                        # 点对点接口，推断网关
                        if [ "$subnet_mask" = "30" ]; then
                            # /30网络，网关是网络中的另一个IP
                            local network_prefix=$(echo "$ip_addr" | cut -d'.' -f1-3)
                            local last_octet=$(echo "$ip_addr" | cut -d'.' -f4)
                            local network_base=$((last_octet / 4 * 4))
                            if [ $((last_octet % 4)) -eq 1 ]; then
                                gateway="${network_prefix}.$((network_base + 2))"
                            else
                                gateway="${network_prefix}.$((network_base + 1))"
                            fi
                        else
                            # 其他点对点网络，尝试推断网关
                            local network=$(echo "$ip_addr" | cut -d'.' -f1-3)
                            gateway="${network}.1"
                        fi
                    else
                        # 以太网接口，网关通常是 .1
                        local network=$(echo "$ip_addr" | cut -d'.' -f1-3)
                        gateway="${network}.1"
                    fi

                    # 添加路由
                    if [ -n "$gateway" ]; then
                        if ip route add default via "$gateway" dev "$interface" metric "$current_metric" 2>/dev/null; then
                            log_message "SUCCESS" "已添加 $interface 路由: via $gateway metric $current_metric"
                        else
                            log_message "ERROR" "添加 $interface 路由失败: via $gateway metric $current_metric"
                            # 尝试恢复原有路由
                            if [ -n "$existing_routes" ]; then
                                echo "$existing_routes" | while IFS= read -r route; do
                                    if [ -n "$route" ]; then
                                        ip route add $route 2>/dev/null || true
                                    fi
                                done
                            fi
                        fi
                    else
                        if ip route add default dev "$interface" metric "$current_metric" 2>/dev/null; then
                            log_message "SUCCESS" "已添加 $interface 点对点路由: metric $current_metric"
                        else
                            log_message "ERROR" "添加 $interface 点对点路由失败: metric $current_metric"
                            # 尝试恢复原有路由
                            if [ -n "$existing_routes" ]; then
                                echo "$existing_routes" | while IFS= read -r route; do
                                    if [ -n "$route" ]; then
                                        ip route add $route 2>/dev/null || true
                                    fi
                                done
                            fi
                        fi
                    fi
                fi
                current_metric=$((current_metric + 50))
            else
                log_message "WARN" "接口 $interface 不存在，跳过"
            fi
        done
    else
        # 自动选择最佳接口
        log_message "INFO" "自动选择最佳网络接口"
        echo "y" | auto_set_best_interface "${PREFERRED_INTERFACE_TYPES:-eth,enp,ens}" "${BASE_METRIC:-10}" >/dev/null 2>&1
    fi
    
    log_message "SUCCESS" "systemd模式执行完成"
}

# 执行主函数
main "$@"
