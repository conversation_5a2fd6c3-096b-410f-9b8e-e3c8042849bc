# 路由优先级管理器安装指南

## 🎯 推荐使用方式

### 日常管理 (无需安装) ⭐
**推荐**: 直接使用 `unified-route-manager.sh` - 功能最完整，无需安装

```bash
# 直接使用，无需安装
./unified-route-manager.sh show
./unified-route-manager.sh ranking
sudo ./unified-route-manager.sh set wlp1s0 10
sudo ./unified-route-manager.sh cleanup
```

### 自动化服务 (需要安装)
如需要定时自动管理路由，安装 systemd 服务：

```bash
# 快速安装systemd服务
sudo ./quick-install.sh

# 或完整安装向导
sudo ./install-route-manager.sh --install
```

## 📋 系统要求

- Linux 系统 (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- root 权限 (仅路由修改操作需要)
- 网络工具: `ip`, `route`, `ping` (通常已预装)

## 🚀 systemd 服务安装

### 方式一: 快速安装 (推荐)

```bash
sudo ./quick-install.sh
```

选择安装方式：
- **快速安装**: 自动选择最佳网卡，配置systemd自启动
- **自定义安装**: 手动指定优先级网卡
- **仅安装**: 安装但不启用自启动

### 方式二: 完整安装向导

```bash
sudo ./install-route-manager.sh --install
```

提供详细配置选项：
- 自定义网卡优先级设置
- 高级功能配置
- 详细的安装日志

### 方式三: 手动安装

```bash
# 1. 复制主脚本
sudo cp route-priority-manager.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/route-priority-manager.sh

# 2. 创建配置目录
sudo mkdir -p /etc/route-manager

# 3. 复制配置文件
sudo cp route-config-example.txt /etc/route-manager/route-manager.conf

# 4. 安装systemd服务
sudo ./install-route-manager.sh --service-only
```

## 📁 安装后的文件结构

```
系统文件:
├── /usr/local/bin/route-priority-manager.sh    # 主脚本
├── /etc/route-manager/route-manager.conf       # 配置文件
├── /etc/systemd/system/route-priority-manager.service  # systemd服务
├── /etc/systemd/system/route-priority-manager.timer    # systemd定时器
└── /var/log/route-priority-manager.log         # 日志文件

备份目录:
└── /etc/network/route-backups/                 # 路由备份
```

## ⚙️ 配置文件说明

主配置文件: `/etc/route-manager/route-manager.conf`

```bash
# 是否启用自动管理
ENABLE_AUTO_MANAGEMENT=true

# 优先级接口列表 (逗号分隔)
PRIORITY_INTERFACES="wlp1s0,wwan0"

# 基础优先级值
BASE_METRIC=10

# 是否清理其他路由 (建议设为false)
CLEANUP_OTHER_ROUTES=false

# 首选接口类型
PREFERRED_INTERFACE_TYPES="eth,enp,ens,wlp"
```

## 🔧 服务管理

### 启动/停止服务
```bash
# 启动定时器
sudo systemctl start route-priority-manager.timer

# 停止定时器
sudo systemctl stop route-priority-manager.timer

# 启用开机自启
sudo systemctl enable route-priority-manager.timer

# 禁用开机自启
sudo systemctl disable route-priority-manager.timer
```

### 查看服务状态
```bash
# 查看定时器状态
sudo systemctl status route-priority-manager.timer

# 查看服务状态
sudo systemctl status route-priority-manager.service

# 查看服务日志
sudo journalctl -u route-priority-manager.service -f
```

### 手动执行服务
```bash
# 手动执行一次路由优化
sudo systemctl start route-priority-manager.service

# 或直接调用脚本
sudo /usr/local/bin/route-priority-manager.sh --systemd-mode
```

## 🔍 验证安装

### 检查安装状态
```bash
# 检查脚本是否安装
ls -la /usr/local/bin/route-priority-manager.sh

# 检查配置文件
ls -la /etc/route-manager/

# 检查systemd服务
systemctl list-unit-files | grep route-priority-manager
```

### 测试功能
```bash
# 显示当前路由状态
sudo /usr/local/bin/route-priority-manager.sh ranking

# 测试systemd模式
sudo /usr/local/bin/route-priority-manager.sh --systemd-mode

# 检查定时器
systemctl list-timers route-priority-manager.timer
```

## 🛠️ 卸载

### 完全卸载
```bash
# 停止并禁用服务
sudo systemctl stop route-priority-manager.timer
sudo systemctl disable route-priority-manager.timer

# 删除systemd文件
sudo rm -f /etc/systemd/system/route-priority-manager.service
sudo rm -f /etc/systemd/system/route-priority-manager.timer

# 重新加载systemd
sudo systemctl daemon-reload

# 删除脚本和配置
sudo rm -f /usr/local/bin/route-priority-manager.sh
sudo rm -rf /etc/route-manager/

# 删除日志 (可选)
sudo rm -f /var/log/route-priority-manager.log
```

### 使用卸载脚本
```bash
# 如果有卸载脚本
sudo ./manage-route-service.sh uninstall
```

## 🔧 故障排除

### 常见问题

**Q: 服务启动失败？**
```bash
# 查看详细错误信息
sudo journalctl -u route-priority-manager.service -n 20

# 检查脚本权限
ls -la /usr/local/bin/route-priority-manager.sh

# 手动测试脚本
sudo /usr/local/bin/route-priority-manager.sh --systemd-mode
```

**Q: 配置不生效？**
```bash
# 检查配置文件语法
cat /etc/route-manager/route-manager.conf

# 重新加载配置
sudo systemctl restart route-priority-manager.timer
```

**Q: 网络连接中断？**
```bash
# 查看当前路由
ip route show default

# 恢复默认路由 (示例)
sudo ip route add default via *********** dev eth0
```

### 日志分析
```bash
# 查看安装日志
tail -f /var/log/route-priority-manager.log

# 查看systemd日志
sudo journalctl -u route-priority-manager.service --since "1 hour ago"
```

## 💡 使用建议

1. **首次安装**: 建议使用快速安装，然后根据需要调整配置
2. **测试环境**: 先在测试环境验证配置，再部署到生产环境
3. **备份重要**: 修改前备份当前路由配置
4. **监控日志**: 定期检查服务日志确保正常运行
5. **网络变化**: 网络环境变化时重新配置优先级

---

**注意**: 如果只是偶尔需要调整路由优先级，推荐直接使用 `unified-route-manager.sh`，无需安装 systemd 服务。
