#!/bin/bash

# 路由优先级管理器服务管理脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="route-priority-manager"
TIMER_NAME="${SERVICE_NAME}.timer"
CONFIG_FILE="/etc/route-manager/route-manager.conf"
MAIN_SCRIPT="/usr/local/bin/route-priority-manager.sh"

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    
    case $level in
        "INFO")  echo -e "${BLUE}ℹ $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}⚠ $message${NC}" ;;
        "ERROR") echo -e "${RED}✗ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✓ $message${NC}" ;;
    esac
}

# 检查服务是否已安装
check_installation() {
    if [ ! -f "$MAIN_SCRIPT" ] || [ ! -f "$CONFIG_FILE" ]; then
        log_message "ERROR" "路由优先级管理器未安装"
        echo -e "${YELLOW}请先运行安装脚本: sudo ./install-route-manager.sh${NC}"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_message "INFO" "显示服务状态"
    
    echo -e "${BLUE}==================== 服务状态 ====================${NC}"
    
    # 定时器状态
    echo -e "${CYAN}=== 定时器状态 ===${NC}"
    systemctl status "$TIMER_NAME" --no-pager -l
    
    # 服务状态
    echo -e "\n${CYAN}=== 服务状态 ===${NC}"
    systemctl status "$SERVICE_NAME.service" --no-pager -l
    
    # 定时器列表
    echo -e "\n${CYAN}=== 定时器信息 ===${NC}"
    systemctl list-timers "$TIMER_NAME" --no-pager
    
    # 当前路由状态
    echo -e "\n${CYAN}=== 当前路由状态 ===${NC}"
    if [ -x "$MAIN_SCRIPT" ]; then
        "$MAIN_SCRIPT" ranking 2>/dev/null || ip route show default
    else
        ip route show default
    fi
}

# 启动服务
start_service() {
    log_message "INFO" "启动路由优先级管理服务"
    
    if systemctl start "$TIMER_NAME"; then
        log_message "SUCCESS" "服务启动成功"
    else
        log_message "ERROR" "服务启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_message "INFO" "停止路由优先级管理服务"
    
    if systemctl stop "$TIMER_NAME"; then
        log_message "SUCCESS" "服务停止成功"
    else
        log_message "ERROR" "服务停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    log_message "INFO" "重启路由优先级管理服务"
    
    systemctl daemon-reload
    if systemctl restart "$TIMER_NAME"; then
        log_message "SUCCESS" "服务重启成功"
    else
        log_message "ERROR" "服务重启失败"
        return 1
    fi
}

# 启用服务
enable_service() {
    log_message "INFO" "启用路由优先级管理服务"
    
    if systemctl enable "$TIMER_NAME"; then
        log_message "SUCCESS" "服务已设置为开机自启"
    else
        log_message "ERROR" "启用服务失败"
        return 1
    fi
}

# 禁用服务
disable_service() {
    log_message "INFO" "禁用路由优先级管理服务"
    
    if systemctl disable "$TIMER_NAME"; then
        log_message "SUCCESS" "服务已禁用开机自启"
    else
        log_message "ERROR" "禁用服务失败"
        return 1
    fi
}

# 查看日志
show_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"
    
    log_message "INFO" "显示服务日志"
    
    if [ "$follow" = "true" ]; then
        echo -e "${YELLOW}实时日志 (按Ctrl+C退出):${NC}"
        journalctl -u "$SERVICE_NAME.service" -f
    else
        echo -e "${YELLOW}最近 $lines 行日志:${NC}"
        journalctl -u "$SERVICE_NAME.service" -n "$lines" --no-pager
    fi
}

# 手动执行一次
run_once() {
    log_message "INFO" "手动执行一次路由配置"
    
    if [ -x "$MAIN_SCRIPT" ]; then
        "$MAIN_SCRIPT" --systemd-mode
        
        echo -e "\n${BLUE}执行后的路由状态:${NC}"
        "$MAIN_SCRIPT" ranking
    else
        log_message "ERROR" "主脚本不存在或无执行权限"
        return 1
    fi
}

# 编辑配置文件
edit_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_message "ERROR" "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    log_message "INFO" "编辑配置文件: $CONFIG_FILE"
    
    # 检查可用的编辑器
    local editor=""
    for cmd in nano vim vi; do
        if command -v "$cmd" >/dev/null 2>&1; then
            editor="$cmd"
            break
        fi
    done
    
    if [ -z "$editor" ]; then
        log_message "ERROR" "未找到可用的文本编辑器"
        return 1
    fi
    
    # 备份配置文件
    cp "$CONFIG_FILE" "${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 编辑配置文件
    "$editor" "$CONFIG_FILE"
    
    # 询问是否重启服务
    echo -e "\n${YELLOW}配置文件已修改，是否重启服务以应用更改? (Y/n)${NC}"
    read -r restart_choice
    restart_choice=${restart_choice:-Y}
    
    if [[ $restart_choice =~ ^[Yy]$ ]]; then
        restart_service
    fi
}

# 显示配置信息
show_config() {
    log_message "INFO" "显示当前配置"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_message "ERROR" "配置文件不存在: $CONFIG_FILE"
        return 1
    fi
    
    echo -e "${BLUE}==================== 当前配置 ====================${NC}"
    echo -e "${CYAN}配置文件: $CONFIG_FILE${NC}"
    echo ""
    
    # 显示主要配置项
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        [[ "$key" =~ ^#.*$ ]] || [ -z "$key" ] && continue
        
        # 清理值中的引号
        value=$(echo "$value" | sed 's/^"//;s/"$//')
        
        case "$key" in
            "ENABLE_AUTO_MANAGEMENT")
                echo -e "${YELLOW}自动管理:${NC} $value"
                ;;
            "PRIORITY_INTERFACES")
                if [ -n "$value" ]; then
                    echo -e "${YELLOW}优先级网卡:${NC} $value"
                else
                    echo -e "${YELLOW}优先级网卡:${NC} 自动选择"
                fi
                ;;
            "BASE_METRIC")
                echo -e "${YELLOW}基础优先级:${NC} $value"
                ;;
            "PREFERRED_INTERFACE_TYPES")
                echo -e "${YELLOW}首选接口类型:${NC} $value"
                ;;
            "CLEANUP_OTHER_ROUTES")
                echo -e "${YELLOW}清理其他路由:${NC} $value"
                ;;
            "LOG_LEVEL")
                echo -e "${YELLOW}日志级别:${NC} $value"
                ;;
        esac
    done < "$CONFIG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}路由优先级管理器服务管理脚本${NC}

${YELLOW}用法:${NC}
  $0 [命令]

${YELLOW}命令:${NC}
  status          显示服务状态
  start           启动服务
  stop            停止服务
  restart         重启服务
  enable          启用开机自启
  disable         禁用开机自启
  logs [行数]     显示日志 (默认50行)
  logs-follow     实时查看日志
  run             手动执行一次
  config          编辑配置文件
  show-config     显示当前配置
  help            显示帮助信息

${YELLOW}示例:${NC}
  sudo $0 status              # 查看服务状态
  sudo $0 restart             # 重启服务
  sudo $0 logs 100            # 查看最近100行日志
  sudo $0 logs-follow         # 实时查看日志
  sudo $0 config              # 编辑配置文件
  sudo $0 run                 # 手动执行一次

${YELLOW}配置文件位置:${NC}
  $CONFIG_FILE

${YELLOW}主脚本位置:${NC}
  $MAIN_SCRIPT
EOF
}

# 主函数
main() {
    # 检查root权限（某些操作需要）
    if [[ "$1" =~ ^(start|stop|restart|enable|disable|run|config)$ ]] && [ "$EUID" -ne 0 ]; then
        log_message "ERROR" "此操作需要root权限"
        echo -e "${YELLOW}请使用: sudo $0 $1${NC}"
        exit 1
    fi
    
    # 检查安装状态
    check_installation
    
    case "${1:-status}" in
        status)
            show_status
            ;;
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        enable)
            enable_service
            ;;
        disable)
            disable_service
            ;;
        logs)
            show_logs "${2:-50}" false
            ;;
        logs-follow)
            show_logs 50 true
            ;;
        run)
            run_once
            ;;
        config)
            edit_config
            ;;
        show-config)
            show_config
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_message "ERROR" "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
