# 路由优先级配置文件示例
# 格式: 目标网络|优先级|网关|接口|注释
# 
# 说明:
# - 目标网络: 可以是 default, 具体IP段 (如 ***********/24), 或单个IP
# - 优先级: 0-65535，数值越小优先级越高
# - 网关: 可选，留空则保持原有网关
# - 接口: 可选，留空则保持原有接口
# - 注释: 可选，用于说明

# ==================== 默认路由配置 ====================
# 主要网络连接 - 有线网络优先级最高
default|100|***********|eth0|主要有线网络连接

# 备用网络连接 - 无线网络作为备用
default|200|***********|wlan0|备用无线网络连接

# 移动热点作为最后备用
default|300|************|wlan1|移动热点备用连接

# ==================== 内网路由配置 ====================
# 办公网络 - 高优先级
************/24|50|***********|eth0|办公内网高优先级

# 服务器网段 - 中等优先级
************/24|100|***********|eth0|服务器网段

# 打印机网段 - 低优先级
************/24|150|***********|eth0|打印机网段

# ==================== VPN和专网配置 ====================
# VPN网络 - 通过特定网关
10.0.0.0/8|80|***********00|tun0|VPN网络连接

# 公司专网
172.16.0.0/12|90|***********|eth0|公司专网

# ==================== 特殊路由配置 ====================
# 本地回环 - 最高优先级
127.0.0.0/8|10||lo|本地回环网络

# 链路本地地址
169.254.0.0/16|250||eth0|链路本地地址

# ==================== 外网访问配置 ====================
# 特定外网服务 - 通过主网关
8.8.8.8/32|50|***********|eth0|Google DNS服务器

# 国内DNS服务器
114.114.114.114/32|60|***********|eth0|国内DNS服务器

# ==================== 测试和调试路由 ====================
# 测试网段 - 用于网络测试
*************/24|200|***********|eth1|测试网段

# 开发环境网段
*************/24|180|***********|eth1|开发环境网段

# ==================== 注释示例 ====================
# 以下是一些被注释掉的配置示例，可以根据需要启用

# 备用DNS路由
# *******/32|70|***********|eth0|Cloudflare DNS

# 特殊应用路由
# ************/24|120|************|eth2|特殊应用网段

# IPv6路由示例（需要系统支持）
# ::/0|100|fe80::1|eth0|IPv6默认路由

# ==================== 配置说明 ====================
# 1. 优先级规划建议:
#    - 0-50:   关键系统路由
#    - 51-100: 主要业务路由  
#    - 101-200: 一般业务路由
#    - 201-300: 备用和测试路由
#
# 2. 网关配置:
#    - 留空: 保持现有网关设置
#    - 指定IP: 使用指定的网关
#
# 3. 接口配置:
#    - 留空: 保持现有接口设置
#    - 指定接口: 使用指定的网络接口
#
# 4. 使用方法:
#    sudo ./route-priority-manager.sh batch route-config-example.txt
