#!/bin/bash

# 统一路由优先级管理器
# 整合所有路由管理功能到一个脚本中
# 版本: 2.0
# 作者: Route Manager Team

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_NAME="$(basename "$0")"
LOG_FILE="${HOME}/.unified-route-manager.log"
BACKUP_DIR="${HOME}/.route-backups"
CONFIG_FILE="${HOME}/.unified-route-manager.conf"

# 如果是root用户，使用系统目录
if [ "$EUID" -eq 0 ]; then
    LOG_FILE="/var/log/unified-route-manager.log"
    BACKUP_DIR="/etc/network/route-backups"
    CONFIG_FILE="/etc/unified-route-manager.conf"
fi

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 尝试写入日志文件，如果失败则忽略
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE" 2>/dev/null || true

    case $level in
        "INFO")  echo -e "${BLUE}ℹ $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}⚠ $message${NC}" ;;
        "ERROR") echo -e "${RED}✗ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✓ $message${NC}" ;;
        "DEBUG") echo -e "${CYAN}🔍 $message${NC}" ;;
    esac
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_message "ERROR" "此操作需要root权限"
        echo -e "${YELLOW}请使用: sudo $0 $*${NC}"
        exit 1
    fi
}

# 创建必要的目录
setup_directories() {
    mkdir -p "$BACKUP_DIR"
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE" 2>/dev/null || true
}

# 备份当前路由表
backup_routes() {
    local backup_file="$BACKUP_DIR/routes_$(date +%Y%m%d_%H%M%S).backup"
    
    log_message "INFO" "备份当前路由表到: $backup_file"
    
    {
        echo "# 路由表备份 - $(date)"
        echo "# 静态路由 (UG标志)"
        route -n | grep "UG"
        echo ""
        echo "# 所有默认路由"
        ip route show default
        echo ""
        echo "# 完整路由表"
        ip route show
    } > "$backup_file"
    
    log_message "SUCCESS" "路由表备份完成"
    echo "$backup_file"
}

# 显示当前路由状态
show_routes() {
    local format="${1:-table}"
    
    log_message "INFO" "显示当前路由状态"
    
    echo -e "${BLUE}==================== 当前路由状态 ====================${NC}"
    
    echo -e "${CYAN}=== 静态路由 (UG标志) ===${NC}"
    route -n | grep "UG" || echo "没有找到静态路由"
    
    echo -e "\n${CYAN}=== 所有默认路由 ===${NC}"
    ip route show default || echo "没有找到默认路由"
    
    if [ "$format" = "detail" ]; then
        echo -e "\n${CYAN}=== 完整路由表 ===${NC}"
        ip route show | head -20
        
        echo -e "\n${CYAN}=== 路由统计 ===${NC}"
        echo "总路由数: $(ip route show | wc -l)"
        echo "默认路由数: $(ip route show default | wc -l)"
        echo "静态路由数: $(route -n | grep -c "UG")"
    fi
}

# 显示网络接口优先级排名
show_ranking() {
    log_message "INFO" "显示网络接口优先级排名"

    echo -e "${BLUE}==================== 网络接口优先级排名 ====================${NC}"

    # 获取所有接口的默认路由metric
    local interface_metrics=()

    while IFS= read -r route; do
        if [[ "$route" =~ ^default ]]; then
            local dev=$(echo "$route" | grep -o 'dev [^ ]*' | awk '{print $2}')
            local metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')
            local gw=$(echo "$route" | grep -o 'via [^ ]*' | awk '{print $2}' || echo "直连")

            if [ -n "$dev" ] && [ -n "$metric" ]; then
                interface_metrics+=("$metric:$dev:$gw")
            fi
        fi
    done < <(ip route show default)

    if [ ${#interface_metrics[@]} -eq 0 ]; then
        echo -e "${YELLOW}没有找到带优先级的默认路由${NC}"
        return 0
    fi

    # 排序并显示
    printf "${CYAN}%-4s %-15s %-8s %-15s %-10s${NC}\n" "排名" "接口" "优先级" "网关" "状态"
    echo "--------------------------------------------------------"

    local rank=1
    for entry in $(printf '%s\n' "${interface_metrics[@]}" | sort -n); do
        local metric=$(echo "$entry" | cut -d':' -f1)
        local dev=$(echo "$entry" | cut -d':' -f2)
        local gw=$(echo "$entry" | cut -d':' -f3)

        # 获取接口状态（检查UP标志而不是state）
        local status="DOWN"
        if ip link show "$dev" 2>/dev/null | grep -q "UP"; then
            status="UP"
        fi

        # 根据排名显示不同颜色
        local color=""
        case $rank in
            1) color="${GREEN}" ;;
            2) color="${YELLOW}" ;;
            *) color="${NC}" ;;
        esac

        printf "${color}%-4s %-15s %-8s %-15s %-10s${NC}\n" "#$rank" "$dev" "$metric" "$gw" "$status"
        ((rank++))
    done

    echo ""
    echo -e "${BLUE}说明:${NC}"
    echo -e "  - 优先级数值越小，路由优先级越高"
    echo -e "  - ${GREEN}绿色${NC}表示最高优先级，${YELLOW}黄色${NC}表示第二优先级"
    echo -e "  - 只显示有默认路由的接口"
}

# 清理重复的默认路由
cleanup_duplicate_routes() {
    local target_interface="$1"
    
    check_root
    setup_directories
    
    log_message "INFO" "清理重复的默认路由"
    
    # 备份路由表
    backup_routes >/dev/null
    
    echo -e "${BLUE}清理前的静态路由:${NC}"
    route -n | grep "UG"
    
    # 获取所有有默认路由的接口
    local interfaces=()
    while IFS= read -r route; do
        if [ -n "$route" ]; then
            local dev=$(echo "$route" | grep -o 'dev [^ ]*' | awk '{print $2}')
            if [ -n "$dev" ] && [[ ! " ${interfaces[@]} " =~ " $dev " ]]; then
                interfaces+=("$dev")
            fi
        fi
    done < <(ip route show default)
    
    # 如果指定了接口，只清理该接口
    if [ -n "$target_interface" ]; then
        interfaces=("$target_interface")
    fi
    
    echo -e "\n${CYAN}发现以下接口有默认路由: ${interfaces[*]}${NC}"
    
    # 为每个接口清理重复路由
    for interface in "${interfaces[@]}"; do
        echo -e "\n${CYAN}清理 $interface 的重复路由...${NC}"
        
        local interface_routes=$(ip route show default dev "$interface" 2>/dev/null)
        if [ -z "$interface_routes" ]; then
            log_message "INFO" "$interface 没有默认路由，跳过"
            continue
        fi
        
        # 计算路由数量
        local route_count=$(echo "$interface_routes" | wc -l)
        if [ "$route_count" -le 1 ]; then
            log_message "INFO" "$interface 只有一条默认路由，无需清理"
            continue
        fi
        
        log_message "INFO" "$interface 有 $route_count 条默认路由，开始清理..."
        
        # 找到最小的metric（最高优先级）
        local min_metric=999999
        local best_route=""
        
        while IFS= read -r route; do
            if [ -n "$route" ]; then
                local metric=$(echo "$route" | grep -o 'metric [0-9]*' | awk '{print $2}')
                if [ -n "$metric" ] && [ "$metric" -lt "$min_metric" ]; then
                    min_metric="$metric"
                    best_route="$route"
                fi
            fi
        done <<< "$interface_routes"
        
        echo -e "${GREEN}保留最优路由: $best_route${NC}"
        
        # 删除其他路由
        local deleted_count=0
        while IFS= read -r route; do
            if [ -n "$route" ] && [ "$route" != "$best_route" ]; then
                echo -e "${YELLOW}删除重复路由: $route${NC}"
                if ip route del $route 2>/dev/null; then
                    log_message "SUCCESS" "删除成功"
                    ((deleted_count++))
                else
                    log_message "WARN" "删除失败，可能已被删除"
                fi
            fi
        done <<< "$interface_routes"
        
        log_message "SUCCESS" "$interface 清理完成，删除了 $deleted_count 条重复路由"
    done
    
    echo -e "\n${BLUE}清理后的静态路由:${NC}"
    route -n | grep "UG"
    
    log_message "SUCCESS" "重复路由清理完成"
}

# 智能设置接口优先级
set_interface_priority() {
    local interface="$1"
    local target_metric="$2"
    
    check_root
    setup_directories
    
    if [ -z "$interface" ] || [ -z "$target_metric" ]; then
        log_message "ERROR" "缺少必要参数: 接口名称和优先级"
        show_help
        return 1
    fi
    
    # 验证metric值
    if ! [[ "$target_metric" =~ ^[0-9]+$ ]] || [ "$target_metric" -lt 0 ] || [ "$target_metric" -gt 65535 ]; then
        log_message "ERROR" "无效的优先级值: $target_metric (应为0-65535之间的整数)"
        return 1
    fi
    
    # 检查接口是否存在
    if ! ip link show "$interface" >/dev/null 2>&1; then
        log_message "ERROR" "网络接口不存在: $interface"
        return 1
    fi
    
    log_message "INFO" "设置 $interface 优先级为 $target_metric"
    
    # 备份路由表
    backup_routes >/dev/null
    
    # 首先清理该接口的重复路由
    cleanup_duplicate_routes "$interface"
    
    # 获取或推断网关
    local gateway=""
    local existing_route=$(ip route show default dev "$interface" 2>/dev/null | head -1)
    
    if [ -n "$existing_route" ]; then
        gateway=$(echo "$existing_route" | grep -o 'via [^ ]*' | awk '{print $2}')
    fi
    
    # 如果没有现有路由，尝试推断网关
    if [ -z "$existing_route" ]; then
        local interface_ip=$(ip addr show "$interface" | grep -o 'inet [0-9.]*/[0-9]*' | awk '{print $2}' | head -1)

        if [ -n "$interface_ip" ]; then
            # 解析IP地址和子网掩码
            local ip_with_mask="$interface_ip"
            local ip_addr=$(echo "$ip_with_mask" | cut -d'/' -f1)
            local subnet_mask=$(echo "$ip_with_mask" | cut -d'/' -f2)

            if ip link show "$interface" | grep -q "POINTOPOINT"; then
                # 点对点接口，智能推断网关
                if [ "$subnet_mask" = "30" ]; then
                    # /30网络，网关是网络中的另一个IP
                    local network_prefix=$(echo "$ip_addr" | cut -d'.' -f1-3)
                    local last_octet=$(echo "$ip_addr" | cut -d'.' -f4)
                    local network_base=$((last_octet / 4 * 4))
                    if [ $((last_octet % 4)) -eq 1 ]; then
                        gateway="${network_prefix}.$((network_base + 2))"
                    else
                        gateway="${network_prefix}.$((network_base + 1))"
                    fi
                else
                    # 其他点对点网络，尝试推断网关
                    local network=$(echo "$ip_addr" | cut -d'.' -f1-3)
                    gateway="${network}.1"
                fi
            else
                # 以太网接口，网关通常是 .1
                local network=$(echo "$ip_addr" | cut -d'.' -f1-3)
                gateway="${network}.1"

                # 验证网关是否可达
                if ! ping -c 1 -W 2 "$gateway" >/dev/null 2>&1; then
                    gateway=""
                fi
            fi
        fi
    fi
    
    # 设置新的路由
    if [ -n "$gateway" ]; then
        if ip route replace default via "$gateway" dev "$interface" metric "$target_metric" 2>/dev/null; then
            log_message "SUCCESS" "已设置 $interface 默认路由: via $gateway metric $target_metric"
        else
            if ip route add default via "$gateway" dev "$interface" metric "$target_metric" 2>/dev/null; then
                log_message "SUCCESS" "已添加 $interface 默认路由: via $gateway metric $target_metric"
            else
                log_message "WARN" "设置路由失败，可能已存在相同路由"
            fi
        fi
    else
        # 点对点接口
        if ip route replace default dev "$interface" metric "$target_metric" 2>/dev/null; then
            log_message "SUCCESS" "已设置 $interface 点对点默认路由: metric $target_metric"
        else
            if ip route add default dev "$interface" metric "$target_metric" 2>/dev/null; then
                log_message "SUCCESS" "已添加 $interface 点对点默认路由: metric $target_metric"
            else
                log_message "WARN" "设置点对点路由失败，可能已存在相同路由"
            fi
        fi
    fi
    
    # 显示结果
    echo -e "\n${BLUE}设置完成后的路由状态:${NC}"
    show_ranking
    
    return 0
}

# 自动选择最佳接口
auto_set_best_interface() {
    local preferred_types="${1:-eth,enp,ens,wlp}"
    local base_metric="${2:-10}"

    check_root
    setup_directories

    log_message "INFO" "自动选择最佳网络接口"

    # 获取所有UP状态的接口
    local available_interfaces=()
    while IFS= read -r line; do
        local iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
        if [ "$iface" != "lo" ] && echo "$line" | grep -q "UP"; then
            available_interfaces+=("$iface")
        fi
    done < <(ip link show | grep -E '^[0-9]+:')

    if [ ${#available_interfaces[@]} -eq 0 ]; then
        log_message "ERROR" "没有找到可用的网络接口"
        return 1
    fi

    echo -e "${CYAN}可用的网络接口: ${available_interfaces[*]}${NC}"

    # 按优先级类型排序接口
    IFS=',' read -ra TYPES <<< "$preferred_types"
    local best_interface=""

    for type in "${TYPES[@]}"; do
        for interface in "${available_interfaces[@]}"; do
            if [[ "$interface" =~ ^$type ]]; then
                best_interface="$interface"
                break 2
            fi
        done
    done

    # 如果没有找到匹配的类型，选择第一个可用接口
    if [ -z "$best_interface" ]; then
        best_interface="${available_interfaces[0]}"
    fi

    log_message "SUCCESS" "选择最佳接口: $best_interface"

    # 设置该接口为最高优先级
    set_interface_priority "$best_interface" "$base_metric"
}

# 批量设置多个接口优先级
batch_set_priorities() {
    local config_file="$1"

    check_root
    setup_directories

    if [ -z "$config_file" ] || [ ! -f "$config_file" ]; then
        log_message "ERROR" "配置文件不存在: $config_file"
        return 1
    fi

    log_message "INFO" "批量设置接口优先级"

    # 备份路由表
    backup_routes >/dev/null

    local processed=0
    local failed=0

    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ "$line" =~ ^[[:space:]]*# ]] || [[ "$line" =~ ^[[:space:]]*$ ]]; then
            continue
        fi

        # 解析配置行: interface:metric
        if [[ "$line" =~ ^([^:]+):([0-9]+)$ ]]; then
            local interface="${BASH_REMATCH[1]}"
            local metric="${BASH_REMATCH[2]}"

            echo -e "${CYAN}设置 $interface 优先级为 $metric${NC}"

            if set_interface_priority "$interface" "$metric"; then
                ((processed++))
            else
                ((failed++))
            fi
        else
            log_message "WARN" "无效的配置行: $line"
        fi
    done < "$config_file"

    log_message "SUCCESS" "批量设置完成: 成功 $processed 个，失败 $failed 个"

    # 显示最终结果
    echo -e "\n${BLUE}批量设置完成后的路由状态:${NC}"
    show_ranking
}

# 恢复路由备份
restore_routes() {
    local backup_file="$1"

    check_root
    setup_directories

    if [ -z "$backup_file" ]; then
        # 显示可用的备份文件
        echo -e "${CYAN}可用的备份文件:${NC}"
        ls -la "$BACKUP_DIR"/*.backup 2>/dev/null | tail -10 || {
            log_message "ERROR" "没有找到备份文件"
            return 1
        }
        return 0
    fi

    if [ ! -f "$backup_file" ]; then
        log_message "ERROR" "备份文件不存在: $backup_file"
        return 1
    fi

    log_message "WARN" "恢复路由备份可能会影响网络连接"
    echo -e "${YELLOW}确定要恢复备份吗? (y/N)${NC}"
    read -r confirm

    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_message "INFO" "取消恢复操作"
        return 0
    fi

    log_message "INFO" "恢复路由备份: $backup_file"

    # 这里只是示例，实际恢复需要更复杂的逻辑
    log_message "WARN" "路由恢复功能需要手动实现，请参考备份文件内容"
    cat "$backup_file"
}

# 检查网络连通性
check_connectivity() {
    local test_hosts=("*******" "***************" "www.baidu.com")

    log_message "INFO" "检查网络连通性"

    echo -e "${BLUE}==================== 网络连通性测试 ====================${NC}"

    for host in "${test_hosts[@]}"; do
        echo -n -e "${CYAN}测试 $host ... ${NC}"

        if ping -c 1 -W 3 "$host" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ 连通${NC}"
        else
            echo -e "${RED}✗ 不通${NC}"
        fi
    done

    echo -e "\n${CYAN}=== 路由追踪到 ******* ===${NC}"
    traceroute -m 5 ******* 2>/dev/null | head -5 || echo "traceroute 命令不可用"
}

# 显示系统信息
show_system_info() {
    echo -e "${BLUE}==================== 系统网络信息 ====================${NC}"

    echo -e "${CYAN}=== 网络接口状态 ===${NC}"
    ip link show | grep -E '^[0-9]+:' | while read -r line; do
        local iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
        if [ "$iface" != "lo" ]; then
            if echo "$line" | grep -q "UP"; then
                echo -e "  ${GREEN}$iface: UP${NC}"
            else
                echo -e "  ${RED}$iface: DOWN${NC}"
            fi
        fi
    done

    echo -e "\n${CYAN}=== IP 地址信息 ===${NC}"
    ip addr show | grep -E 'inet ' | grep -v '127.0.0.1' | while read -r line; do
        echo "  $line"
    done

    echo -e "\n${CYAN}=== DNS 配置 ===${NC}"
    if [ -f /etc/resolv.conf ]; then
        grep nameserver /etc/resolv.conf | head -3
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}==================== 统一路由优先级管理器 ====================${NC}

${CYAN}用法:${NC}
  $SCRIPT_NAME <命令> [参数]

${CYAN}路由管理命令:${NC}
  show [detail]                   显示当前路由状态
  ranking                         显示接口优先级排名
  set <接口> <优先级>             设置接口优先级 (metric值)
  auto [类型] [基础优先级]        自动选择最佳接口
  cleanup [接口]                  清理重复的默认路由
  batch <配置文件>                批量设置接口优先级

${CYAN}备份恢复命令:${NC}
  backup                          备份当前路由表
  restore [备份文件]              恢复路由备份 (不指定文件则列出可用备份)

${CYAN}诊断命令:${NC}
  check                           检查网络连通性
  info                            显示系统网络信息
  status                          显示服务状态 (如果已安装)

${CYAN}示例:${NC}
  $SCRIPT_NAME show               # 显示当前路由
  $SCRIPT_NAME ranking            # 显示优先级排名
  $SCRIPT_NAME set wlp1s0 10      # 设置WiFi最高优先级
  $SCRIPT_NAME set wwan0 50       # 设置移动网络第二优先级
  $SCRIPT_NAME cleanup            # 清理所有重复路由
  $SCRIPT_NAME auto               # 自动选择最佳接口
  $SCRIPT_NAME check              # 检查网络连通性

${CYAN}配置文件格式 (用于batch命令):${NC}
  # 接口:优先级
  wlp1s0:10
  wwan0:50
  eth0:100

${CYAN}说明:${NC}
  - 优先级数值越小，路由优先级越高
  - 建议WiFi使用10，移动网络使用50，有线网络使用100
  - 所有修改前都会自动备份路由表
  - 需要root权限执行路由修改操作

EOF
}

# 检查服务状态 (如果已安装systemd服务)
check_service_status() {
    echo -e "${BLUE}==================== 服务状态检查 ====================${NC}"

    # 检查是否安装了systemd服务
    if systemctl list-unit-files | grep -q "route-priority-manager"; then
        echo -e "${CYAN}=== 定时器状态 ===${NC}"
        systemctl status route-priority-manager.timer --no-pager -l | head -10

        echo -e "\n${CYAN}=== 服务最后执行状态 ===${NC}"
        systemctl status route-priority-manager.service --no-pager -l | head -10

        echo -e "\n${CYAN}=== 最近日志 ===${NC}"
        journalctl -u route-priority-manager.service -n 5 --no-pager -o short

        echo -e "\n${CYAN}=== 定时器信息 ===${NC}"
        systemctl list-timers route-priority-manager.timer --no-pager
    else
        echo -e "${YELLOW}未检测到已安装的systemd服务${NC}"
        echo -e "${CYAN}如需安装服务，请使用原始的安装脚本${NC}"
    fi
}

# 创建示例配置文件
create_sample_config() {
    local config_file="${1:-./route-config-sample.txt}"

    cat > "$config_file" << 'EOF'
# 路由优先级配置文件
# 格式: 接口名:优先级(metric值)
# 优先级数值越小，路由优先级越高

# WiFi接口 - 最高优先级
wlp1s0:10

# 移动网络 - 第二优先级
wwan0:50

# 有线网络 - 第三优先级
eth0:100
enp1s0:100

# 其他接口
# ens33:200
# enp2s0:200
EOF

    log_message "SUCCESS" "示例配置文件已创建: $config_file"
    echo -e "${CYAN}配置文件内容:${NC}"
    cat "$config_file"
}

# 主函数
main() {
    # 设置目录
    setup_directories

    case "${1:-help}" in
        show)
            show_routes "$2"
            ;;
        ranking|rank)
            show_ranking
            ;;
        set|priority)
            set_interface_priority "$2" "$3"
            ;;
        auto)
            auto_set_best_interface "$2" "$3"
            ;;
        cleanup|clean)
            cleanup_duplicate_routes "$2"
            ;;
        batch)
            batch_set_priorities "$2"
            ;;
        backup)
            backup_file=$(backup_routes)
            echo -e "${GREEN}路由表已备份到: $backup_file${NC}"
            ;;
        restore)
            restore_routes "$2"
            ;;
        check|test)
            check_connectivity
            ;;
        info|system)
            show_system_info
            ;;
        status|service)
            check_service_status
            ;;
        sample|config)
            create_sample_config "$2"
            ;;
        help|--help|-h|"")
            show_help
            ;;
        *)
            log_message "ERROR" "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
