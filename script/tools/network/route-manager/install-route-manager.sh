#!/bin/bash

# 路由优先级管理器安装脚本
# 支持systemd自启动和网卡优先级配置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/etc/route-manager"
SYSTEMD_DIR="/etc/systemd/system"
SERVICE_NAME="route-priority-manager"

# 安装文件列表
MAIN_SCRIPT="route-priority-manager.sh"
SERVICE_FILE="${SERVICE_NAME}.service"
TIMER_FILE="${SERVICE_NAME}.timer"
CONFIG_FILE="route-manager.conf"

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}ℹ $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}⚠ $message${NC}" ;;
        "ERROR") echo -e "${RED}✗ $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}✓ $message${NC}" ;;
    esac
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_message "ERROR" "此脚本需要root权限运行"
        echo -e "${YELLOW}请使用: sudo $0${NC}"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_message "INFO" "检查系统要求..."
    
    # 检查systemd
    if ! command -v systemctl >/dev/null 2>&1; then
        log_message "ERROR" "系统不支持systemd"
        exit 1
    fi
    
    # 检查ip命令
    if ! command -v ip >/dev/null 2>&1; then
        log_message "ERROR" "系统缺少ip命令工具"
        exit 1
    fi
    
    # 检查主脚本
    if [ ! -f "$SCRIPT_DIR/$MAIN_SCRIPT" ]; then
        log_message "ERROR" "找不到主脚本: $SCRIPT_DIR/$MAIN_SCRIPT"
        exit 1
    fi
    
    log_message "SUCCESS" "系统要求检查通过"
}

# 创建systemd服务文件
create_systemd_files() {
    log_message "INFO" "创建systemd服务文件..."
    
    # 创建服务文件
    cat > "$SYSTEMD_DIR/$SERVICE_FILE" << EOF
[Unit]
Description=Route Priority Manager Service
Documentation=file://$CONFIG_DIR/README.md
After=network.target
Wants=network.target

[Service]
Type=oneshot
User=root
Group=root
WorkingDirectory=$CONFIG_DIR
ExecStart=$INSTALL_DIR/$MAIN_SCRIPT --systemd-mode
StandardOutput=journal
StandardError=journal
SyslogIdentifier=route-priority-manager

# 环境变量
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=HOME=/root

# 安全设置
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$CONFIG_DIR
ReadWritePaths=/var/log
NoNewPrivileges=true

# 资源限制
MemoryMax=128M
CPUQuota=25%

[Install]
WantedBy=multi-user.target
EOF

    # 创建定时器文件
    cat > "$SYSTEMD_DIR/$TIMER_FILE" << EOF
[Unit]
Description=Route Priority Manager Timer
Documentation=file://$CONFIG_DIR/README.md
Requires=$SERVICE_FILE

[Timer]
# 系统启动后30秒执行
OnBootSec=30sec

# 网络状态变化时触发
OnUnitActiveSec=60sec

# 如果系统关机时错过了任务，启动后立即执行
Persistent=true

# 随机延迟0-30秒，避免系统负载峰值
RandomizedDelaySec=30sec

# 精确度设置
AccuracySec=10sec

[Install]
WantedBy=timers.target
EOF

    log_message "SUCCESS" "systemd文件创建完成"
}

# 创建配置文件
create_config_file() {
    log_message "INFO" "创建配置文件..."
    
    mkdir -p "$CONFIG_DIR"
    
    cat > "$CONFIG_DIR/$CONFIG_FILE" << EOF
# 路由优先级管理器配置文件
# 此文件在系统启动时被读取

# 是否启用自动路由管理
ENABLE_AUTO_MANAGEMENT=true

# 优先级网卡配置 (留空则自动选择)
# 支持多个网卡，用逗号分隔，按优先级排序
PRIORITY_INTERFACES=""

# 基础优先级值 (数值越小优先级越高)
BASE_METRIC=10

# 网卡类型优先级 (用逗号分隔，按优先级排序)
PREFERRED_INTERFACE_TYPES="eth,enp,ens,wlp,wlan"

# 是否清理其他接口的高优先级路由
CLEANUP_OTHER_ROUTES=true

# 日志级别 (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# 是否在网络变化时自动调整
AUTO_ADJUST_ON_CHANGE=true

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 是否发送通知 (需要安装notify-send)
ENABLE_NOTIFICATIONS=false

# 排除的网络接口 (不进行管理)
EXCLUDED_INTERFACES="lo,docker0,br-"

# 最大重试次数
MAX_RETRY_COUNT=3

# 重试间隔 (秒)
RETRY_INTERVAL=5
EOF

    log_message "SUCCESS" "配置文件创建完成: $CONFIG_DIR/$CONFIG_FILE"
}

# 安装主脚本
install_main_script() {
    log_message "INFO" "安装主脚本..."
    
    # 复制主脚本
    cp "$SCRIPT_DIR/$MAIN_SCRIPT" "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/$MAIN_SCRIPT"
    
    # 复制其他文件
    if [ -f "$SCRIPT_DIR/README.md" ]; then
        cp "$SCRIPT_DIR/README.md" "$CONFIG_DIR/"
    fi
    
    if [ -f "$SCRIPT_DIR/route-config-example.txt" ]; then
        cp "$SCRIPT_DIR/route-config-example.txt" "$CONFIG_DIR/"
    fi
    
    log_message "SUCCESS" "主脚本安装完成"
}

# 修改主脚本支持systemd模式
modify_main_script() {
    log_message "INFO" "修改主脚本以支持systemd模式..."
    
    # 在主脚本中添加systemd模式支持
    cat >> "$INSTALL_DIR/$MAIN_SCRIPT" << 'EOF'

# systemd模式函数
systemd_mode() {
    local config_file="/etc/route-manager/route-manager.conf"
    
    # 加载配置文件
    if [ -f "$config_file" ]; then
        source "$config_file"
    fi
    
    # 检查是否启用自动管理
    if [ "${ENABLE_AUTO_MANAGEMENT:-true}" != "true" ]; then
        log_message "INFO" "自动路由管理已禁用"
        return 0
    fi
    
    log_message "INFO" "systemd模式启动 - 自动配置网卡优先级"
    
    # 如果指定了优先级接口
    if [ -n "${PRIORITY_INTERFACES}" ]; then
        IFS=',' read -ra INTERFACES <<< "${PRIORITY_INTERFACES}"
        local current_metric=${BASE_METRIC:-10}
        
        for interface in "${INTERFACES[@]}"; do
            interface=$(echo "$interface" | xargs)  # 去除空格
            if ip link show "$interface" >/dev/null 2>&1; then
                log_message "INFO" "设置 $interface 优先级: metric $current_metric"
                set_interface_highest_priority "$interface" "$current_metric" >/dev/null 2>&1
                current_metric=$((current_metric + 50))
            else
                log_message "WARN" "接口 $interface 不存在，跳过"
            fi
        done
    else
        # 自动选择最佳接口
        log_message "INFO" "自动选择最佳网络接口"
        echo "y" | auto_set_best_interface "${PREFERRED_INTERFACE_TYPES:-eth,enp,ens}" "${BASE_METRIC:-10}" >/dev/null 2>&1
    fi
    
    log_message "SUCCESS" "systemd模式执行完成"
}

# 检查systemd模式参数
if [ "$1" = "--systemd-mode" ]; then
    systemd_mode
    exit 0
fi
EOF

    log_message "SUCCESS" "主脚本修改完成"
}

# 配置向导
configuration_wizard() {
    # 检查是否有预设配置参数
    if [ -n "$PRESET_CONFIG_MODE" ]; then
        log_message "INFO" "使用预设配置模式: $PRESET_CONFIG_MODE"

        case "$PRESET_CONFIG_MODE" in
            "auto")
                log_message "INFO" "选择自动模式"
                sed -i 's/PRIORITY_INTERFACES=".*"/PRIORITY_INTERFACES=""/' "$CONFIG_DIR/$CONFIG_FILE"
                ;;
            "manual")
                if [ -n "$PRESET_INTERFACES" ]; then
                    log_message "INFO" "设置指定网卡: $PRESET_INTERFACES"
                    sed -i "s/PRIORITY_INTERFACES=\".*\"/PRIORITY_INTERFACES=\"$PRESET_INTERFACES\"/" "$CONFIG_DIR/$CONFIG_FILE"
                else
                    log_message "WARN" "未指定网卡，使用自动模式"
                    sed -i 's/PRIORITY_INTERFACES=".*"/PRIORITY_INTERFACES=""/' "$CONFIG_DIR/$CONFIG_FILE"
                fi
                ;;
            "default")
                log_message "INFO" "使用默认配置"
                ;;
        esac

        # 设置基础优先级
        if [ -n "$PRESET_BASE_METRIC" ]; then
            sed -i "s/BASE_METRIC=.*/BASE_METRIC=$PRESET_BASE_METRIC/" "$CONFIG_DIR/$CONFIG_FILE"
        fi

        # 设置自启动
        if [ "$PRESET_AUTO_START" = "true" ]; then
            systemctl enable "$TIMER_FILE"
            log_message "SUCCESS" "已启用自启动"
        fi

        return 0
    fi
    echo -e "\n${BLUE}==================== 配置向导 ====================${NC}"
    
    # 显示可用网络接口
    echo -e "${CYAN}可用的网络接口:${NC}"
    local interfaces=()
    while IFS= read -r line; do
        local iface=$(echo "$line" | awk -F': ' '{print $2}' | cut -d'@' -f1)
        # 检查接口是否真正可用（有UP标志）
        if [ "$iface" != "lo" ]; then
            interfaces+=("$iface")
            if echo "$line" | grep -q "UP"; then
                echo -e "  ${YELLOW}$iface${NC}: UP"
            else
                echo -e "  ${YELLOW}$iface${NC}: DOWN"
            fi
        fi
    done < <(ip link show | grep -E '^[0-9]+:')
    
    # 询问用户配置
    echo -e "\n${YELLOW}请选择配置方式:${NC}"
    echo -e "1) 自动选择最佳网卡 (推荐)"
    echo -e "2) 手动指定优先级网卡"
    echo -e "3) 使用默认配置"
    
    read -p "请选择 (1-3): " config_choice
    
    case $config_choice in
        1)
            log_message "INFO" "选择自动模式"
            sed -i 's/PRIORITY_INTERFACES=".*"/PRIORITY_INTERFACES=""/' "$CONFIG_DIR/$CONFIG_FILE"
            ;;
        2)
            echo -e "${CYAN}请输入优先级网卡 (用逗号分隔，按优先级排序):${NC}"
            echo -e "${YELLOW}示例: eth0,wlan0${NC}"
            read -p "网卡列表: " priority_interfaces
            
            if [ -n "$priority_interfaces" ]; then
                sed -i "s/PRIORITY_INTERFACES=\".*\"/PRIORITY_INTERFACES=\"$priority_interfaces\"/" "$CONFIG_DIR/$CONFIG_FILE"
                log_message "SUCCESS" "已设置优先级网卡: $priority_interfaces"
            fi
            ;;
        3)
            log_message "INFO" "使用默认配置"
            ;;
    esac
    
    # 询问基础优先级
    echo -e "\n${CYAN}基础优先级值 (数值越小优先级越高，默认10):${NC}"
    read -p "基础优先级: " base_metric
    base_metric=${base_metric:-10}
    
    sed -i "s/BASE_METRIC=.*/BASE_METRIC=$base_metric/" "$CONFIG_DIR/$CONFIG_FILE"
    
    # 询问是否启用自启动
    echo -e "\n${YELLOW}是否启用系统启动时自动配置? (Y/n)${NC}"
    read -r auto_start
    auto_start=${auto_start:-Y}
    
    if [[ $auto_start =~ ^[Yy]$ ]]; then
        systemctl enable "$TIMER_FILE"
        log_message "SUCCESS" "已启用自启动"
    fi
}

# 启用服务
enable_service() {
    log_message "INFO" "启用systemd服务..."
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    # 启用并启动定时器
    systemctl enable "$TIMER_FILE"
    systemctl start "$TIMER_FILE"
    
    log_message "SUCCESS" "服务已启用并启动"
}

# 显示安装结果
show_installation_result() {
    echo -e "\n${GREEN}==================== 安装完成 ====================${NC}"
    
    echo -e "${BLUE}安装位置:${NC}"
    echo -e "  主脚本: ${CYAN}$INSTALL_DIR/$MAIN_SCRIPT${NC}"
    echo -e "  配置文件: ${CYAN}$CONFIG_DIR/$CONFIG_FILE${NC}"
    echo -e "  systemd服务: ${CYAN}$SYSTEMD_DIR/$SERVICE_FILE${NC}"
    echo -e "  systemd定时器: ${CYAN}$SYSTEMD_DIR/$TIMER_FILE${NC}"
    
    echo -e "\n${BLUE}管理命令:${NC}"
    echo -e "  查看状态: ${CYAN}systemctl status $TIMER_FILE${NC}"
    echo -e "  启动服务: ${CYAN}systemctl start $TIMER_FILE${NC}"
    echo -e "  停止服务: ${CYAN}systemctl stop $TIMER_FILE${NC}"
    echo -e "  查看日志: ${CYAN}journalctl -u $SERVICE_FILE -f${NC}"
    echo -e "  手动执行: ${CYAN}$INSTALL_DIR/$MAIN_SCRIPT --systemd-mode${NC}"
    
    echo -e "\n${BLUE}配置文件:${NC}"
    echo -e "  编辑配置: ${CYAN}nano $CONFIG_DIR/$CONFIG_FILE${NC}"
    echo -e "  重新加载: ${CYAN}systemctl daemon-reload && systemctl restart $TIMER_FILE${NC}"
    
    echo -e "\n${YELLOW}注意事项:${NC}"
    echo -e "  - 服务将在系统启动后30秒自动运行"
    echo -e "  - 可通过编辑配置文件自定义行为"
    echo -e "  - 日志记录在systemd journal中"
    echo -e "  - 卸载请运行: sudo $0 --uninstall"
}

# 卸载功能
uninstall() {
    log_message "INFO" "开始卸载路由优先级管理器..."
    
    # 停止并禁用服务
    systemctl stop "$TIMER_FILE" 2>/dev/null || true
    systemctl disable "$TIMER_FILE" 2>/dev/null || true
    systemctl stop "$SERVICE_FILE" 2>/dev/null || true
    systemctl disable "$SERVICE_FILE" 2>/dev/null || true
    
    # 删除文件
    rm -f "$SYSTEMD_DIR/$SERVICE_FILE"
    rm -f "$SYSTEMD_DIR/$TIMER_FILE"
    rm -f "$INSTALL_DIR/$MAIN_SCRIPT"
    rm -rf "$CONFIG_DIR"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_message "SUCCESS" "卸载完成"
}

# 显示帮助信息
show_help() {
    echo "路由优先级管理器安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --install                    执行完整安装 (默认)"
    echo "  --install-auto               自动安装 (自动选择最佳网卡)"
    echo "  --install-manual <网卡>      手动安装 (指定网卡)"
    echo "  --uninstall                  卸载系统"
    echo "  --help                       显示帮助信息"
    echo ""
    echo "预设配置参数:"
    echo "  --interfaces <网卡列表>      指定优先级网卡 (用逗号分隔)"
    echo "  --metric <数值>              设置基础优先级 (默认10)"
    echo "  --auto-start                 启用自启动"
    echo "  --no-auto-start              禁用自启动"
    echo ""
    echo "示例:"
    echo "  $0 --install-auto --metric 5 --auto-start"
    echo "  $0 --install-manual eth0 --metric 10"
    echo "  $0 --install --interfaces eth0,wlan0 --auto-start"
    echo ""
    echo "功能:"
    echo "  - 安装路由优先级管理器到系统"
    echo "  - 配置systemd自启动服务"
    echo "  - 支持自动或手动网卡优先级配置"
    echo "  - 提供完整的配置向导"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --interfaces)
                PRESET_INTERFACES="$2"
                PRESET_CONFIG_MODE="manual"
                shift 2
                ;;
            --metric)
                PRESET_BASE_METRIC="$2"
                shift 2
                ;;
            --auto-start)
                PRESET_AUTO_START="true"
                shift
                ;;
            --no-auto-start)
                PRESET_AUTO_START="false"
                shift
                ;;
            *)
                break
                ;;
        esac
    done
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"

    case "${1:-install}" in
        --install|install)
            echo -e "${BLUE}==================== 路由优先级管理器安装 ====================${NC}"
            check_root
            check_requirements
            create_systemd_files
            create_config_file
            install_main_script
            modify_main_script
            configuration_wizard
            enable_service
            show_installation_result
            ;;
        --install-auto)
            echo -e "${BLUE}==================== 自动安装模式 ====================${NC}"
            PRESET_CONFIG_MODE="auto"
            PRESET_BASE_METRIC="${PRESET_BASE_METRIC:-10}"
            PRESET_AUTO_START="${PRESET_AUTO_START:-true}"
            check_root
            check_requirements
            create_systemd_files
            create_config_file
            install_main_script
            modify_main_script
            configuration_wizard
            enable_service
            show_installation_result
            ;;
        --install-manual)
            if [ -z "$2" ]; then
                log_message "ERROR" "请指定网卡名称"
                echo "用法: $0 --install-manual <网卡名称>"
                exit 1
            fi
            echo -e "${BLUE}==================== 手动安装模式 ====================${NC}"
            PRESET_CONFIG_MODE="manual"
            PRESET_INTERFACES="$2"
            PRESET_BASE_METRIC="${PRESET_BASE_METRIC:-10}"
            PRESET_AUTO_START="${PRESET_AUTO_START:-true}"
            check_root
            check_requirements
            create_systemd_files
            create_config_file
            install_main_script
            modify_main_script
            configuration_wizard
            enable_service
            show_installation_result
            ;;
        --uninstall|uninstall)
            check_root
            uninstall
            ;;
        --help|help|-h)
            show_help
            ;;
        *)
            log_message "ERROR" "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
