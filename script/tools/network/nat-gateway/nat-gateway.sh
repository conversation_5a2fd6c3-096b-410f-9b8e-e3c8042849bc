#!/bin/bash

# =============================================================================
# 智能NAT网关主控制脚本
# 功能: 统一管理NAT网关的所有功能
# 版本: 1.0
# =============================================================================

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检测是否为系统安装版本
if [[ "$0" == "/usr/local/bin/nat-gateway" ]]; then
    # 系统安装版本，使用安装的脚本路径
    INSTALL_DIR="/usr/local/bin"
    SOURCE_DIR="/usr/local/share/nat-gateway"

    # 如果源码目录存在，优先使用源码目录的脚本
    if [[ -d "$SOURCE_DIR" ]]; then
        SCRIPT_DIR="$SOURCE_DIR"
    else
        SCRIPT_DIR="$INSTALL_DIR"
    fi
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    智能NAT网关管理系统                        ║
║                  Intelligent NAT Gateway                    ║
╠══════════════════════════════════════════════════════════════╣
║  功能: 为局域网设备提供智能网络地址转换服务                    ║
║  特性: 自动检测、智能配置、服务管理、状态监控                  ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 显示主菜单
show_menu() {
    echo -e "${BLUE}=== 主要功能 ===${NC}"
    echo -e "${GREEN}1.${NC} NAT服务管理"
    echo -e "   ${YELLOW}start${NC}    - 启动NAT服务"
    echo -e "   ${YELLOW}stop${NC}     - 停止NAT服务"
    echo -e "   ${YELLOW}restart${NC}  - 重启NAT服务"
    echo -e "   ${YELLOW}status${NC}   - 查看服务状态"
    echo
    echo -e "${GREEN}2.${NC} 外网接口管理 🆕"
    echo -e "   ${YELLOW}list-wan${NC}     - 列出可用外网接口"
    echo -e "   ${YELLOW}auto-wan${NC}     - 自动选择最佳外网接口"
    echo -e "   ${YELLOW}switch-wan${NC}   - 手动切换外网接口"
    echo -e "   ${YELLOW}force-wan${NC}    - 强制切换外网接口"
    echo
    echo -e "${GREEN}3.${NC} 系统管理"
    echo -e "   ${YELLOW}install${NC}  - 安装NAT网关服务"
    echo -e "   ${YELLOW}remove${NC}   - 卸载NAT网关服务"
    echo -e "   ${YELLOW}config${NC}   - 配置管理"
    echo
    echo -e "${GREEN}4.${NC} 监控和测试"
    echo -e "   ${YELLOW}monitor${NC}  - 实时监控"
    echo -e "   ${YELLOW}test${NC}     - 网络连通性测试"
    echo -e "   ${YELLOW}logs${NC}     - 查看日志"
    echo
    echo -e "${GREEN}5.${NC} 工具"
    echo -e "   ${YELLOW}backup${NC}   - 备份配置"
    echo -e "   ${YELLOW}restore${NC}  - 恢复配置"
    echo -e "   ${YELLOW}reset${NC}    - 重置配置"
    echo
    echo -e "${GREEN}6.${NC} 帮助"
    echo -e "   ${YELLOW}help${NC}     - 显示帮助信息"
    echo -e "   ${YELLOW}version${NC}  - 显示版本信息"
    echo
}

# 执行NAT服务操作
execute_nat_service() {
    local action=$1
    local param=$2
    local nat_script="$SCRIPT_DIR/nat-service.sh"

    if [[ ! -f "$nat_script" ]]; then
        echo -e "${RED}错误: NAT服务脚本不存在: $nat_script${NC}"
        return 1
    fi

    echo -e "${BLUE}执行NAT服务操作: $action${NC}"
    if [[ -n $param ]]; then
        sudo "$nat_script" "$action" "$param"
    else
        sudo "$nat_script" "$action"
    fi
}

# 执行外网接口管理操作
execute_wan_management() {
    local action=$1
    local interface=$2
    local nat_script="$SCRIPT_DIR/nat-service.sh"

    if [[ ! -f "$nat_script" ]]; then
        echo -e "${RED}错误: NAT服务脚本不存在: $nat_script${NC}"
        return 1
    fi

    case "$action" in
        "list")
            echo -e "${BLUE}列出可用的外网接口...${NC}"
            sudo "$nat_script" "list-wan"
            ;;
        "auto")
            echo -e "${BLUE}自动选择最佳外网接口...${NC}"
            sudo "$nat_script" "auto-wan"
            ;;
        "switch")
            if [[ -n $interface ]]; then
                echo -e "${BLUE}切换到外网接口: $interface${NC}"
                sudo "$nat_script" "switch-wan" "$interface"
            else
                echo -e "${BLUE}交互式选择外网接口...${NC}"
                sudo "$nat_script" "switch-wan"
            fi
            ;;
        "force")
            if [[ -z $interface ]]; then
                echo -e "${RED}错误: 请指定要强制切换的外网接口${NC}"
                return 1
            fi
            echo -e "${BLUE}强制切换到外网接口: $interface${NC}"
            sudo "$nat_script" "force-wan" "$interface"
            ;;
        *)
            echo -e "${RED}错误: 未知的外网接口管理操作: $action${NC}"
            return 1
            ;;
    esac
}

# 执行安装操作
execute_install() {
    local install_script="$SCRIPT_DIR/install.sh"
    
    if [[ ! -f "$install_script" ]]; then
        echo -e "${RED}错误: 安装脚本不存在: $install_script${NC}"
        return 1
    fi
    
    echo -e "${BLUE}执行系统安装...${NC}"
    sudo "$install_script" install
}

# 执行卸载操作
execute_remove() {
    local install_script="$SCRIPT_DIR/install.sh"
    
    if [[ ! -f "$install_script" ]]; then
        echo -e "${RED}错误: 安装脚本不存在: $install_script${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}确认要卸载NAT网关服务吗? [y/N]: ${NC}"
    read -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}执行系统卸载...${NC}"
        sudo "$install_script" uninstall
    else
        echo -e "${YELLOW}取消卸载操作${NC}"
    fi
}

# 配置管理
manage_config() {
    # 尝试多个可能的配置脚本位置
    local config_locations=(
        "$SCRIPT_DIR/config.sh"
        "/usr/local/share/nat-gateway/config.sh"
        "$(dirname "$0")/config.sh"
        "./config.sh"
    )

    local config_script=""
    for location in "${config_locations[@]}"; do
        if [[ -f "$location" ]]; then
            config_script="$location"
            break
        fi
    done

    if [[ -z "$config_script" ]]; then
        echo -e "${RED}错误: 配置脚本不存在，尝试了以下位置:${NC}"
        for location in "${config_locations[@]}"; do
            echo -e "  ${YELLOW}$location${NC}"
        done
        echo -e "${BLUE}提示: 请确保在NAT网关源码目录中运行，或重新安装系统${NC}"
        return 1
    fi

    "$config_script"
}

# 实时监控
start_monitor() {
    # 尝试多个可能的监控脚本位置
    local monitor_locations=(
        "$SCRIPT_DIR/monitor.sh"
        "/usr/local/share/nat-gateway/monitor.sh"
        "$(dirname "$0")/monitor.sh"
        "./monitor.sh"
    )

    local monitor_script=""
    for location in "${monitor_locations[@]}"; do
        if [[ -f "$location" ]]; then
            monitor_script="$location"
            break
        fi
    done

    if [[ -z "$monitor_script" ]]; then
        echo -e "${RED}错误: 监控脚本不存在，尝试了以下位置:${NC}"
        for location in "${monitor_locations[@]}"; do
            echo -e "  ${YELLOW}$location${NC}"
        done
        echo -e "${BLUE}提示: 请确保在NAT网关源码目录中运行，或重新安装系统${NC}"
        return 1
    fi

    "$monitor_script"
}

# 网络测试
run_test() {
    # 尝试多个可能的测试脚本位置
    local test_locations=(
        "$SCRIPT_DIR/test.sh"
        "/usr/local/share/nat-gateway/test.sh"
        "$(dirname "$0")/test.sh"
        "./test.sh"
    )

    local test_script=""
    for location in "${test_locations[@]}"; do
        if [[ -f "$location" ]]; then
            test_script="$location"
            break
        fi
    done

    if [[ -z "$test_script" ]]; then
        echo -e "${RED}错误: 测试脚本不存在，尝试了以下位置:${NC}"
        for location in "${test_locations[@]}"; do
            echo -e "  ${YELLOW}$location${NC}"
        done
        echo -e "${BLUE}提示: 请确保在NAT网关源码目录中运行，或重新安装系统${NC}"
        return 1
    fi

    "$test_script"
}

# 查看日志
view_logs() {
    local log_file="/var/log/iptables-nat.log"
    
    if [[ -f "$log_file" ]]; then
        echo -e "${BLUE}NAT网关日志 (最近50行):${NC}"
        tail -50 "$log_file"
    else
        echo -e "${YELLOW}日志文件不存在: $log_file${NC}"
    fi
}

# 查找备份脚本
find_backup_script() {
    local backup_locations=(
        "$SCRIPT_DIR/backup.sh"
        "/usr/local/share/nat-gateway/backup.sh"
        "$(dirname "$0")/backup.sh"
        "./backup.sh"
    )

    for location in "${backup_locations[@]}"; do
        if [[ -f "$location" ]]; then
            echo "$location"
            return 0
        fi
    done

    return 1
}

# 备份配置
backup_config() {
    local backup_script=$(find_backup_script)

    if [[ -z "$backup_script" ]]; then
        echo -e "${RED}错误: 备份脚本不存在${NC}"
        echo -e "${BLUE}提示: 请确保在NAT网关源码目录中运行，或重新安装系统${NC}"
        return 1
    fi

    "$backup_script" backup
}

# 恢复配置
restore_config() {
    local backup_script=$(find_backup_script)

    if [[ -z "$backup_script" ]]; then
        echo -e "${RED}错误: 备份脚本不存在${NC}"
        echo -e "${BLUE}提示: 请确保在NAT网关源码目录中运行，或重新安装系统${NC}"
        return 1
    fi

    "$backup_script" restore
}

# 重置配置
reset_config() {
    echo -e "${YELLOW}警告: 这将重置所有NAT配置到默认状态!${NC}"
    echo -e "${YELLOW}确认要继续吗? [y/N]: ${NC}"
    read -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        execute_nat_service "stop"
        echo -e "${GREEN}配置已重置${NC}"
    else
        echo -e "${YELLOW}取消重置操作${NC}"
    fi
}

# 显示版本信息
show_version() {
    echo -e "${BLUE}智能NAT网关管理系统${NC}"
    echo -e "版本: 1.0"
    echo -e "作者: 网络工具集"
    echo -e "更新: $(date '+%Y-%m-%d')"
}

# 显示帮助信息
show_help() {
    show_banner
    show_menu

    echo -e "${BLUE}=== 使用示例 ===${NC}"
    echo -e "启动NAT服务:         ${YELLOW}$0 start${NC}"
    echo -e "查看服务状态:        ${YELLOW}$0 status${NC}"
    echo -e "安装系统服务:        ${YELLOW}$0 install${NC}"
    echo -e "实时监控:           ${YELLOW}$0 monitor${NC}"
    echo -e "网络测试:           ${YELLOW}$0 test${NC}"
    echo
    echo -e "${BLUE}=== 外网接口管理示例 🆕 ===${NC}"
    echo -e "列出外网接口:        ${YELLOW}$0 list-wan${NC}"
    echo -e "自动选择最佳接口:     ${YELLOW}$0 auto-wan${NC}"
    echo -e "交互式切换接口:      ${YELLOW}$0 switch-wan${NC}"
    echo -e "切换到指定接口:      ${YELLOW}$0 switch-wan eth0${NC}"
    echo -e "强制切换接口:        ${YELLOW}$0 force-wan wlan0${NC}"
    echo
    echo -e "${BLUE}=== 配置文件 ===${NC}"
    echo -e "主配置: /etc/network/iptables-nat.conf"
    echo -e "日志文件: /var/log/iptables-nat.log"
    echo
    echo -e "${BLUE}=== 外网接口切换说明 ===${NC}"
    echo -e "• ${GREEN}自动模式${NC}: 根据连通性、接口类型、速度等因素自动选择"
    echo -e "• ${GREEN}手动模式${NC}: 用户交互式选择或直接指定接口"
    echo -e "• ${GREEN}强制模式${NC}: 跳过连通性检查，强制切换到指定接口"
    echo -e "• ${GREEN}评分机制${NC}: 有线网络优先，连通性好的接口优先"
    echo
}

# 主函数
main() {
    local action=${1:-help}
    local param=$2

    case "$action" in
        # NAT服务管理
        "start"|"stop"|"restart"|"status")
            execute_nat_service "$action"
            ;;

        # 外网接口管理
        "list-wan")
            execute_wan_management "list"
            ;;
        "auto-wan")
            execute_wan_management "auto"
            ;;
        "switch-wan")
            execute_wan_management "switch" "$param"
            ;;
        "force-wan")
            execute_wan_management "force" "$param"
            ;;

        # 系统管理
        "install")
            execute_install
            ;;
        "remove"|"uninstall")
            execute_remove
            ;;
        "config")
            manage_config
            ;;

        # 监控和测试
        "monitor")
            start_monitor
            ;;
        "test")
            run_test
            ;;
        "logs")
            view_logs
            ;;

        # 工具
        "backup")
            backup_config
            ;;
        "restore")
            restore_config
            ;;
        "reset")
            reset_config
            ;;

        # 帮助和信息
        "help"|"-h"|"--help")
            show_help
            ;;
        "version"|"-v"|"--version")
            show_version
            ;;

        # 交互模式
        "menu"|"")
            show_banner
            show_menu
            ;;

        *)
            echo -e "${RED}错误: 未知选项 '$action'${NC}"
            echo -e "使用 '${YELLOW}$0 help${NC}' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
