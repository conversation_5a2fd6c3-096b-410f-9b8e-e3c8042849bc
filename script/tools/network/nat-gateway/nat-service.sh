#!/bin/bash

# =============================================================================
# 智能网关 iptables NAT 配置脚本
# 功能: 为局域网设备提供网络地址转换(NAT)服务，实现共享上网
# 作者: 网络工具集
# 版本: 2.0
# =============================================================================

# 配置文件路径
CONFIG_FILE="/etc/network/iptables-nat.conf"
LOG_FILE="/var/log/iptables-nat.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

# 成功信息函数
success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

# 警告信息函数
warning_message() {
    log_message "WARN" "$1"
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 信息函数
info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此脚本需要root权限运行，请使用 sudo"
    fi
}

# 检查iptables是否安装
check_iptables() {
    if ! command -v iptables &> /dev/null; then
        error_exit "iptables 未安装，请先安装 iptables"
    fi
}

# 自动检测网络接口
detect_interfaces() {
    local wan_interfaces=()
    local lan_networks=()

    # 检测所有活动的网络接口
    for interface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo); do
        # 检查接口是否有IP地址
        if ip addr show "$interface" | grep -q "inet "; then
            local ip_info=$(ip addr show "$interface" | grep "inet " | head -1)
            local ip=$(echo "$ip_info" | awk '{print $2}' | cut -d'/' -f1)
            local network=$(echo "$ip_info" | awk '{print $2}')

            # 判断是否为外网接口（通常有默认路由）
            if ip route show default | grep -q "$interface"; then
                wan_interfaces+=("$interface")
                info_message "检测到外网接口: $interface (IP: $ip)"
            else
                # 判断是否为内网网段
                if [[ $ip =~ ^192\.168\. ]] || [[ $ip =~ ^10\. ]] || [[ $ip =~ ^172\.(1[6-9]|2[0-9]|3[0-1])\. ]]; then
                    lan_networks+=("$network")
                    info_message "检测到内网网段: $network (接口: $interface)"
                fi
            fi
        fi
    done

    echo "${wan_interfaces[@]}" > /tmp/wan_interfaces
    echo "${lan_networks[@]}" > /tmp/lan_networks
}

# 获取所有可用的外网接口
get_available_wan_interfaces() {
    local available_interfaces=()

    # 检测所有活动的网络接口
    for interface in $(ip link show | grep -E '^[0-9]+:' | cut -d: -f2 | tr -d ' ' | grep -v lo); do
        # 检查接口是否有IP地址且状态为UP
        if ip addr show "$interface" | grep -q "inet " && ip link show "$interface" | grep -q "state UP"; then
            local ip_info=$(ip addr show "$interface" | grep "inet " | head -1)
            local ip=$(echo "$ip_info" | awk '{print $2}' | cut -d'/' -f1)

            # 排除明显的内网接口
            if [[ ! ($ip =~ ^192\.168\. && $(ip route show | grep "$interface" | grep -v default | wc -l) -gt 0) ]]; then
                available_interfaces+=("$interface")
            fi
        fi
    done

    echo "${available_interfaces[@]}"
}

# 检测外网接口连通性
test_interface_connectivity() {
    local interface=$1
    local test_hosts=("*******" "*******" "***************")
    local success_count=0

    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 2 -I "$interface" "$host" &> /dev/null; then
            success_count=$((success_count + 1))
        fi
    done

    # 如果至少有一个测试主机可达，认为接口可用
    if [[ $success_count -gt 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 评估外网接口质量
evaluate_interface_quality() {
    local interface=$1
    local score=0

    # 检查是否有默认路由 (权重: 40)
    if ip route show default | grep -q "$interface"; then
        score=$((score + 40))
    fi

    # 检查连通性 (权重: 30)
    if test_interface_connectivity "$interface"; then
        score=$((score + 30))
    fi

    # 检查接口类型优先级 (权重: 20)
    if [[ $interface =~ ^eth ]]; then
        score=$((score + 20))  # 有线网络优先级最高
    elif [[ $interface =~ ^enp ]]; then
        score=$((score + 18))  # 新式有线网络命名
    elif [[ $interface =~ ^ens ]]; then
        score=$((score + 16))  # 另一种有线网络命名
    elif [[ $interface =~ ^wlan ]]; then
        score=$((score + 10))  # 无线网络
    elif [[ $interface =~ ^wwan ]]; then
        score=$((score + 5))   # 移动网络
    fi

    # 检查接口速度 (权重: 10)
    local speed=$(cat "/sys/class/net/$interface/speed" 2>/dev/null || echo "0")
    if [[ $speed -ge 1000 ]]; then
        score=$((score + 10))  # 千兆及以上
    elif [[ $speed -ge 100 ]]; then
        score=$((score + 8))   # 百兆
    elif [[ $speed -gt 0 ]]; then
        score=$((score + 5))   # 其他有速度信息的接口
    fi

    echo "$score"
}

# 自动选择最佳外网接口
auto_select_wan_interface() {
    local available_interfaces=($(get_available_wan_interfaces))
    local best_interface=""
    local best_score=0

    if [[ ${#available_interfaces[@]} -eq 0 ]]; then
        warning_message "未找到可用的外网接口"
        return 1
    fi

    info_message "评估可用的外网接口..."

    for interface in "${available_interfaces[@]}"; do
        local score=$(evaluate_interface_quality "$interface")
        local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)

        info_message "接口 $interface (IP: $ip) 评分: $score"

        if [[ $score -gt $best_score ]]; then
            best_score=$score
            best_interface=$interface
        fi
    done

    if [[ -n $best_interface ]]; then
        success_message "自动选择最佳外网接口: $best_interface (评分: $best_score)"
        echo "$best_interface"
        return 0
    else
        warning_message "无法确定最佳外网接口"
        return 1
    fi
}

# 安全删除iptables规则
delete_rule() {
    local table=$1
    local chain=$2
    local rule=$3

    # 检查规则是否存在并删除
    while iptables -t "$table" -C "$chain" $rule &> /dev/null; do
        iptables -t "$table" -D "$chain" $rule
        log_message "INFO" "删除规则: iptables -t $table -D $chain $rule"
    done
}

# 安全添加iptables规则
add_rule() {
    local table=$1
    local chain=$2
    local rule=$3

    # 检查规则是否已存在
    if ! iptables -t "$table" -C "$chain" $rule &> /dev/null; then
        iptables -t "$table" -A "$chain" $rule
        log_message "INFO" "添加规则: iptables -t $table -A $chain $rule"
        return 0
    else
        log_message "WARN" "规则已存在: iptables -t $table -A $chain $rule"
        return 1
    fi
}

# 清理所有NAT规则
cleanup_nat_rules() {
    info_message "清理现有的NAT规则..."

    # 清理POSTROUTING链中的MASQUERADE规则
    iptables -t nat -S POSTROUTING | grep MASQUERADE | while read -r line; do
        # 提取规则参数
        rule=$(echo "$line" | sed 's/^-A POSTROUTING //')
        delete_rule "nat" "POSTROUTING" "$rule"
    done

    # 清理FORWARD链中的相关规则
    iptables -S FORWARD | grep -E "(192\.168\.|10\.|172\.)" | while read -r line; do
        if [[ $line == "-A"* ]]; then
            rule=$(echo "$line" | sed 's/^-A FORWARD //')
            delete_rule "filter" "FORWARD" "$rule"
        fi
    done

    success_message "NAT规则清理完成"
}

# 启用IP转发
enable_ip_forward() {
    info_message "启用IP转发..."

    # 临时启用
    echo 1 > /proc/sys/net/ipv4/ip_forward

    # 永久启用
    if ! grep -q "^net.ipv4.ip_forward=1" /etc/sysctl.conf; then
        echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
        success_message "已永久启用IP转发"
    else
        info_message "IP转发已经启用"
    fi
}

# 手动选择外网接口
manual_select_wan_interface() {
    local available_interfaces=($(get_available_wan_interfaces))

    if [[ ${#available_interfaces[@]} -eq 0 ]]; then
        error_exit "未找到可用的外网接口"
    fi

    echo -e "\n${BLUE}可用的外网接口:${NC}"
    for i in "${!available_interfaces[@]}"; do
        local interface="${available_interfaces[$i]}"
        local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
        local status="未知"

        # 检查连通性
        if test_interface_connectivity "$interface"; then
            status="${GREEN}可用${NC}"
        else
            status="${YELLOW}不可用${NC}"
        fi

        # 检查是否为当前默认接口
        local is_default=""
        if ip route show default | grep -q "$interface"; then
            is_default=" ${CYAN}[当前默认]${NC}"
        fi

        echo -e "  ${YELLOW}$((i+1)).${NC} $interface (IP: $ip) - $status$is_default"
    done

    echo -e "\n${YELLOW}请选择外网接口 [1-${#available_interfaces[@]}]: ${NC}"
    read -r choice

    if [[ $choice =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#available_interfaces[@]} ]]; then
        local selected_interface="${available_interfaces[$((choice-1))]}"
        success_message "选择外网接口: $selected_interface"
        echo "$selected_interface"
        return 0
    else
        error_exit "无效的选择"
    fi
}

# 切换外网接口
switch_wan_interface() {
    local new_interface=$1
    local force=${2:-false}

    if [[ -z $new_interface ]]; then
        error_exit "未指定外网接口"
    fi

    # 检查接口是否存在且可用
    if ! ip link show "$new_interface" &> /dev/null; then
        error_exit "接口 $new_interface 不存在"
    fi

    if ! ip addr show "$new_interface" | grep -q "inet "; then
        error_exit "接口 $new_interface 没有IP地址"
    fi

    # 检查连通性（除非强制切换）
    if [[ $force != "true" ]] && ! test_interface_connectivity "$new_interface"; then
        warning_message "接口 $new_interface 连通性测试失败"
        echo -e "${YELLOW}是否强制切换? [y/N]: ${NC}"
        read -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info_message "取消切换操作"
            return 1
        fi
    fi

    info_message "切换外网接口到: $new_interface"

    # 备份当前配置
    backup_current_config

    # 清理现有NAT规则
    cleanup_nat_rules

    # 设置新的外网接口
    echo "$new_interface" > /tmp/wan_interfaces

    # 重新配置NAT规则
    configure_nat_rules

    # 更新默认路由（如果需要）
    update_default_route "$new_interface"

    success_message "外网接口切换完成: $new_interface"
}

# 更新默认路由
update_default_route() {
    local interface=$1
    local gateway=""

    # 获取接口的网关
    gateway=$(ip route show dev "$interface" | grep default | awk '{print $3}' | head -1)

    if [[ -z $gateway ]]; then
        # 尝试从同网段推测网关
        local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
        local network=$(ip route show dev "$interface" | grep -v default | head -1 | awk '{print $1}')

        if [[ -n $network ]]; then
            # 通常网关是网段的第一个IP
            gateway=$(echo "$network" | sed 's/\.[0-9]*\/.*/.1/')
        fi
    fi

    if [[ -n $gateway ]]; then
        info_message "更新默认路由: $gateway via $interface"

        # 删除现有默认路由
        ip route del default 2>/dev/null || true

        # 添加新的默认路由
        ip route add default via "$gateway" dev "$interface"

        success_message "默认路由已更新"
    else
        warning_message "无法确定接口 $interface 的网关"
    fi
}

# 备份当前配置
backup_current_config() {
    local backup_file="/tmp/nat_config_backup_$(date +%s)"

    {
        echo "# NAT配置备份 - $(date)"
        echo "# 当前外网接口"
        cat /tmp/wan_interfaces 2>/dev/null || echo ""
        echo "# 当前内网网段"
        cat /tmp/lan_networks 2>/dev/null || echo ""
        echo "# 当前iptables规则"
        iptables-save
    } > "$backup_file"

    info_message "当前配置已备份到: $backup_file"
}

# 配置NAT规则
configure_nat_rules() {
    local wan_interfaces=($(cat /tmp/wan_interfaces 2>/dev/null))
    local lan_networks=($(cat /tmp/lan_networks 2>/dev/null))

    if [[ ${#wan_interfaces[@]} -eq 0 ]]; then
        error_exit "未检测到外网接口"
    fi

    if [[ ${#lan_networks[@]} -eq 0 ]]; then
        error_exit "未检测到内网网段"
    fi

    info_message "配置NAT规则..."

    # 为每个内网网段配置到每个外网接口的NAT
    for lan_network in "${lan_networks[@]}"; do
        for wan_interface in "${wan_interfaces[@]}"; do
            # 添加MASQUERADE规则
            add_rule "nat" "POSTROUTING" "-s $lan_network -o $wan_interface -j MASQUERADE"

            # 添加FORWARD规则允许内网到外网的流量
            add_rule "filter" "FORWARD" "-s $lan_network -o $wan_interface -j ACCEPT"

            # 添加FORWARD规则允许外网到内网的已建立连接
            add_rule "filter" "FORWARD" "-i $wan_interface -d $lan_network -m state --state RELATED,ESTABLISHED -j ACCEPT"
        done

        # 添加内网网段之间的通信规则（如果有多个内网）
        for other_network in "${lan_networks[@]}"; do
            if [[ "$lan_network" != "$other_network" ]]; then
                add_rule "filter" "FORWARD" "-s $lan_network -d $other_network -j ACCEPT"
            fi
        done
    done

    success_message "NAT规则配置完成"
}

# 显示当前配置
show_current_config() {
    echo -e "\n${BLUE}=== 当前网络配置 ===${NC}"

    # 显示IP转发状态
    local ip_forward=$(cat /proc/sys/net/ipv4/ip_forward)
    if [[ $ip_forward -eq 1 ]]; then
        echo -e "${GREEN}✓ IP转发: 已启用${NC}"
    else
        echo -e "${RED}✗ IP转发: 未启用${NC}"
    fi

    # 显示网络接口
    echo -e "\n${BLUE}网络接口:${NC}"
    ip addr show | grep -E "^[0-9]+:|inet " | while read -r line; do
        if [[ $line =~ ^[0-9]+: ]]; then
            interface=$(echo "$line" | cut -d: -f2 | tr -d ' ')
            echo -e "  ${YELLOW}$interface${NC}"
        elif [[ $line =~ inet ]]; then
            ip_info=$(echo "$line" | awk '{print $2}')
            echo -e "    IP: $ip_info"
        fi
    done

    # 显示NAT规则
    echo -e "\n${BLUE}NAT规则 (POSTROUTING):${NC}"
    iptables -t nat -L POSTROUTING -n --line-numbers | grep -v "^Chain\|^num" | while read -r line; do
        if [[ -n $line ]]; then
            echo -e "  $line"
        fi
    done

    # 显示FORWARD规则
    echo -e "\n${BLUE}转发规则 (FORWARD):${NC}"
    iptables -L FORWARD -n --line-numbers | grep -v "^Chain\|^num" | while read -r line; do
        if [[ -n $line ]]; then
            echo -e "  $line"
        fi
    done

    # 显示路由表
    echo -e "\n${BLUE}路由表:${NC}"
    ip route show | while read -r line; do
        echo -e "  $line"
    done
}

# 保存配置到文件
save_config() {
    info_message "保存配置到 $CONFIG_FILE..."

    cat > "$CONFIG_FILE" << EOF
# iptables NAT 配置文件
# 生成时间: $(date)

# 检测到的外网接口
WAN_INTERFACES="$(cat /tmp/wan_interfaces 2>/dev/null | tr '\n' ' ')"

# 检测到的内网网段
LAN_NETWORKS="$(cat /tmp/lan_networks 2>/dev/null | tr '\n' ' ')"

# IP转发状态
IP_FORWARD_ENABLED=1

# 自动检测模式
AUTO_DETECT=1
EOF

    success_message "配置已保存到 $CONFIG_FILE"
}

# 从配置文件加载
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        info_message "从 $CONFIG_FILE 加载配置"
        return 0
    else
        warning_message "配置文件 $CONFIG_FILE 不存在，将使用自动检测"
        return 1
    fi
}

# 测试网络连通性
test_connectivity() {
    info_message "测试网络连通性..."

    # 测试外网连接
    if ping -c 3 ******* &> /dev/null; then
        success_message "外网连接正常"
    else
        warning_message "外网连接异常，请检查网络配置"
    fi

    # 测试DNS解析
    if nslookup google.com &> /dev/null; then
        success_message "DNS解析正常"
    else
        warning_message "DNS解析异常，请检查DNS配置"
    fi
}

# 列出可用的外网接口
list_wan_interfaces() {
    echo -e "${BLUE}=== 可用的外网接口 ===${NC}"

    local available_interfaces=($(get_available_wan_interfaces))

    if [[ ${#available_interfaces[@]} -eq 0 ]]; then
        warning_message "未找到可用的外网接口"
        return 1
    fi

    for interface in "${available_interfaces[@]}"; do
        local ip=$(ip addr show "$interface" | grep "inet " | head -1 | awk '{print $2}' | cut -d'/' -f1)
        local score=$(evaluate_interface_quality "$interface")
        local status="未知"

        # 检查连通性
        if test_interface_connectivity "$interface"; then
            status="${GREEN}可用${NC}"
        else
            status="${YELLOW}不可用${NC}"
        fi

        # 检查是否为当前默认接口
        local is_default=""
        if ip route show default | grep -q "$interface"; then
            is_default=" ${CYAN}[当前默认]${NC}"
        fi

        # 检查是否在NAT规则中
        local in_nat=""
        if iptables -t nat -L POSTROUTING -n | grep -q "$interface"; then
            in_nat=" ${GREEN}[NAT活动]${NC}"
        fi

        echo -e "${CYAN}$interface${NC}: IP=$ip, 评分=$score, 状态=$status$is_default$in_nat"
    done

    echo
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}智能网关 iptables NAT 配置脚本${NC}

${YELLOW}用法:${NC}
    $0 [选项] [参数]

${YELLOW}基本选项:${NC}
    start           启动NAT服务（默认操作）
    stop            停止NAT服务，清理所有规则
    restart         重启NAT服务
    status          显示当前配置状态
    test            测试网络连通性
    save            保存当前配置
    load            从配置文件加载
    help            显示此帮助信息

${YELLOW}外网接口管理:${NC}
    list-wan        列出所有可用的外网接口
    auto-wan        自动选择最佳外网接口
    switch-wan      手动选择并切换外网接口
    switch-wan <接口>  切换到指定的外网接口
    force-wan <接口>   强制切换到指定接口（跳过连通性检查）

${YELLOW}示例:${NC}
    $0                      # 启动NAT服务
    $0 start                # 启动NAT服务
    $0 stop                 # 停止NAT服务
    $0 status               # 查看状态
    $0 test                 # 测试连通性
    $0 list-wan             # 列出外网接口
    $0 auto-wan             # 自动选择最佳外网接口
    $0 switch-wan           # 交互式选择外网接口
    $0 switch-wan eth0      # 切换到eth0接口
    $0 force-wan wlan0      # 强制切换到wlan0接口

${YELLOW}说明:${NC}
    - 脚本会自动检测网络接口和内网网段
    - 支持多个外网接口和内网网段
    - 支持外网接口的自动和手动切换
    - 自动启用IP转发功能
    - 所有操作都会记录到日志文件: $LOG_FILE

${YELLOW}外网接口评分标准:${NC}
    - 有默认路由: +40分
    - 连通性良好: +30分
    - 有线网络(eth*): +20分
    - 新式有线(enp*): +18分
    - 另一种有线(ens*): +16分
    - 无线网络(wlan*): +10分
    - 移动网络(wwan*): +5分
    - 千兆速度: +10分
    - 百兆速度: +8分

${YELLOW}配置文件:${NC}
    $CONFIG_FILE

EOF
}

# 主函数
main() {
    local action=${1:-start}
    local param=$2

    # 检查权限和依赖
    check_root
    check_iptables

    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "$action" in
        "start")
            info_message "启动智能网关NAT服务..."
            detect_interfaces
            enable_ip_forward
            cleanup_nat_rules
            configure_nat_rules
            save_config
            success_message "NAT服务启动完成"
            ;;
        "stop")
            info_message "停止NAT服务..."
            cleanup_nat_rules
            success_message "NAT服务已停止"
            ;;
        "restart")
            info_message "重启NAT服务..."
            cleanup_nat_rules
            detect_interfaces
            enable_ip_forward
            configure_nat_rules
            save_config
            success_message "NAT服务重启完成"
            ;;
        "status")
            show_current_config
            ;;
        "test")
            test_connectivity
            ;;
        "save")
            detect_interfaces
            save_config
            ;;
        "load")
            load_config
            ;;
        "list-wan")
            list_wan_interfaces
            ;;
        "auto-wan")
            info_message "自动选择最佳外网接口..."
            detect_interfaces  # 先检测内网
            local best_wan=$(auto_select_wan_interface)
            if [[ -n $best_wan ]]; then
                switch_wan_interface "$best_wan"
            else
                error_exit "无法自动选择外网接口"
            fi
            ;;
        "switch-wan")
            detect_interfaces  # 先检测内网
            if [[ -n $param ]]; then
                # 切换到指定接口
                switch_wan_interface "$param"
            else
                # 交互式选择
                local selected_wan=$(manual_select_wan_interface)
                if [[ -n $selected_wan ]]; then
                    switch_wan_interface "$selected_wan"
                else
                    error_exit "未选择外网接口"
                fi
            fi
            ;;
        "force-wan")
            if [[ -z $param ]]; then
                error_exit "请指定要强制切换的外网接口"
            fi
            detect_interfaces  # 先检测内网
            switch_wan_interface "$param" "true"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error_exit "未知选项: $action。使用 '$0 help' 查看帮助信息"
            ;;
    esac

    # 清理临时文件
    rm -f /tmp/wan_interfaces /tmp/lan_networks
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

