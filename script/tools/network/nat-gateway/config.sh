#!/bin/bash

# =============================================================================
# NAT网关配置管理脚本
# 功能: 管理NAT网关的配置文件和参数
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置文件路径
CONFIG_FILE="/etc/network/nat-gateway.conf"
BACKUP_DIR="/etc/network/backups"

# 默认配置
DEFAULT_CONFIG="# NAT网关配置文件
AUTO_DETECT=1
IP_FORWARD_ENABLED=1
LOG_LEVEL=INFO
TEST_CONNECTIVITY=1
BACKUP_RETENTION_DAYS=30
MONITOR_INTERVAL=60
AUTO_RESTART=1"

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

warning_message() {
    log_message "WARN" "$1"
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 检查配置文件是否存在
check_config_file() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        warning_message "配置文件不存在，创建默认配置"
        create_default_config
    fi
}

# 创建默认配置
create_default_config() {
    mkdir -p "$(dirname "$CONFIG_FILE")"
    echo "$DEFAULT_CONFIG" > "$CONFIG_FILE"
    success_message "默认配置文件已创建: $CONFIG_FILE"
}

# 加载配置
load_config() {
    check_config_file
    source "$CONFIG_FILE"
}

# 显示当前配置
show_config() {
    echo -e "${BLUE}=== 当前配置 ===${NC}"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        warning_message "配置文件不存在"
        return 1
    fi
    
    echo -e "${CYAN}配置文件: $CONFIG_FILE${NC}"
    echo
    
    # 读取并显示配置
    while IFS='=' read -r key value; do
        # 跳过注释和空行
        if [[ $key =~ ^#.*$ ]] || [[ -z $key ]]; then
            continue
        fi
        
        # 移除值中的引号
        value=$(echo "$value" | sed 's/^"//;s/"$//')
        
        case "$key" in
            "AUTO_DETECT")
                if [[ $value == "1" ]]; then
                    echo -e "${GREEN}✓${NC} 自动检测网络接口: ${GREEN}启用${NC}"
                else
                    echo -e "${YELLOW}⚠${NC} 自动检测网络接口: ${YELLOW}禁用${NC}"
                fi
                ;;
            "WAN_INTERFACES")
                echo -e "${BLUE}ℹ${NC} 外网接口: ${CYAN}$value${NC}"
                ;;
            "LAN_NETWORKS")
                echo -e "${BLUE}ℹ${NC} 内网网段: ${CYAN}$value${NC}"
                ;;
            "IP_FORWARD_ENABLED")
                if [[ $value == "1" ]]; then
                    echo -e "${GREEN}✓${NC} IP转发: ${GREEN}启用${NC}"
                else
                    echo -e "${RED}✗${NC} IP转发: ${RED}禁用${NC}"
                fi
                ;;
            "LOG_LEVEL")
                echo -e "${BLUE}ℹ${NC} 日志级别: ${CYAN}$value${NC}"
                ;;
            "TEST_CONNECTIVITY")
                if [[ $value == "1" ]]; then
                    echo -e "${GREEN}✓${NC} 连通性测试: ${GREEN}启用${NC}"
                else
                    echo -e "${YELLOW}⚠${NC} 连通性测试: ${YELLOW}禁用${NC}"
                fi
                ;;
            "BACKUP_RETENTION_DAYS")
                echo -e "${BLUE}ℹ${NC} 备份保留天数: ${CYAN}$value${NC}"
                ;;
            "MONITOR_INTERVAL")
                echo -e "${BLUE}ℹ${NC} 监控间隔: ${CYAN}$value 秒${NC}"
                ;;
            "AUTO_RESTART")
                if [[ $value == "1" ]]; then
                    echo -e "${GREEN}✓${NC} 自动重启: ${GREEN}启用${NC}"
                else
                    echo -e "${YELLOW}⚠${NC} 自动重启: ${YELLOW}禁用${NC}"
                fi
                ;;
            *)
                echo -e "${BLUE}ℹ${NC} $key: ${CYAN}$value${NC}"
                ;;
        esac
    done < "$CONFIG_FILE"
    
    echo
}

# 编辑配置
edit_config() {
    check_config_file
    
    # 备份当前配置
    backup_config
    
    # 检查编辑器
    local editor="${EDITOR:-nano}"
    if ! command -v "$editor" &> /dev/null; then
        editor="vi"
    fi
    
    info_message "使用 $editor 编辑配置文件..."
    "$editor" "$CONFIG_FILE"
    
    # 验证配置
    if validate_config; then
        success_message "配置文件编辑完成"
    else
        warning_message "配置文件可能有错误，请检查"
    fi
}

# 验证配置
validate_config() {
    check_config_file
    
    info_message "验证配置文件..."
    
    # 尝试加载配置
    if ! source "$CONFIG_FILE" 2>/dev/null; then
        error_exit "配置文件语法错误"
    fi
    
    # 检查必要的配置项
    local errors=0
    
    if [[ -n $AUTO_DETECT ]] && [[ $AUTO_DETECT != "0" ]] && [[ $AUTO_DETECT != "1" ]]; then
        echo -e "${RED}✗ AUTO_DETECT 必须是 0 或 1${NC}"
        ((errors++))
    fi
    
    if [[ -n $IP_FORWARD_ENABLED ]] && [[ $IP_FORWARD_ENABLED != "0" ]] && [[ $IP_FORWARD_ENABLED != "1" ]]; then
        echo -e "${RED}✗ IP_FORWARD_ENABLED 必须是 0 或 1${NC}"
        ((errors++))
    fi
    
    if [[ -n $LOG_LEVEL ]] && [[ ! $LOG_LEVEL =~ ^(DEBUG|INFO|WARN|ERROR)$ ]]; then
        echo -e "${RED}✗ LOG_LEVEL 必须是 DEBUG, INFO, WARN 或 ERROR${NC}"
        ((errors++))
    fi
    
    if [[ -n $BACKUP_RETENTION_DAYS ]] && ! [[ $BACKUP_RETENTION_DAYS =~ ^[0-9]+$ ]]; then
        echo -e "${RED}✗ BACKUP_RETENTION_DAYS 必须是数字${NC}"
        ((errors++))
    fi
    
    if [[ -n $MONITOR_INTERVAL ]] && ! [[ $MONITOR_INTERVAL =~ ^[0-9]+$ ]]; then
        echo -e "${RED}✗ MONITOR_INTERVAL 必须是数字${NC}"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        success_message "配置文件验证通过"
        return 0
    else
        error_exit "配置文件验证失败，发现 $errors 个错误"
    fi
}

# 备份配置
backup_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        warning_message "配置文件不存在，无需备份"
        return 1
    fi
    
    mkdir -p "$BACKUP_DIR"
    local backup_file="$BACKUP_DIR/nat-gateway.conf.$(date +%Y%m%d_%H%M%S)"
    
    cp "$CONFIG_FILE" "$backup_file"
    success_message "配置已备份到: $backup_file"
}

# 恢复配置
restore_config() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        warning_message "备份目录不存在"
        return 1
    fi
    
    echo -e "${BLUE}可用的备份文件:${NC}"
    local backups=($(ls -1t "$BACKUP_DIR"/nat-gateway.conf.* 2>/dev/null))
    
    if [[ ${#backups[@]} -eq 0 ]]; then
        warning_message "没有找到备份文件"
        return 1
    fi
    
    for i in "${!backups[@]}"; do
        local backup_file="${backups[$i]}"
        local backup_name=$(basename "$backup_file")
        local backup_date=$(echo "$backup_name" | sed 's/nat-gateway.conf.//')
        echo -e "${YELLOW}$((i+1)).${NC} $backup_date"
    done
    
    echo -e "${YELLOW}请选择要恢复的备份 [1-${#backups[@]}]: ${NC}"
    read -r choice
    
    if [[ $choice =~ ^[0-9]+$ ]] && [[ $choice -ge 1 ]] && [[ $choice -le ${#backups[@]} ]]; then
        local selected_backup="${backups[$((choice-1))]}"
        
        # 备份当前配置
        backup_config
        
        # 恢复选择的备份
        cp "$selected_backup" "$CONFIG_FILE"
        success_message "配置已从备份恢复: $(basename "$selected_backup")"
        
        # 验证恢复的配置
        validate_config
    else
        warning_message "无效的选择"
    fi
}

# 重置配置
reset_config() {
    echo -e "${YELLOW}警告: 这将重置配置到默认状态!${NC}"
    echo -e "${YELLOW}确认要继续吗? [y/N]: ${NC}"
    read -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 备份当前配置
        backup_config
        
        # 创建默认配置
        create_default_config
        success_message "配置已重置为默认值"
    else
        info_message "取消重置操作"
    fi
}

# 设置配置项
set_config() {
    local key=$1
    local value=$2
    
    if [[ -z $key ]] || [[ -z $value ]]; then
        error_exit "用法: set <配置项> <值>"
    fi
    
    check_config_file
    
    # 备份配置
    backup_config
    
    # 更新配置项
    if grep -q "^$key=" "$CONFIG_FILE"; then
        # 更新现有配置项
        sed -i "s/^$key=.*/$key=$value/" "$CONFIG_FILE"
        success_message "配置项 $key 已更新为: $value"
    else
        # 添加新配置项
        echo "$key=$value" >> "$CONFIG_FILE"
        success_message "配置项 $key 已添加: $value"
    fi
    
    # 验证配置
    validate_config
}

# 显示帮助
show_help() {
    cat << EOF
${BLUE}NAT网关配置管理${NC}

${YELLOW}用法:${NC}
    $0 [命令] [参数]

${YELLOW}命令:${NC}
    show            显示当前配置
    edit            编辑配置文件
    validate        验证配置文件
    backup          备份当前配置
    restore         从备份恢复配置
    reset           重置为默认配置
    set <key> <val> 设置配置项
    help            显示此帮助信息

${YELLOW}配置项说明:${NC}
    AUTO_DETECT             自动检测网络接口 (0/1)
    WAN_INTERFACES          外网接口列表 (空格分隔)
    LAN_NETWORKS            内网网段列表 (空格分隔)
    IP_FORWARD_ENABLED      启用IP转发 (0/1)
    LOG_LEVEL               日志级别 (DEBUG/INFO/WARN/ERROR)
    TEST_CONNECTIVITY       启动时测试连通性 (0/1)
    BACKUP_RETENTION_DAYS   备份保留天数
    MONITOR_INTERVAL        监控间隔(秒)
    AUTO_RESTART            自动重启失败服务 (0/1)

${YELLOW}示例:${NC}
    $0 show                     # 显示当前配置
    $0 edit                     # 编辑配置文件
    $0 set LOG_LEVEL DEBUG      # 设置日志级别为DEBUG
    $0 backup                   # 备份当前配置
    $0 restore                  # 从备份恢复

${YELLOW}配置文件位置:${NC}
    $CONFIG_FILE

EOF
}

# 主函数
main() {
    local command=${1:-show}
    
    case "$command" in
        "show")
            show_config
            ;;
        "edit")
            edit_config
            ;;
        "validate")
            validate_config
            ;;
        "backup")
            backup_config
            ;;
        "restore")
            restore_config
            ;;
        "reset")
            reset_config
            ;;
        "set")
            set_config "$2" "$3"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error_exit "未知命令: $command。使用 '$0 help' 查看帮助信息"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
