#!/bin/bash

# =============================================================================
# NAT网关快速安装脚本
# 功能: 一键安装和配置智能NAT网关系统
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                NAT网关快速安装向导                            ║
║              Quick Install Wizard                           ║
╠══════════════════════════════════════════════════════════════╣
║  一键安装智能NAT网关系统                                      ║
║  自动检测网络环境并配置NAT服务                                ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}"
}

error_exit() {
    log_message "ERROR" "$1"
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

success_message() {
    log_message "INFO" "$1"
    echo -e "${GREEN}✓ $1${NC}"
}

info_message() {
    log_message "INFO" "$1"
    echo -e "${BLUE}ℹ $1${NC}"
}

warning_message() {
    log_message "WARN" "$1"
    echo -e "${YELLOW}⚠ $1${NC}"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此脚本需要root权限运行，请使用 sudo"
    fi
}

# 检查系统环境
check_system() {
    echo -e "${BLUE}=== 检查系统环境 ===${NC}"
    
    # 检查操作系统
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        info_message "操作系统: $PRETTY_NAME"
    else
        warning_message "无法识别操作系统"
    fi
    
    # 检查内核版本
    local kernel_version=$(uname -r)
    info_message "内核版本: $kernel_version"
    
    # 检查必要工具
    local missing_tools=()
    
    for tool in iptables ip systemctl; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        warning_message "缺少工具: ${missing_tools[*]}"
        info_message "将尝试自动安装..."
    else
        success_message "系统环境检查通过"
    fi
    
    echo
}

# 检测网络环境
detect_network() {
    echo -e "${BLUE}=== 检测网络环境 ===${NC}"
    
    # 检测网络接口
    local interfaces=($(ip link show | grep -E '^[0-9]+:' | grep 'state UP' | cut -d: -f2 | tr -d ' ' | grep -v lo))
    
    if [[ ${#interfaces[@]} -eq 0 ]]; then
        error_exit "未检测到活动的网络接口"
    fi
    
    info_message "检测到 ${#interfaces[@]} 个活动网络接口:"
    
    local wan_interfaces=()
    local lan_networks=()
    
    for interface in "${interfaces[@]}"; do
        local ip_info=$(ip addr show "$interface" | grep "inet " | head -1)
        if [[ -n $ip_info ]]; then
            local ip=$(echo "$ip_info" | awk '{print $2}' | cut -d'/' -f1)
            local network=$(echo "$ip_info" | awk '{print $2}')
            
            echo -e "  ${CYAN}$interface${NC}: $network"
            
            # 判断是否为外网接口
            if ip route show default | grep -q "$interface"; then
                wan_interfaces+=("$interface")
                echo -e "    ${GREEN}[外网接口]${NC}"
            else
                # 判断是否为内网网段
                if [[ $ip =~ ^192\.168\. ]] || [[ $ip =~ ^10\. ]] || [[ $ip =~ ^172\.(1[6-9]|2[0-9]|3[0-1])\. ]]; then
                    lan_networks+=("$network")
                    echo -e "    ${YELLOW}[内网网段]${NC}"
                fi
            fi
        fi
    done
    
    echo
    
    if [[ ${#wan_interfaces[@]} -eq 0 ]]; then
        warning_message "未检测到外网接口，NAT功能可能无法正常工作"
    else
        success_message "检测到 ${#wan_interfaces[@]} 个外网接口: ${wan_interfaces[*]}"
    fi
    
    if [[ ${#lan_networks[@]} -eq 0 ]]; then
        warning_message "未检测到内网网段"
    else
        success_message "检测到 ${#lan_networks[@]} 个内网网段: ${lan_networks[*]}"
    fi
    
    echo
}

# 用户确认
user_confirmation() {
    echo -e "${BLUE}=== 安装确认 ===${NC}"
    echo -e "即将安装智能NAT网关系统，包括:"
    echo -e "  ${GREEN}•${NC} NAT服务核心功能"
    echo -e "  ${GREEN}•${NC} 系统服务集成 (systemd)"
    echo -e "  ${GREEN}•${NC} 配置管理工具"
    echo -e "  ${GREEN}•${NC} 实时监控功能"
    echo -e "  ${GREEN}•${NC} 备份恢复功能"
    echo -e "  ${GREEN}•${NC} 完整测试套件"
    echo
    echo -e "安装位置:"
    echo -e "  ${CYAN}•${NC} 主程序: /usr/local/bin/nat-gateway"
    echo -e "  ${CYAN}•${NC} 配置文件: /etc/network/nat-gateway.conf"
    echo -e "  ${CYAN}•${NC} 日志文件: /var/log/iptables-nat.log"
    echo -e "  ${CYAN}•${NC} 系统服务: nat-gateway.service"
    echo
    
    echo -e "${YELLOW}确认要继续安装吗? [Y/n]: ${NC}"
    read -r response
    
    if [[ $response =~ ^[Nn]$ ]]; then
        info_message "用户取消安装"
        exit 0
    fi
    
    echo
}

# 执行安装
perform_installation() {
    echo -e "${BLUE}=== 开始安装 ===${NC}"
    
    # 检查安装脚本
    local install_script="$SCRIPT_DIR/install.sh"
    if [[ ! -f "$install_script" ]]; then
        error_exit "安装脚本不存在: $install_script"
    fi
    
    # 执行安装
    info_message "执行系统安装..."
    if "$install_script" install; then
        success_message "系统安装完成"
    else
        error_exit "系统安装失败"
    fi
    
    echo
}

# 初始配置
initial_configuration() {
    echo -e "${BLUE}=== 初始配置 ===${NC}"
    
    # 启动NAT服务
    info_message "启动NAT服务..."
    if nat-gateway start; then
        success_message "NAT服务启动成功"
    else
        warning_message "NAT服务启动失败，请检查配置"
    fi
    
    # 运行基本测试
    info_message "运行基本功能测试..."
    if nat-gateway test system > /dev/null 2>&1; then
        success_message "基本功能测试通过"
    else
        warning_message "基本功能测试有问题，请查看详细测试结果"
    fi
    
    echo
}

# 显示安装结果
show_installation_result() {
    echo -e "${GREEN}=== 安装完成 ===${NC}"
    echo
    
    # 显示服务状态
    echo -e "${BLUE}服务状态:${NC}"
    nat-gateway status
    echo
    
    # 显示使用说明
    echo -e "${BLUE}使用说明:${NC}"
    echo -e "  ${GREEN}查看帮助:${NC}     nat-gateway help"
    echo -e "  ${GREEN}查看状态:${NC}     nat-gateway status"
    echo -e "  ${GREEN}实时监控:${NC}     nat-gateway monitor"
    echo -e "  ${GREEN}功能测试:${NC}     nat-gateway test"
    echo -e "  ${GREEN}配置管理:${NC}     nat-gateway config"
    echo
    
    # 显示快捷命令
    echo -e "${BLUE}快捷命令:${NC}"
    echo -e "  ${CYAN}natgw${NC} - 快捷命令别名"
    echo
    
    # 显示配置文件位置
    echo -e "${BLUE}重要文件:${NC}"
    echo -e "  ${CYAN}配置文件:${NC} /etc/network/nat-gateway.conf"
    echo -e "  ${CYAN}日志文件:${NC} /var/log/iptables-nat.log"
    echo -e "  ${CYAN}备份目录:${NC} /etc/network/backups"
    echo
    
    # 显示下一步建议
    echo -e "${BLUE}建议下一步:${NC}"
    echo -e "  ${YELLOW}1.${NC} 运行完整测试: ${CYAN}nat-gateway test${NC}"
    echo -e "  ${YELLOW}2.${NC} 查看实时监控: ${CYAN}nat-gateway monitor${NC}"
    echo -e "  ${YELLOW}3.${NC} 创建配置备份: ${CYAN}nat-gateway backup${NC}"
    echo -e "  ${YELLOW}4.${NC} 查看详细文档: ${CYAN}cat $SCRIPT_DIR/README.md${NC}"
    echo
    
    success_message "NAT网关系统安装成功！"
}

# 错误处理
handle_error() {
    echo -e "\n${RED}安装过程中出现错误！${NC}"
    echo -e "${YELLOW}故障排除建议:${NC}"
    echo -e "  ${CYAN}1.${NC} 检查网络连接"
    echo -e "  ${CYAN}2.${NC} 确认具有root权限"
    echo -e "  ${CYAN}3.${NC} 查看系统日志: journalctl -xe"
    echo -e "  ${CYAN}4.${NC} 手动安装: sudo ./install.sh install"
    echo -e "  ${CYAN}5.${NC} 运行测试: ./test.sh system"
    echo
    echo -e "如需帮助，请查看 ${CYAN}README.md${NC} 文档"
}

# 主函数
main() {
    # 设置错误处理
    trap 'handle_error' ERR
    
    # 显示横幅
    show_banner
    
    # 检查权限
    check_root
    
    # 系统检查
    check_system
    
    # 网络检测
    detect_network
    
    # 用户确认
    user_confirmation
    
    # 执行安装
    perform_installation
    
    # 初始配置
    initial_configuration
    
    # 显示结果
    show_installation_result
}

# 显示帮助
show_help() {
    cat << EOF
${BLUE}NAT网关快速安装脚本${NC}

${YELLOW}用法:${NC}
    $0 [选项]

${YELLOW}选项:${NC}
    install         执行快速安装 (默认)
    help            显示此帮助信息

${YELLOW}功能:${NC}
    - 自动检测系统环境
    - 智能识别网络配置
    - 一键安装所有组件
    - 自动配置和启动服务
    - 运行基本功能测试

${YELLOW}安装内容:${NC}
    - NAT网关核心服务
    - 系统服务集成
    - 配置管理工具
    - 监控和测试工具
    - 备份恢复功能

${YELLOW}系统要求:${NC}
    - Linux操作系统
    - root权限
    - iptables支持
    - systemd服务管理

EOF
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-install}" in
        "install")
            main
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            error_exit "未知选项: $1。使用 '$0 help' 查看帮助信息"
            ;;
    esac
fi
