#!/bin/bash

# =============================================================================
# 外网接口切换功能演示脚本
# 功能: 演示NAT网关的外网接口自动和手动切换功能
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示演示横幅
show_demo_banner() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║              NAT网关外网接口切换功能演示                      ║
║            WAN Interface Switching Demo                     ║
╠══════════════════════════════════════════════════════════════╣
║  演示内容: 自动检测、智能评分、手动切换、故障转移              ║
║  适用场景: 多网卡环境、网络故障恢复、性能优化                  ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 等待用户确认
wait_for_user() {
    echo -e "${YELLOW}按回车键继续...${NC}"
    read -r
}

# 显示步骤标题
show_step() {
    local step_num=$1
    local step_title=$2
    
    echo -e "\n${BLUE}=== 步骤 $step_num: $step_title ===${NC}"
}

# 演示1: 列出可用的外网接口
demo_list_interfaces() {
    show_step "1" "列出可用的外网接口"
    echo -e "命令: ${CYAN}./nat-gateway.sh list-wan${NC}"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        ./nat-gateway.sh list-wan
    else
        echo -e "${YELLOW}注意: 需要root权限才能执行实际操作${NC}"
        echo -e "${CYAN}模拟输出:${NC}"
        cat << 'EOF'
=== 可用的外网接口 ===
eth0: IP=*************/24, 评分=88, 状态=可用 [当前默认] [NAT活动]
wlan0: IP=*************/24, 评分=65, 状态=可用
wwan0: IP=**********/24, 评分=35, 状态=不可用
EOF
    fi
    
    echo
    echo -e "${GREEN}说明:${NC}"
    echo -e "• 显示所有可用的外网接口及其状态"
    echo -e "• 评分基于连通性、接口类型、速度等因素"
    echo -e "• 标识当前默认接口和NAT活动状态"
    
    wait_for_user
}

# 演示2: 外网接口详细信息
demo_interface_info() {
    show_step "2" "查看外网接口详细信息"
    echo -e "命令: ${CYAN}./wan-manager.sh info${NC}"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        ./wan-manager.sh info
    else
        echo -e "${YELLOW}注意: 需要root权限才能执行实际操作${NC}"
        echo -e "${CYAN}模拟输出:${NC}"
        cat << 'EOF'
=== 详细外网接口信息 ===
=== 可用的外网接口 ===
eth0: IP=*************/24, 评分=88, 状态=可用 [当前默认] [NAT活动]
wlan0: IP=*************/24, 评分=65, 状态=可用

当前活动的外网接口: eth0
活动的NAT规则数量: 3
EOF
    fi
    
    echo
    echo -e "${GREEN}说明:${NC}"
    echo -e "• 显示当前活动的外网接口"
    echo -e "• 统计NAT规则数量"
    echo -e "• 提供详细的接口状态信息"
    
    wait_for_user
}

# 演示3: 测试外网接口连通性
demo_test_connectivity() {
    show_step "3" "测试外网接口连通性"
    echo -e "命令: ${CYAN}./wan-manager.sh test${NC}"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        ./wan-manager.sh test
    else
        echo -e "${YELLOW}注意: 需要root权限才能执行实际操作${NC}"
        echo -e "${CYAN}模拟输出:${NC}"
        cat << 'EOF'
=== 测试所有外网接口 ===
测试主机: ******* ******* ***************

测试接口: eth0 (*************)
  ✓ ******* 可达
  ✓ ******* 可达
  ✓ *************** 可达
  成功率: 100% (3/3)

测试接口: wlan0 (*************)
  ✓ ******* 可达
  ✗ ******* 不可达
  ✓ *************** 可达
  成功率: 67% (2/3)
EOF
    fi
    
    echo
    echo -e "${GREEN}说明:${NC}"
    echo -e "• 测试每个接口到多个外网主机的连通性"
    echo -e "• 计算连通性成功率"
    echo -e "• 帮助评估接口质量"
    
    wait_for_user
}

# 演示4: 比较外网接口性能
demo_compare_performance() {
    show_step "4" "比较外网接口性能"
    echo -e "命令: ${CYAN}./wan-manager.sh compare${NC}"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        ./wan-manager.sh compare
    else
        echo -e "${YELLOW}注意: 需要root权限才能执行实际操作${NC}"
        echo -e "${CYAN}模拟输出:${NC}"
        cat << 'EOF'
=== 外网接口性能比较 ===
测试主机: *******
测试项目: 延迟、丢包率

接口         IP地址          延迟(ms)   丢包率     状态
================================================================
eth0         *************   12.5ms     0%         正常
wlan0        *************   25.8ms     10%        正常
wwan0        **********      超时       100%       异常
EOF
    fi
    
    echo
    echo -e "${GREEN}说明:${NC}"
    echo -e "• 测试每个接口的网络延迟"
    echo -e "• 统计丢包率"
    echo -e "• 评估接口性能状态"
    
    wait_for_user
}

# 演示5: 自动选择最佳外网接口
demo_auto_select() {
    show_step "5" "自动选择最佳外网接口"
    echo -e "命令: ${CYAN}./nat-gateway.sh auto-wan${NC}"
    echo
    
    echo -e "${GREEN}自动选择过程:${NC}"
    echo -e "1. 检测所有可用的外网接口"
    echo -e "2. 对每个接口进行评分"
    echo -e "3. 选择评分最高的接口"
    echo -e "4. 切换NAT规则到新接口"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        echo -e "${YELLOW}是否执行自动选择? [y/N]: ${NC}"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ./nat-gateway.sh auto-wan
        else
            echo -e "${CYAN}跳过实际执行${NC}"
        fi
    else
        echo -e "${CYAN}模拟输出:${NC}"
        cat << 'EOF'
评估可用的外网接口...
接口 eth0 (IP: *************) 评分: 88
接口 wlan0 (IP: *************) 评分: 65
接口 wwan0 (IP: **********) 评分: 35
✓ 自动选择最佳外网接口: eth0 (评分: 88)
✓ 外网接口切换完成: eth0
EOF
    fi
    
    wait_for_user
}

# 演示6: 手动切换外网接口
demo_manual_switch() {
    show_step "6" "手动切换外网接口"
    echo -e "命令: ${CYAN}./nat-gateway.sh switch-wan${NC}"
    echo
    
    echo -e "${GREEN}手动切换选项:${NC}"
    echo -e "• 交互式选择: ${CYAN}./nat-gateway.sh switch-wan${NC}"
    echo -e "• 指定接口: ${CYAN}./nat-gateway.sh switch-wan wlan0${NC}"
    echo -e "• 强制切换: ${CYAN}./nat-gateway.sh force-wan wlan0${NC}"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        echo -e "${YELLOW}是否演示交互式切换? [y/N]: ${NC}"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ./nat-gateway.sh switch-wan
        else
            echo -e "${CYAN}跳过实际执行${NC}"
        fi
    else
        echo -e "${CYAN}模拟交互式选择:${NC}"
        cat << 'EOF'
可用的外网接口:
  1. eth0 (IP: *************) - 可用 [当前默认]
  2. wlan0 (IP: *************) - 可用
  3. wwan0 (IP: **********) - 不可用

请选择外网接口 [1-3]: 2
✓ 选择外网接口: wlan0
✓ 外网接口切换完成: wlan0
EOF
    fi
    
    wait_for_user
}

# 演示7: 故障转移功能
demo_failover() {
    show_step "7" "自动故障转移"
    echo -e "命令: ${CYAN}./wan-manager.sh failover${NC}"
    echo
    
    echo -e "${GREEN}故障转移场景:${NC}"
    echo -e "1. 检测当前外网接口连通性"
    echo -e "2. 如果发现故障，自动切换到备用接口"
    echo -e "3. 确保网络服务不中断"
    echo
    
    if [[ $EUID -eq 0 ]]; then
        ./wan-manager.sh failover
    else
        echo -e "${CYAN}模拟故障转移:${NC}"
        cat << 'EOF'
=== 自动故障转移 ===
当前外网接口: eth0
⚠ 当前外网接口连通性异常，开始故障转移...
评估可用的外网接口...
接口 wlan0 (IP: *************) 评分: 65
✓ 自动选择最佳外网接口: wlan0 (评分: 65)
✓ 故障转移完成
EOF
    fi
    
    wait_for_user
}

# 演示8: 实时监控
demo_monitoring() {
    show_step "8" "实时监控外网接口"
    echo -e "命令: ${CYAN}./wan-manager.sh monitor${NC}"
    echo
    
    echo -e "${GREEN}监控功能:${NC}"
    echo -e "• 实时显示所有外网接口状态"
    echo -e "• 连通性测试结果"
    echo -e "• 当前活动接口标识"
    echo -e "• 可自定义监控间隔"
    echo
    
    echo -e "${YELLOW}注意: 监控模式会持续运行，按Ctrl+C退出${NC}"
    echo -e "${CYAN}这里只演示一次性显示${NC}"
    
    if [[ $EUID -eq 0 ]]; then
        echo -e "${YELLOW}是否启动5秒监控演示? [y/N]: ${NC}"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            timeout 5 ./wan-manager.sh monitor 2 || true
        fi
    else
        echo -e "${CYAN}模拟监控输出:${NC}"
        cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    外网接口管理器                             ║
║                  WAN Interface Manager                      ║
╚══════════════════════════════════════════════════════════════╝

=== 详细外网接口信息 ===
当前活动的外网接口: wlan0
活动的NAT规则数量: 3

=== 实时连通性测试 ===
eth0: ************* - ✗ 不可达
wlan0: ************* - ✓ 可达 [当前]
wwan0: ********** - ✗ 不可达

下次刷新: 2秒后 | 按 Ctrl+C 退出
EOF
    fi
    
    wait_for_user
}

# 显示总结
show_summary() {
    show_step "总结" "外网接口切换功能特点"
    
    echo -e "${GREEN}🎯 主要功能:${NC}"
    echo -e "• ${CYAN}智能检测${NC}: 自动发现和评估外网接口"
    echo -e "• ${CYAN}自动切换${NC}: 基于评分机制自动选择最佳接口"
    echo -e "• ${CYAN}手动控制${NC}: 支持交互式和命令行指定切换"
    echo -e "• ${CYAN}故障转移${NC}: 自动检测故障并切换到备用接口"
    echo -e "• ${CYAN}实时监控${NC}: 持续监控接口状态和连通性"
    echo -e "• ${CYAN}性能测试${NC}: 延迟、丢包率等性能指标测试"
    echo
    
    echo -e "${GREEN}🔧 适用场景:${NC}"
    echo -e "• ${YELLOW}多网卡环境${NC}: 有线、无线、移动网络共存"
    echo -e "• ${YELLOW}网络故障恢复${NC}: 主网络故障时自动切换备用"
    echo -e "• ${YELLOW}性能优化${NC}: 根据网络质量动态选择最佳接口"
    echo -e "• ${YELLOW}负载均衡${NC}: 在多个外网接口间分配流量"
    echo
    
    echo -e "${GREEN}📈 评分机制:${NC}"
    echo -e "• ${MAGENTA}连通性${NC}: 30分 - 能否正常访问外网"
    echo -e "• ${MAGENTA}默认路由${NC}: 40分 - 是否为系统默认路由"
    echo -e "• ${MAGENTA}接口类型${NC}: 20分 - 有线网络优先级最高"
    echo -e "• ${MAGENTA}网络速度${NC}: 10分 - 千兆网络加分最多"
    echo
    
    echo -e "${BLUE}感谢使用NAT网关外网接口切换功能演示！${NC}"
}

# 主函数
main() {
    show_demo_banner
    
    echo -e "${BLUE}本演示将展示NAT网关的外网接口切换功能${NC}"
    echo -e "${YELLOW}包括自动检测、智能评分、手动切换、故障转移等特性${NC}"
    echo
    
    if [[ $EUID -ne 0 ]]; then
        echo -e "${YELLOW}注意: 当前非root用户，部分功能将显示模拟输出${NC}"
        echo -e "${YELLOW}要体验完整功能，请使用 sudo 运行此脚本${NC}"
        echo
    fi
    
    wait_for_user
    
    # 执行各个演示步骤
    demo_list_interfaces
    demo_interface_info
    demo_test_connectivity
    demo_compare_performance
    demo_auto_select
    demo_manual_switch
    demo_failover
    demo_monitoring
    show_summary
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
