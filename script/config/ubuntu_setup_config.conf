# Ubuntu自动化配置脚本配置文件
# 设置为true启用，false禁用
# 支持普通用户和root用户运行
# root用户运行时会为所有用户配置环境

# 系统更新
UPDATE_SYSTEM=true

# 基础软件包安装
INSTALL_BASIC_PACKAGES=true

# 中文环境配置
SETUP_CHINESE_LOCALE=true

# Zsh和Oh My Zsh安装
INSTALL_ZSH=true

# Docker安装
INSTALL_DOCKER=true

# Node.js安装
INSTALL_NODEJS=true

# Python开发环境
INSTALL_PYTHON_ENV=true

# Rust和Cargo安装
INSTALL_RUST_CARGO=true

# Zellij终端复用器安装
INSTALL_ZELLIJ=true

# x-cmd工具集安装
INSTALL_XCMD=true

# fzf模糊搜索配置
SETUP_FZF=true

# autojump目录跳转配置
SETUP_AUTOJUMP=true

# LazyVim安装
INSTALL_LAZYVIM=true

# Git配置
SETUP_GIT=true

# SSH配置
SETUP_SSH=true

# 防火墙配置
SETUP_FIREWALL=true

# 字体安装
INSTALL_FONTS=true

# Git配置信息（如果SETUP_GIT=true）
GIT_USERNAME="xuhui"
GIT_EMAIL="<EMAIL>"

# Git大文件管理
GIT_LARGE_FILE_THRESHOLD="40m"
GIT_AUTO_IGNORE_LARGE_FILES=true

# SSH密钥生成（如果SETUP_SSH=true）
GENERATE_SSH_KEY=true

# SSH配置部署（如果存在ssh/ssh.tar.gz文件）
DEPLOY_SSH_CONFIG=true

# SSH服务安全配置
SSH_PERMIT_ROOT_LOGIN=true
SSH_PUBKEY_AUTHENTICATION=true
SSH_PASSWORD_AUTHENTICATION=true

# 额外软件包列表（空格分隔）
EXTRA_PACKAGES="code firefox chromium-browser vlc gimp"

# Docker Compose安装
INSTALL_DOCKER_COMPOSE=true

# 开发工具安装
INSTALL_DEV_TOOLS=true

# 开发工具列表
DEV_TOOLS="code postman dbeaver-ce"

# 系统优化
ENABLE_SYSTEM_OPTIMIZATION=true

# 自动清理
AUTO_CLEANUP=true
