#!/bin/bash

# SSH配置部署脚本
# 将ssh.tar.gz解压到所有用户的根目录并设置正确权限

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo -e "${GREEN}=== SSH配置部署脚本 ===${NC}"

# 检查用户权限
if [[ $EUID -eq 0 ]]; then
    log_warn "检测到root用户，将为所有用户配置SSH"
    IS_ROOT=true
else
    log_info "检测到普通用户: $(whoami)"
    IS_ROOT=false
    if ! sudo -n true 2>/dev/null; then
        log_info "需要sudo权限，请输入密码"
        sudo -v
    fi
fi

# 检查ssh.tar.gz文件是否存在
SSH_ARCHIVE="ssh/ssh.tar.gz"
if [[ ! -f "$SSH_ARCHIVE" ]]; then
    log_error "未找到SSH配置文件: $SSH_ARCHIVE"
    log_info "请确保ssh/ssh.tar.gz文件存在"
    exit 1
fi

log_info "找到SSH配置文件: $SSH_ARCHIVE"

# 显示压缩包内容
log_step "SSH配置文件内容:"
tar -tzf "$SSH_ARCHIVE" | while read file; do
    echo "  $file"
done

# 确认部署
if [[ "${1:-}" != "--force" ]]; then
    echo ""
    read -p "是否继续部署SSH配置到所有用户? (y/n): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
fi

# 启动SSH服务
log_step "配置SSH服务..."
sudo systemctl enable ssh
sudo systemctl start ssh
log_info "SSH服务已启动"

# 获取所有普通用户（UID >= 1000，排除nobody）
log_step "获取系统用户列表..."
users=$(awk -F: '$3 >= 1000 && $3 != 65534 {print $1":"$6}' /etc/passwd)

if [[ -z "$users" ]]; then
    log_warn "未找到普通用户"
else
    log_info "找到以下用户:"
    echo "$users" | while IFS=: read -r username homedir; do
        echo "  $username -> $homedir"
    done
fi

# 为每个用户部署SSH配置
log_step "为普通用户部署SSH配置..."
echo "$users" | while IFS=: read -r username homedir; do
    if [[ -d "$homedir" ]]; then
        log_info "配置用户: $username ($homedir)"
        
        # 备份现有SSH配置
        if [[ -d "$homedir/.ssh" ]]; then
            backup_dir="$homedir/.ssh.backup.$(date +%Y%m%d_%H%M%S)"
            sudo cp -r "$homedir/.ssh" "$backup_dir"
            log_info "已备份现有SSH配置到: $backup_dir"
        fi
        
        # 创建.ssh目录
        sudo mkdir -p "$homedir/.ssh"
        
        # 解压SSH配置到用户目录
        sudo tar -xzf "$SSH_ARCHIVE" -C "$homedir/"
        
        # 设置正确的所有者
        sudo chown -R "$username:$username" "$homedir/.ssh"
        
        # 设置目录权限
        sudo chmod 700 "$homedir/.ssh"
        
        # 设置文件权限
        if [[ -f "$homedir/.ssh/id_rsa" ]]; then
            sudo chmod 600 "$homedir/.ssh/id_rsa"
            log_info "  ✓ 私钥权限设置为600"
        fi
        
        if [[ -f "$homedir/.ssh/id_rsa.pub" ]]; then
            sudo chmod 644 "$homedir/.ssh/id_rsa.pub"
            log_info "  ✓ 公钥权限设置为644"
        fi
        
        if [[ -f "$homedir/.ssh/authorized_keys" ]]; then
            sudo chmod 600 "$homedir/.ssh/authorized_keys"
            log_info "  ✓ authorized_keys权限设置为600"
        fi
        
        if [[ -f "$homedir/.ssh/config" ]]; then
            sudo chmod 600 "$homedir/.ssh/config"
            log_info "  ✓ config文件权限设置为600"
        fi
        
        # 设置其他SSH文件权限
        sudo find "$homedir/.ssh" -type f -exec chmod 600 {} \;
        sudo find "$homedir/.ssh" -name "*.pub" -exec chmod 644 {} \;
        
        log_info "  ✓ 用户 $username 的SSH配置完成"
    else
        log_warn "用户 $username 的家目录不存在: $homedir"
    fi
done

# 为root用户配置SSH
log_step "为root用户部署SSH配置..."
if [[ -d "/root" ]]; then
    log_info "配置root用户..."
    
    # 备份现有SSH配置
    if [[ -d "/root/.ssh" ]]; then
        backup_dir="/root/.ssh.backup.$(date +%Y%m%d_%H%M%S)"
        sudo cp -r "/root/.ssh" "$backup_dir"
        log_info "已备份root的SSH配置到: $backup_dir"
    fi
    
    # 创建.ssh目录
    sudo mkdir -p /root/.ssh
    
    # 解压SSH配置到root目录
    sudo tar -xzf "$SSH_ARCHIVE" -C /root/
    
    # 设置正确的所有者和权限
    sudo chown -R root:root /root/.ssh
    sudo chmod 700 /root/.ssh
    
    # 设置文件权限
    if [[ -f "/root/.ssh/id_rsa" ]]; then
        sudo chmod 600 /root/.ssh/id_rsa
        log_info "  ✓ root私钥权限设置为600"
    fi
    
    if [[ -f "/root/.ssh/id_rsa.pub" ]]; then
        sudo chmod 644 /root/.ssh/id_rsa.pub
        log_info "  ✓ root公钥权限设置为644"
    fi
    
    if [[ -f "/root/.ssh/authorized_keys" ]]; then
        sudo chmod 600 /root/.ssh/authorized_keys
        log_info "  ✓ root authorized_keys权限设置为600"
    fi
    
    if [[ -f "/root/.ssh/config" ]]; then
        sudo chmod 600 /root/.ssh/config
        log_info "  ✓ root config文件权限设置为600"
    fi
    
    # 设置其他SSH文件权限
    sudo find /root/.ssh -type f -exec chmod 600 {} \;
    sudo find /root/.ssh -name "*.pub" -exec chmod 644 {} \;
    
    log_info "  ✓ root用户的SSH配置完成"
else
    log_warn "root目录不存在"
fi

# 配置SSH服务安全设置
log_step "配置SSH服务安全设置..."

# 备份原始配置
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup.$(date +%Y%m%d_%H%M%S)
log_info "已备份SSH服务配置"

# 应用安全配置
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin yes/' /etc/ssh/sshd_config
sudo sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
sudo sed -i 's/#AuthorizedKeysFile/AuthorizedKeysFile/' /etc/ssh/sshd_config

log_info "SSH服务配置已更新"

# 测试SSH配置
log_step "测试SSH配置..."
if sudo sshd -t; then
    log_info "SSH配置语法检查通过"
    
    # 重启SSH服务
    sudo systemctl restart ssh
    log_info "SSH服务已重启"
else
    log_error "SSH配置语法检查失败，请检查配置"
    exit 1
fi

# 显示部署摘要
log_step "部署摘要"
echo "=================================="
echo "SSH配置部署完成！"
echo "=================================="
echo "已配置的用户："
echo "$users" | while IFS=: read -r username homedir; do
    if [[ -d "$homedir/.ssh" ]]; then
        echo "✓ $username ($homedir)"
    fi
done
echo "✓ root (/root)"
echo ""
echo "配置的文件权限："
echo "  .ssh/           -> 700"
echo "  id_rsa          -> 600"
echo "  id_rsa.pub      -> 644"
echo "  authorized_keys -> 600"
echo "  config          -> 600"
echo ""
echo "SSH服务状态："
echo "  服务状态: $(sudo systemctl is-active ssh)"
echo "  开机启动: $(sudo systemctl is-enabled ssh)"
echo ""
echo "备份位置："
echo "  SSH服务配置: /etc/ssh/sshd_config.backup.*"
echo "  用户SSH配置: ~/.ssh.backup.*"
echo "=================================="

log_info "SSH配置部署完成！"
