#!/bin/bash

USER="root"
USER2="cat"
HOST="nevin.email"
PORT="21422"

if [ -n "$1" ]; then
    PORT=$1
fi
if [ -n "$1" ]; then
    USER2=$2
fi

SCRIPT_PATH=$(readlink -f "$0")
echo "$SCRIPT_PATH"
SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

ubuntu_software_path=$SCRIPT_DIR
rsyncthing_bin="syncthing-linux-arm64-v1.29.0/syncthing"
rsyncthing_config="syncthing-linux-arm64-v1.29.0/etc/linux-systemd/"
config_file_path="/home/<USER>/.local/state/syncthing/config.xml"

# 定义 scp 传输函数
rsc_transfer() {
    # 检查参数数量
    if [ "$#" -ne 4 ]; then
        echo "Usage: scp_transfer <source_file> <target_host> <target_path>"
        return 1
    fi

    local source_file=$1
    local target_host=$2
    local target_port=$3
    local target_path=$4

    # 检查源文件是否存在
    if [ ! -f "$source_file" ]; then
        if [ ! -d "$source_file" ]; then
            echo "Error: Source file $source_file is not a regular file."
            return 1
        fi
    fi

    # 执行 scp 命令
    rsync -rvz --progress -e "ssh -p  ${target_port}"  $source_file   --exclude-from=$SCRIPT_DIR/exclude.txt   $target_host:$target_path
    if [ $? -eq 0 ]; then
        echo "File $source_file successfully transferred to $target_host:$target_path"
    else
        echo "Error: Failed to transfer file $source_file to $target_host:$target_path"
        return 1
    fi
}

rsc_transfer ${ubuntu_software_path}/${rsyncthing_bin} ${USER}@${HOST} ${PORT} /usr/bin/
rsc_transfer ${ubuntu_software_path}/${rsyncthing_config} ${USER}@${HOST} ${PORT} /etc/systemd/


ssh -o StrictHostKeyChecking=no $USER@$HOST -p $PORT << EOF
echo "开始执行"



systemctl enable syncthing@$USER2
systemctl start syncthing@$USER2
sleep 5
sed -i 's|<address>127.0.0.1:8384</address>|<address>0.0.0.0:8384</address>|g' "$config_file_path"
systemctl restart syncthing@$USER2
echo "修改完成"

# eval "$(curl https://get.x-cmd.com)"


EOF

# 示例调用
# scp_transfer /path/to/source/file user@remote_host:/path/to/target/directory


# des_file=$1
# des_port=$2

# syncthing_bin_path="/mine/ubunt"


# SCRIPT_PATH=$(readlink -f "$0")
# echo "$SCRIPT_PATH"

# SCRIPT_DIR=$(dirname "$0")
# # 将目录名转换为绝对路径
# SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
# echo "$SCRIPT_DIR"

# rsync -rvz --progress -e "ssh -p $2 "  $SCRIPT_DIR   --exclude-from=$SCRIPT_DIR/exclude.txt   $1
