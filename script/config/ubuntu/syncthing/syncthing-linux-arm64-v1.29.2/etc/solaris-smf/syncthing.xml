<?xml version="1.0"?>
<!DOCTYPE service_bundle SYSTEM "/usr/share/lib/xml/dtd/service_bundle.dtd.1">
<service_bundle type="manifest" name="syncthing">
	<service name="site/syncthing" type="service" version="1">
		<create_default_instance enabled="true"/>
		<single_instance/>

		<dependency name="network" grouping="require_all" restart_on="error" type="service">
			<service_fmri value="svc:/milestone/network:default"/>
		</dependency>

		<dependency name="filesystem" grouping="require_all" restart_on="error" type="service">
			<service_fmri value="svc:/system/filesystem/local"/>
		</dependency>

		<method_context>
			<method_credential user="jb" group="other"/>
		</method_context>

		<exec_method type="method" name="start" exec="/home/<USER>/bin/syncthing" timeout_seconds="60">
			<method_context>
				<method_environment>
                    <envvar name="HOME" value="/home/<USER>"/>
					<envvar name="STNORESTART" value="1"/>
				</method_environment>
			</method_context>
		</exec_method>
		<exec_method type="method" name="stop" exec=":kill" timeout_seconds="60"/>

		<property_group name="startd" type="framework">
			<propval name="duration" type="astring" value="child"/>
			<propval name="ignore_error" type="astring" value="core,signal"/>
		</property_group>

		<property_group name="application" type="application">
		</property_group>

		<stability value="Evolving"/>

		<template>
			<common_name>
				<loctext xml:lang="C">
					Syncthing
				</loctext>
			</common_name>
		</template>
	</service>
</service_bundle>
