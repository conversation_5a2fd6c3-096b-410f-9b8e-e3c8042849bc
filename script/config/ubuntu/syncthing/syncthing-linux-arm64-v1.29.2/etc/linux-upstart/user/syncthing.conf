# Location of the syncthing executable
env SYNCTHING_EXE="/usr/local/bin"

# Set the name of the application
description "Syncthing"

# Start syncthing you login to your desktop
start on desktop-start

# Stop syncthing you logout of your desktop
stop on desktop-end

# Set STNORESTART to yes to have <PERSON><PERSON><PERSON> monitor the process instead
# of having a separate syncthing process do the monitoring
env STNORESTART=yes

# If Upstart detects syncthing has failed - it should restart it
respawn

# the syncthing command Upstart is to execute when it is started up
exec $SYNCTHING_EXE -no-browser
