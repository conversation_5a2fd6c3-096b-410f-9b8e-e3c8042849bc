#/bin/bash

#更换软件源
rm -rf /etc/apt/sources.list.d/pve-enterprise.list /etc/apt/sources.list.d/ceph.list
echo "deb https://mirrors.nju.edu.cn/proxmox/debian bookworm pve-no-subscription" > /etc/apt/sources.list.d/pve-no-subscription.list
sed -i.bak "s#http://ftp.debian.org#https://repo.huaweicloud.com#g" /etc/apt/sources.list     #华为Debian源
sed -i "s#http://security.debian.org#https://repo.huaweicloud.com/debian-security#g" /etc/apt/sources.list     #华为Debian源
apt update && apt-get install -y apt-transport-https ca-certificates  --fix-missing

#更换LXC源
sed -i.bak "s#http://download.proxmox.com/images#https://mirrors.nju.edu.cn/proxmox/images#g" /usr/share/perl5/PVE/APLInfo.pm  
wget -O /var/lib/pve-manager/apl-info/mirrors.nju.edu.cn https://mirrors.nju.edu.cn/proxmox/images/aplinfo-pve-7.dat

#删除订阅弹窗
sed -Ezi.bak "s/(Ext.Msg.show\(\{\s+title: gettext\('No valid sub)/void\(\{ \/\/\1/g" /usr/share/javascript/proxmox-widget-toolkit/proxmoxlib.js
echo "0 1 * * * sed -Ezi \"s/(Ext.Msg.show\(\{\s+title: gettext\('No valid sub)/void\(\{ \/\/\1/g\" /usr/share/javascript/proxmox-widget-toolkit/proxmoxlib.js" | crontab -

#设置Web控制台默认语言为中文
echo 'language: zh_CN' >>/etc/pve/datacenter.cfg

#下载并导入虚拟机模板
#wget -P /var/lib/vz/dump/ http://*************/vzdump-qemu/vzdump-qemu-openEuler2403-x86_64.vma.zst
#qmrestore /var/lib/vz/dump/vzdump-qemu-openEuler2403-x86_64.vma.zst 100

# 重启相关服务
systemctl restart pvedaemon & systemctl restart pveproxy.service
