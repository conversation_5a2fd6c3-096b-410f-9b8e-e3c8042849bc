{"name": "ubuntu", "settings": "{\"settings\":\"{\\n  \\\"vscode-edge-devtools.webhint\\\": false,\\n  \\\"git.enableSmartCommit\\\": true,\\n  //     // \\\"editor.fontFamily\\\": \\\"微软雅黑\\\",\\n  \\\"editor.fontFamily\\\": \\\"Hack Nerd Font Mono\\\",\\n  \\\"editor.fontWeight\\\": \\\"550\\\",\\n  \\\"editor.mouseWheelZoom\\\": true,\\n  \\\"terminal.integrated.fontWeight\\\": \\\"400\\\",\\n  \\\"terminal.integrated.fontWeightBold\\\": \\\"700\\\",\\n  \\\"window.title\\\": \\\"${dirty}${activeEditorShort}${separator}${separator}${remoteName}${rootPath}${separator}${separator}${rootName}\\\",\\n  //     // \\\"editor.wordWrap\\\": \\\"on\\\",\\n  \\\"vim.easymotionDimBackground\\\": false,\\n  \\\"vim.easymotion\\\": true,\\n  \\\"vim.incsearch\\\": true,\\n  \\\"vim.hlsearch\\\": true,\\n  \\\"vim.flash.enable\\\": true,\\n  \\\"vim.insertModeKeyBindings\\\": [\\n    {\\n      \\\"before\\\": [\\\"j\\\", \\\"j\\\"],\\n      \\\"after\\\": [\\\"<Esc>\\\"]\\n    }\\n  ],\\n  \\\"vim.normalModeKeyBindingsNonRecursive\\\": [\\n    {\\n      \\\"before\\\": [\\\"<leader>\\\", \\\"d\\\"],\\n      \\\"after\\\": [\\\"d\\\", \\\"d\\\"]\\n    },\\n    {\\n      \\\"before\\\": [\\\"<C-n>\\\"],\\n      \\\"commands\\\": [\\\":nohl\\\"]\\n    },\\n    {\\n      // 取消最近的撤销\\n      \\\"before\\\": [\\\"<leader>\\\", \\\"u\\\"],\\n      \\\"after\\\": [\\\"<C-r>\\\"]\\n    },\\n    {\\n      \\\"before\\\": [\\\"K\\\"],\\n      \\\"commands\\\": [\\\"lineBreakInsert\\\"],\\n      \\\"silent\\\": true\\n    }\\n  ],\\n  \\\"vim.leader\\\": \\\"<space>\\\",\\n  \\\"vim.handleKeys\\\": {\\n    \\\"<C-x>\\\": false,\\n    \\\"<C-c>\\\": false,\\n    \\\"<C-v>\\\": false,\\n    \\\"<C-a>\\\": false,\\n    \\\"<C-k>\\\": false,\\n    \\\"<C-z>\\\": false,\\n    \\\"<C-y>\\\": false,\\n    \\\"<C-p>\\\": false,\\n    // \\\"<C-f>\\\": false,\\n    \\\"<C-,>\\\": false\\n  },\\n  \\\"tabnine.experimentalAutoImports\\\": true,\\n  \\\"editor.insertSpaces\\\": false,\\n  \\\"clangd.detectExtensionConflicts\\\": false,\\n  \\\"search.actionsPosition\\\": \\\"auto\\\",\\n  \\\"search.collapseResults\\\": \\\"auto\\\",\\n  \\\"search.defaultViewMode\\\": \\\"tree\\\",\\n  \\\"search.experimental.notebookSearch\\\": true,\\n  \\\"search.searchEditor.doubleClickBehaviour\\\": \\\"openLocationToSide\\\",\\n  \\\"search.searchEditor.reusePriorSearchConfiguration\\\": true,\\n  \\\"search.showLineNumbers\\\": true,\\n  \\\"terminal.external.linuxExec\\\": \\\"\\\",\\n  \\\"terminal.integrated.copyOnSelection\\\": true,\\n  // \\\"terminal.integrated.cursorBlinking\\\": true,\\n  \\\"terminal.integrated.defaultProfile.linux\\\": \\\"zsh (2)\\\",\\n  // \\\"terminal.integrated.rightClickBehavior\\\": \\\"copyPaste\\\",\\n  \\\"terminal.integrated.scrollback\\\": 10000000,\\n  \\\"terminal.integrated.shellIntegration.history\\\": 9999,\\n  \\\"terminal.integrated.shellIntegration.suggestEnabled\\\": true,\\n  \\\"editor.glyphMargin\\\": true,\\n  // \\\"editor.foldingImportsByDefault\\\": true,\\n  \\\"editor.unfoldOnClickAfterEndOfLine\\\": true,\\n  \\\"debug.allowBreakpointsEverywhere\\\": true,\\n  \\\"debug.console.closeOnEnd\\\": true,\\n  \\\"debug.openExplorerOnEnd\\\": true,\\n  // \\\"workbench.colorCustomizations\\\": {\\n  //     \\\"editor.selectionBackground\\\": \\\"#f54813\\\",\\n  //     \\\"editor.selectionHighlightBackground\\\": \\\"#139bf5\\\",\\n  //     \\\"statusBar.noFolderBackground\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.debuggingBackground\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.debuggingForeground\\\": \\\"#ffffff\\\",\\n  //     \\\"statusBar.background\\\": \\\"#5f00af\\\",\\n  //     \\\"statusBar.foreground\\\": \\\"#ffffff\\\"\\n  // },\\n  \\\"extensions.ignoreRecommendations\\\": true,\\n  // \\\"debug.onTaskErrors\\\": \\\"showErrors\\\",\\n  // \\\"debug.showBreakpointsInOverviewRuler\\\": true,\\n  \\\"highlightLine.borderColor\\\": \\\"#65EAB9\\\",\\n  \\\"highlightLine.borderWidth\\\": \\\"1px\\\",\\n  // \\\"vscodeGoogleTranslate.preferredLanguage\\\": \\\"Chinese (Simplified)\\\",\\n  \\\"git.inputValidationSubjectLength\\\": 10000,\\n  \\\"gnuGlobal.debugMode\\\": \\\"Enabled\\\",\\n  /*editor*/\\n  \\\"editor.cursorBlinking\\\": \\\"smooth\\\", //敲完一行代码自动格式化\\n  \\\"editor.smoothScrolling\\\": true, //使编辑器滚动变平滑\\n  \\\"editor.tabCompletion\\\": \\\"on\\\", //启用Tab补全\\n  // \\\"editor.fontFamily\\\": \\\"'Jetbrains Mono', '思源黑体'\\\",//字体设置，个人喜欢Jetbrains Mono作英文字体，思源黑体作中文字体\\n  \\\"editor.fontLigatures\\\": true, //复制代码时复制纯文本而不是连语法高亮都复制了\\n  \\\"editor.suggest.snippetsPreventQuickSuggestions\\\": false, //这个开不开效果好像都一样，据说是因为一个bug，建议关掉\\n  \\\"editor.stickyTabStops\\\": true, //在缩进上移动光标时四个空格一组来移动，就仿佛它们是制表符(\\\\t)一样\\n  \\\"editor.wordBasedSuggestions\\\": \\\"off\\\", //关闭基于文件中单词来联想的功能（语言自带的联想就够了，开了这个会导致用vscode写MarkDown时的中文引号异常联想）\\n  \\\"editor.linkedEditing\\\": true, //在文件内容溢出vscode显示区域时自动折行\\n  \\\"editor.cursorSmoothCaretAnimation\\\": \\\"on\\\", //让光标移动、插入变得平滑\\n  \\\"editor.renderControlCharacters\\\": true, //编辑器中显示不可见的控制字符\\n  \\\"editor.renderWhitespace\\\": \\\"boundary\\\", //除了两个单词之间用于分隔单词的一个空格，以一个小灰点的样子使空格可见\\n  /*terminal*/\\n  \\\"terminal.integrated.defaultProfile.windows\\\": \\\"Ubuntu-20.04 (WSL)\\\", //将终端设为cmd，个人比较喜欢cmd作为终端\\n  \\\"terminal.integrated.cursorBlinking\\\": true, //终端光标闪烁\\n  \\\"terminal.integrated.rightClickBehavior\\\": \\\"copyPaste\\\", //在终端中右键时显示菜单而不是粘贴（个人喜好）\\n  /*files*/\\n  \\\"files.autoGuessEncoding\\\": true, //让VScode自动猜源代码文件的编码格式\\n  \\\"files.autoSave\\\": \\\"onFocusChange\\\", //在编辑器失去焦点时自动保存，这使自动保存近乎达到“无感知”的体验\\n  \\\"files.exclude\\\": {\\n    \\\".stfolder\\\": true,\\n    \\\"**/.cache\\\": true,\\n    \\\"**/.DS_Store\\\": true,\\n    \\\"**/.hg\\\": true,\\n    \\\"**/.history\\\": true,\\n    \\\"**/.idea\\\": true,\\n    \\\"**/.lh\\\": true,\\n    \\\"**/.svn\\\": true,\\n    \\\"**/.vscode-ctags\\\": true,\\n    \\\"**/*.depend\\\": true,\\n    \\\"**/*.o\\\": true,\\n    \\\"**/bower_components\\\": true,\\n    \\\"**/CVS\\\": true,\\n    \\\"**/deeprobots_application_ros1\\\": true,\\n    \\\"**/GPATH\\\": true,\\n    \\\"**/GRTAGS\\\": true,\\n    \\\"**/GTAGS\\\": true,\\n    \\\"**/node_modules\\\": true,\\n    \\\"**/xiaoli*-ROS2*\\\": true,\\n    \\\"build\\\": true,\\n    \\\"deeprob_ws_ctrl\\\": true,\\n    \\\"install\\\": true,\\n    \\\"log\\\": true,\\n    \\\"src\\\": true\\n  },\\n  \\\"files.watcherExclude\\\": {\\n    //不索引一些不必要索引的大文件夹以减少内存和CPU消耗\\n    \\\"**/.git/objects/**\\\": true,\\n    \\\"**/.git/subtree-cache/**\\\": true,\\n    \\\"**/node_modules/**\\\": true,\\n    \\\"**/tmp/**\\\": true,\\n    \\\"**/bower_components/**\\\": true,\\n    \\\"**/dist/**\\\": true,\\n    \\\"**/.history\\\": true\\n  },\\n  //     \\\"files.exclude\\\": {\\n  //   \\\"**/*.depend\\\": true,\\n  //   \\\"**/*.o\\\": true,\\n  //   \\\"**/build_dir/toolchain-aarch64_cortex-a53_gcc-8.4.0_musl\\\": true\\n  // },\\n  \\\"search.exclude\\\": {\\n    \\\"**/.history\\\": true,\\n    \\\"**/deeprobots_application_ros1\\\": true\\n  },\\n  /*workbench*/\\n  \\\"workbench.list.smoothScrolling\\\": true, //打开文件时不是“预览”模式，即在编辑一个文件时打开编辑另一个文件不会覆盖当前编辑的文件而是新建一个标签页\\n  \\\"workbench.editor.wrapTabs\\\": true, //隐藏新建无标题文件时的“选择语言？”提示（个人喜好，可以删掉此行然后Ctrl+N打开无标题新文件看看不hidden的效果）\\n  /*explorer*/\\n  \\\"explorer.confirmDelete\\\": false, //删除文件时不弹出确认弹窗（因为很烦）\\n  \\\"explorer.confirmDragAndDrop\\\": false, //往左边文件资源管理器拖动东西来移动/复制时不显示确认窗口（因为很烦）\\n  /*search*/\\n  \\\"search.followSymlinks\\\": false, //据说可以减少vscode的CPU和内存占用\\n  /*window*/\\n  \\\"window.menuBarVisibility\\\": \\\"visible\\\", //在全屏模式下仍然显示窗口顶部菜单（没有菜单很难受）\\n  \\\"window.dialogStyle\\\": \\\"custom\\\", //每次调试都打开调试控制台，方便调试\\n  \\\"debug.showBreakpointsInOverviewRuler\\\": true, //固定调试时工具条的位置，防止遮挡代码内容（个人喜好）\\n  \\\"debug.saveBeforeStart\\\": \\\"nonUntitledEditorsInActiveGroup\\\", //在启动调试会话前保存除了无标题文档以外的文档（毕竟你创建了无标题文档就说明你根本没有想保存它的意思（至少我是这样的。））\\n  \\\"debug.onTaskErrors\\\": \\\"showErrors\\\", //预启动任务出错后显示错误，并不启动调试\\n  /*html*/\\n  \\\"html.format.indentHandlebars\\\": true,\\n  \\\"gitlens.showWelcomeOnInstall\\\": false,\\n  \\\"workbench.startupEditor\\\": \\\"none\\\",\\n  \\\"lldb.suppressUpdateNotifications\\\": true,\\n  \\\"terminal.integrated.enableMultiLinePasteWarning\\\": false,\\n  \\\"merge-conflict.autoNavigateNextConflict.enabled\\\": true,\\n  \\\"problems.decorations.enabled\\\": false,\\n  \\\"problems.defaultViewMode\\\": \\\"table\\\",\\n  \\\"editor.definitionLinkOpensInPeek\\\": true,\\n  \\\"markdown.preview.breaks\\\": true,\\n  \\\"files.associations\\\": {\\n    \\\"*.cmake\\\": \\\"makefile\\\",\\n    \\\"*.vim\\\": \\\"lua\\\"\\n  },\\n  \\\"projectManager.groupList\\\": true,\\n  \\\"window.customMenuBarAltFocus\\\": false,\\n  // 头部注释\\n  \\\"fileheader.customMade\\\": {\\n    // Author字段是文件的创建者 可以在specialOptions中更改特殊属性\\n    // 公司项目和个人项目可以配置不同的用户名与邮箱 搜索: gitconfig includeIf  比如: https://ayase.moe/2021/03/09/customized-git-config/\\n    // 自动提取当前git config中的: 用户名、邮箱\\n    \\\"Author\\\": \\\"git config user.name && git config user.email\\\", // 同时获取用户名与邮箱\\n    // \\\"Author\\\": \\\"git config user.name\\\", // 仅获取用户名\\n    // \\\"Author\\\": \\\"git config user.email\\\", // 仅获取邮箱\\n    // \\\"Author\\\": \\\"OBKoro1\\\", // 写死的固定值 不从git config中获取\\n    \\\"Date\\\": \\\"Do not edit\\\", // 文件创建时间(不变)\\n    // LastEditors、LastEditTime、FilePath将会自动更新 如果觉得时间更新的太频繁可以使用throttleTime(默认为1分钟)配置更改更新时间。\\n    \\\"LastEditors\\\": \\\"git config user.name && git config user.email\\\", // 文件最后编辑者 与Author字段一致\\n    // 由于编辑文件就会变更最后编辑时间，多人协作中合并的时候会导致merge\\n    // 可以将时间颗粒度改为周、或者月，这样冲突就减少很多。搜索变更时间格式: dateFormat\\n    \\\"LastEditTime\\\": \\\"Do not edit\\\", // 文件最后编辑时间\\n    // 输出相对路径，类似: /文件夹名称/src/index.js\\n    \\\"FilePath\\\": \\\"Do not edit\\\", // 文件在项目中的相对路径 自动更新\\n    // 插件会自动将光标移动到Description选项中 方便输入 Description字段可以在specialOptions更改\\n    \\\"Description\\\": \\\"\\\", // 介绍文件的作用、文件的入参、出参。\\n    // custom_string_obkoro1~custom_string_obkoro100都可以输出自定义信息\\n    // 可以设置多条自定义信息 设置个性签名、留下QQ、微信联系方式、输入空行等\\n    \\\"custom_string_obkoro1\\\": \\\"\\\",\\n    // 版权声明 保留文件所有权利 自动替换年份 获取git配置的用户名和邮箱\\n    // 版权声明获取git配置, 与Author字段一致: ${git_name} ${git_email} ${git_name_email}\\n    \\\"custom_string_obkoro1_copyright\\\": \\\"Copyright (c) ${now_year} by NevinXu, All Rights Reserved. \\\"\\n    // \\\"custom_string_obkoro1_copyright\\\": \\\"Copyright (c) ${now_year} by 写死的公司名/用户名, All Rights Reserved. \\\"\\n  },\\n  // 函数注释\\n  \\\"fileheader.cursorMode\\\": {\\n    \\\"description\\\": \\\"\\\", // 函数注释生成之后，光标移动到这里\\n    \\\"param\\\": \\\"\\\", // param 开启函数参数自动提取 需要将光标放在函数行或者函数上方的空白行\\n    \\\"return\\\": \\\"\\\"\\n  },\\n  // 插件配置项\\n  \\\"fileheader.configObj\\\": {\\n    \\\"autoAdd\\\": true, // 检测文件没有头部注释，自动添加文件头部注释\\n    \\\"autoAddLine\\\": 100, // 文件超过多少行数 不再自动添加头部注释\\n    \\\"autoAlready\\\": true, // 只添加插件支持的语言以及用户通过`language`选项自定义的注释\\n    \\\"supportAutoLanguage\\\": [], // 设置之后，在数组内的文件才支持自动添加\\n    // 自动添加头部注释黑名单\\n    \\\"prohibitAutoAdd\\\": [\\\"json\\\"],\\n    \\\"prohibitItemAutoAdd\\\": [\\n      \\\"项目的全称禁止项目自动添加头部注释, 使用快捷键自行添加\\\"\\n    ],\\n    \\\"folderBlacklist\\\": [\\\"node_modules\\\"], // 文件夹或文件名禁止自动添加头部注释\\n    \\\"wideSame\\\": false, // 头部注释等宽设置\\n    \\\"wideNum\\\": 13, // 头部注释字段长度 默认为13\\n    \\\"functionWideNum\\\": 0, // 函数注释等宽设置 设为0 即为关闭\\n    // 头部注释第几行插入\\n    \\\"headInsertLine\\\": {\\n      \\\"php\\\": 2 // php文件 插入到第二行\\n    },\\n    \\\"beforeAnnotation\\\": {}, // 头部注释之前插入内容\\n    \\\"afterAnnotation\\\": {}, // 头部注释之后插入内容\\n    \\\"specialOptions\\\": {}, // 特殊字段自定义 比如: Author、LastEditTime、LastEditors、FilePath、Description、Date等\\n    \\\"switch\\\": {\\n      \\\"newlineAddAnnotation\\\": true // 默认遇到换行符(\\\\r\\\\n \\\\n \\\\r)添加注释符号\\n    },\\n    \\\"moveCursor\\\": true, // 自动移动光标到Description所在行\\n    \\\"dateFormat\\\": \\\"YYYY-MM-DD HH:mm:ss\\\",\\n    \\\"atSymbol\\\": [\\\"@\\\", \\\"@\\\"], // 更改所有文件的自定义注释中的@符号\\n    \\\"atSymbolObj\\\": {}, //  更改单独语言/文件的@\\n    \\\"colon\\\": [\\\": \\\", \\\": \\\"], // 更改所有文件的注释冒号\\n    \\\"colonObj\\\": {}, //  更改单独语言/文件的冒号\\n    \\\"filePathColon\\\": \\\"路径分隔符替换\\\", // 默认值： mac: / window是: \\\\\\n    \\\"showErrorMessage\\\": false, // 是否显示插件错误通知 用于debugger\\n    \\\"writeLog\\\": false, // 错误日志生成\\n    \\\"CheckFileChange\\\": false, // 单个文件保存时进行diff检查\\n    \\\"createHeader\\\": false, // 新建文件自动添加头部注释\\n    \\\"useWorker\\\": false, // 是否使用工作区设置\\n    \\\"designAddHead\\\": false, // 添加注释图案时添加头部注释\\n    \\\"headDesignName\\\": \\\"random\\\", // 图案注释使用哪个图案\\n    \\\"headDesign\\\": false, // 是否使用图案注释替换头部注释\\n    // 自定义配置是否在函数内生成注释 不同文件类型和语言类型\\n    \\\"cursorModeInternalAll\\\": {}, // 默认为false 在函数外生成函数注释\\n    \\\"openFunctionParamsCheck\\\": true, // 开启关闭自动提取添加函数参数\\n    \\\"functionParamsShape\\\": [\\\"{\\\", \\\"}\\\"], // 函数参数外形自定义\\n    // \\\"functionParamsShape\\\": \\\"no type\\\" 函数参数不需要类型\\n    \\\"functionBlankSpaceAll\\\": {}, // 函数注释空格缩进 默认为空对象 默认值为0 不缩进\\n    \\\"functionTypeSymbol\\\": \\\"*\\\", // 参数没有类型时的默认值\\n    \\\"typeParamOrder\\\": \\\"type param\\\", // 参数类型 和 参数的位置自定义\\n    \\\"NoMatchParams\\\": \\\"no show param\\\", // 没匹配到函数参数，是否显示@param与@return这两行 默认不显示param\\n    \\\"functionParamAddStr\\\": \\\"\\\", // 在 type param 后面增加字符串 可能是冒号，方便输入参数描述\\n    // 自定义语言注释，自定义取消 head、end 部分\\n    // 不设置自定义配置language无效 默认都有head、end\\n    \\\"customHasHeadEnd\\\": {}, // \\\"cancel head and function\\\" | \\\"cancel head\\\" | \\\"cancel function\\\"\\n    \\\"throttleTime\\\": 60000, // 对同一个文件 需要过1分钟再次修改文件并保存才会更新注释\\n    // 自定义语言注释符号，覆盖插件的注释格式\\n    \\\"language\\\": {\\n      // js后缀文件\\n      \\\"js\\\": {\\n        \\\"head\\\": \\\"/$$\\\",\\n        \\\"middle\\\": \\\" $ @\\\",\\n        \\\"end\\\": \\\" $/\\\",\\n        // 函数自定义注释符号：如果有此配置 会默认使用\\n        \\\"functionSymbol\\\": {\\n          \\\"head\\\": \\\"/******* \\\", // 统一增加几个*号\\n          \\\"middle\\\": \\\" * @\\\",\\n          \\\"end\\\": \\\" */\\\"\\n        },\\n        \\\"functionParams\\\": \\\"typescript\\\" // 函数注释使用ts语言的解析逻辑\\n      },\\n      // 一次匹配多种文件后缀文件 不用重复设置\\n      \\\"h/hpp/cpp\\\": {\\n        \\\"head\\\": \\\"/*** \\\", // 统一增加几个*号\\n        \\\"middle\\\": \\\" * @\\\",\\n        \\\"end\\\": \\\" */\\\"\\n      },\\n      // 针对有特殊要求的文件如：test.blade.php\\n      \\\"blade.php\\\": {\\n        \\\"head\\\": \\\"<!--\\\",\\n        \\\"middle\\\": \\\" * @\\\",\\n        \\\"end\\\": \\\"-->\\\"\\n      }\\n    },\\n    // 默认注释  没有匹配到注释符号的时候使用。\\n    \\\"annotationStr\\\": {\\n      \\\"head\\\": \\\"/*\\\",\\n      \\\"middle\\\": \\\" * @\\\",\\n      \\\"end\\\": \\\" */\\\",\\n      \\\"use\\\": false\\n    }\\n  },\\n  \\\"git.openRepositoryInParentFolders\\\": \\\"always\\\",\\n  \\\"alias-skip.mappings\\\": {\\n    \\\"@\\\": \\\"/src\\\"\\n  },\\n  \\\"git.autofetch\\\": true,\\n  \\\"cmake.configureOnOpen\\\": false,\\n  \\\"git.ignoreRebaseWarning\\\": true,\\n  \\\"gitlens.graph.layout\\\": \\\"editor\\\",\\n  \\\"epitech-c-cpp-headers.username\\\": \\\"nevinxu\\\",\\n  \\\"epitech-c-cpp-headers.login\\\": \\\"\\\",\\n  \\\"epitech-c-cpp-headers.headerType\\\": \\\"post2017\\\",\\n  \\\"epitech-c-cpp-headers.usePragmaOnce\\\": false,\\n  \\\"epitech-c-cpp-headers.autoGenerateClasses\\\": true,\\n  \\\"epitech-c-cpp-headers.indentAccessSpecifiers\\\": true,\\n  \\\"git.fetchOnPull\\\": true,\\n  \\\"git.ignoreLimitWarning\\\": true,\\n  \\\"git.mergeEditor\\\": true,\\n  \\\"git.statusLimit\\\": 0,\\n  \\\"git.supportCancellation\\\": true,\\n  \\\"git.confirmSync\\\": false,\\n  \\\"todo-tree.filtering.ignoreGitSubmodules\\\": true,\\n  \\\"git.rememberPostCommitCommand\\\": true,\\n  \\\"doxdocgen.generic.useGitUserEmail\\\": true,\\n  \\\"doxdocgen.generic.useGitUserName\\\": true,\\n  \\\"gnuGlobal.autoUpdate\\\": \\\"Enabled\\\",\\n  \\\"editor.defaultFormatter\\\": \\\"xaver.clang-format\\\",\\n  \\\"alias-skip.allowedsuffix\\\": [\\\"js\\\", \\\"vue\\\", \\\"jsx\\\", \\\"ts\\\"],\\n  \\\"sync-rsync.exclude\\\": [\\\".git\\\"],\\n  \\\"local-history.daysLimit\\\": 0,\\n  \\\"local-history.maxDisplay\\\": 9999999999,\\n  \\\"gitlens.gitCommands.skipConfirmations\\\": [\\n    \\\"fetch:command\\\",\\n    \\\"stash-push:command\\\",\\n    \\\"switch:command\\\",\\n    \\\"push:command\\\"\\n  ],\\n  \\\"local-history.saveDelay\\\": 60,\\n  \\\"security.workspace.trust.untrustedFiles\\\": \\\"open\\\",\\n  \\\"sync-rsync.autoShowOutputOnError\\\": false,\\n  \\\"sync-rsync.local\\\": \\\"\\\",\\n  \\\"workbench.localHistory.maxFileEntries\\\": 1000000000000000000,\\n  \\\"workbench.localHistory.maxFileSize\\\": 1000000000000000000,\\n  \\\"local-history.treeLocation\\\": \\\"localHistory\\\",\\n  \\\"local-history.path\\\": \\\"${workspaceFolder}/.vscode\\\",\\n  \\\"workbench.commandPalette.preserveInput\\\": true,\\n  \\\"workbench.experimental.cloudChanges.autoStore\\\": \\\"onShutdown\\\",\\n  \\\"workbench.experimental.cloudChanges.partialMatches.enabled\\\": true,\\n  \\\"workbench.editor.limit.enabled\\\": true,\\n  \\\"workbench.settings.useSplitJSON\\\": true,\\n  \\\"sftp.debug\\\": true,\\n  \\\"sftp.printDebugLog\\\": true,\\n  \\\"localHistory.daysLimit\\\": 0,\\n  \\\"localHistory.exclude\\\": [\\n    \\\"**/.history/**\\\",\\n    \\\"**/node_modules/**\\\",\\n    \\\"**/typings/**\\\",\\n    \\\"**/out/**\\\",\\n    \\\"**/Code/User/**\\\"\\n  ],\\n  \\\"localHistory.maxDisplay\\\": 9999,\\n  \\\"C_Cpp.hover\\\": \\\"disabled\\\",\\n  // \\\"C_Cpp.intelliSenseEngine\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.formatting\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.doxygen.generateOnType\\\": false,\\n  \\\"C_Cpp.simplifyStructuredComments\\\": false,\\n  \\\"C_Cpp.codeAnalysis.clangTidy.codeAction.formatFixes\\\": false,\\n  \\\"C_Cpp.loggingLevel\\\": \\\"None\\\",\\n  \\\"timeline.pageOnScroll\\\": true,\\n  \\\"Codegeex.Privacy\\\": true,\\n  \\\"projectManager.openInNewWindowWhenClickingInStatusBar\\\": true,\\n  \\\"gitlens.graph.minimap.additionalTypes\\\": [\\n    \\\"localBranches\\\",\\n    \\\"remoteBranches\\\",\\n    \\\"tags\\\"\\n  ],\\n  \\\"remote.SSH.remotePlatform\\\": {\\n    \\\"192.168.12.131\\\": \\\"linux\\\",\\n    \\\"192.168.12.134\\\": \\\"linux\\\",\\n    \\\"192.168.12.100\\\": \\\"linux\\\",\\n    \\\"**************\\\": \\\"linux\\\",\\n    \\\"nevinxu.top\\\": \\\"linux\\\",\\n    \\\"192.168.2.4\\\": \\\"linux\\\",\\n    \\\"**************\\\": \\\"linux\\\",\\n    \\\"192.168.11.223\\\": \\\"linux\\\",\\n    \\\"192.168.11.11\\\": \\\"linux\\\",\\n    \\\"11022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"192.168.11.132\\\": \\\"linux\\\",\\n    \\\"10022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"20022.nevinxu.top\\\": \\\"linux\\\",\\n    \\\"127.0.0.1\\\": \\\"linux\\\",\\n    \\\"192.168.11.4\\\": \\\"linux\\\",\\n    \\\"nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.11.208\\\": \\\"linux\\\",\\n    \\\"192.168.1.130\\\": \\\"linux\\\",\\n    \\\"192.168.2.1\\\": \\\"linux\\\",\\n    \\\"48022.nevin.email\\\": \\\"linux\\\",\\n    \\\"50022.nevin.email\\\": \\\"linux\\\",\\n    \\\"ubuntu2204.nevin.email\\\": \\\"linux\\\",\\n    \\\"ubuntu2004.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.15.128\\\": \\\"linux\\\",\\n    \\\"46022.nevin.email\\\": \\\"linux\\\",\\n    \\\"47022.nevin.email\\\": \\\"linux\\\",\\n    \\\"45022.nevin.email\\\": \\\"linux\\\",\\n    \\\"49022.nevin.email\\\": \\\"linux\\\",\\n    \\\"43022.nevin.email\\\": \\\"linux\\\",\\n    \\\"44022.nevin.email\\\": \\\"linux\\\",\\n    \\\"54022.nevin.email\\\": \\\"linux\\\",\\n    \\\"52022.nevin.email\\\": \\\"linux\\\",\\n    \\\"51022.nevin.email\\\": \\\"linux\\\",\\n    \\\"pve.nevin.email\\\": \\\"linux\\\",\\n    \\\"58022.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.99.2\\\": \\\"linux\\\",\\n    \\\"10522.nevin.email\\\": \\\"linux\\\",\\n    \\\"20322.nevin.email\\\": \\\"linux\\\",\\n    \\\"192.168.51.14\\\": \\\"linux\\\",\\n    \\\"pve-remote\\\": \\\"linux\\\",\\n    \\\"work-remote\\\": \\\"linux\\\"\\n  },\\n  \\\"debug.showSubSessionsInToolBar\\\": true,\\n  \\\"debug.inlineValues\\\": \\\"on\\\",\\n  \\\"debug.showInStatusBar\\\": \\\"always\\\",\\n  \\\"search.useGlobalIgnoreFiles\\\": true,\\n  \\\"codestream.email\\\": \\\"<EMAIL>\\\",\\n  \\\"editor.bracketPairColorization.independentColorPoolPerBracketType\\\": true,\\n  \\\"editor.renderLineHighlightOnlyWhenFocus\\\": true,\\n  \\\"diffEditor.experimental.showMoves\\\": true,\\n  \\\"window.enableMenuBarMnemonics\\\": false,\\n  \\\"editor.codeActionsOnSave\\\": {},\\n  \\\"debug.internalConsoleOptions\\\": \\\"neverOpen\\\",\\n  \\\"todo-tree.general.debug\\\": true,\\n  \\\"editor.experimental.asyncTokenizationLogging\\\": true,\\n  \\\"debug.openDebug\\\": \\\"neverOpen\\\",\\n  \\\"markdown.trace.extension\\\": \\\"verbose\\\",\\n  \\\"settingsSync.ignoredSettings\\\": [\\\"-vim.autoSwitchInputMethod.switchIMCmd\\\"],\\n  \\\"workbench.editor.preferHistoryBasedLanguageDetection\\\": true,\\n  \\\"localHistory.saveDelay\\\": 60,\\n  \\\"localHistory.suppressErrors\\\": true,\\n  \\\"localHistory.alwaysExpand\\\": true,\\n  \\\"localHistory.dateLocale\\\": \\\"zh-CN\\\",\\n  \\\"git.timeline.showUncommitted\\\": true,\\n  \\\"clangd.checkUpdates\\\": false,\\n  \\\"editor.accessibilitySupport\\\": \\\"off\\\",\\n  \\\"dashboard.openOnStartup\\\": \\\"never\\\",\\n  \\\"marquee.configuration.workspaceLaunch\\\": true,\\n  \\\"marquee.widgets.github.since\\\": \\\"Daily\\\",\\n  \\\"marquee.widgets.weather.scale\\\": \\\"Celsius\\\",\\n  \\\"marquee.widgets.weather.city\\\": \\\"hangzhou\\\",\\n  \\\"diffEditor.maxComputationTime\\\": 0,\\n  /// clangd\\n  \\\"clangd.checkUpdates\\\": true,\\n  \\\"clangd.arguments\\\": [\\n    \\\"--background-index\\\",\\n    \\\"--compile-commands-dir=${workspaceFolder}\\\",\\n    \\\"-j=12\\\",\\n    \\\"--clang-tidy\\\",\\n    \\\"--clang-tidy-checks=performance-*,bugprone-*\\\",\\n    \\\"--all-scopes-completion\\\",\\n    \\\"--completion-style=detailed\\\",\\n    \\\"--header-insertion=iwyu\\\",\\n    \\\"--pch-storage=disk\\\"\\n  ],\\n  \\\"diffEditor.renderSideBySide\\\": false,\\n  \\\"workbench.settings.applyToAllProfiles\\\": [\\n    \\\"editor.defaultFormatter\\\",\\n    \\\"editor.formatOnSave\\\",\\n    \\\"editor.formatOnType\\\",\\n    \\\"C_Cpp.codeFolding\\\",\\n    \\\"C_Cpp.configurationWarnings\\\",\\n    \\\"C_Cpp.formatting\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.function\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.block\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.lambda\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.namespace\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeOpenBrace.type\\\",\\n    \\\"C_Cpp.vcFormat.newLine.beforeWhileInDoWhile\\\",\\n    \\\"C_Cpp.vcFormat.newLine.scopeBracesOnSeparateLines\\\",\\n    \\\"vim.statusBarColorControl\\\",\\n    \\\"clangd.arguments\\\",\\n    \\\"clang-format.assumeFilename\\\",\\n    \\\"editor.fontFamily\\\",\\n    \\\"terminal.integrated.fontFamily\\\"\\n  ],\\n  \\\"highlightLine.borderStyle\\\": \\\"dashed\\\",\\n  \\\"Codegeex.Explanation.LanguagePreference\\\": \\\"zh-CN\\\",\\n  \\\"makefile.buildBeforeLaunch\\\": false,\\n  \\\"cmake.allowCommentsInPresetsFile\\\": true,\\n  \\\"cmake.automaticReconfigure\\\": false,\\n  \\\"cmake.ctest.allowParallelJobs\\\": true,\\n  \\\"cmake.parseBuildDiagnostics\\\": false,\\n  \\\"editor.formatOnSaveMode\\\": \\\"modificationsIfAvailable\\\",\\n  \\\"editor.minimap.autohide\\\": true,\\n  \\\"editor.minimap.size\\\": \\\"fill\\\",\\n  \\\"editor.cursorStyle\\\": \\\"block\\\",\\n  \\\"editor.autoIndent\\\": \\\"advanced\\\",\\n  \\\"editor.defaultColorDecorators\\\": \\\"auto\\\",\\n  \\\"editor.foldingImportsByDefault\\\": true,\\n  \\\"editor.inlayHints.padding\\\": true,\\n  \\\"editor.stickyScroll.enabled\\\": true,\\n  \\\"editor.wrappingIndent\\\": \\\"indent\\\",\\n  \\\"files.readonlyFromPermissions\\\": true,\\n  \\\"workbench.editor.defaultBinaryEditor\\\": \\\"hexEditor.hexedit\\\",\\n  \\\"workbench.editor.enablePreviewFromCodeNavigation\\\": true,\\n  \\\"workbench.editor.enablePreviewFromQuickOpen\\\": true,\\n  \\\"application.experimental.rendererProfiling\\\": true,\\n  \\\"notebook.outline.showCodeCells\\\": true,\\n  \\\"outline.collapseItems\\\": \\\"alwaysCollapse\\\",\\n  \\\"vim.useSystemClipboard\\\": true,\\n  \\\"vim.cursorStylePerMode.insert\\\": \\\"line\\\",\\n  \\\"[json]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"debug.console.acceptSuggestionOnEnter\\\": \\\"on\\\",\\n  \\\"debug.terminal.clearBeforeReusing\\\": true,\\n  \\\"vim.highlightedyank.enable\\\": true,\\n  \\\"colorTabs.activityBarBackground\\\": true,\\n  \\\"colorTabs.titleBackground\\\": true,\\n  \\\"colorTabs.titleLabel\\\": true,\\n  \\\"colorTabs.tabBackground\\\": true,\\n  \\\"colorTabs.ignoreCase\\\": true,\\n  \\\"localHistory.absolute\\\": true,\\n  \\\"[cmake]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"cheshirekow.cmake-format\\\"\\n  },\\n  \\\"remote.autoForwardPorts\\\": false,\\n  \\\"cmake.buildDirectory\\\": \\\"${workspaceFolder}/build/vscode\\\",\\n  \\\"remote.forwardOnOpen\\\": false,\\n  \\\"remote.restoreForwardedPorts\\\": false,\\n  \\\"workbench.editor.empty.hint\\\": \\\"hidden\\\",\\n  \\\"dotnetAcquisitionExtension.existingDotnetPath\\\": [\\\"/usr/bin/dotnet\\\"],\\n  \\\"EnglishChineseDictionary.enableHover\\\": true,\\n  \\\"cmake.format.allowOptionalArgumentIndentation\\\": true,\\n  \\\"cmake.format.spaceAfterCommandName\\\": true,\\n  \\\"cmake.format.spaceInParentheses\\\": true,\\n  \\\"json.format.enable\\\": false,\\n  \\\"vim.autoSwitchInputMethod.enable\\\": true,\\n\\n  \\\"workbench.editorAssociations\\\": {\\n    \\\"*.copilotmd\\\": \\\"vscode.markdown.preview.editor\\\",\\n    \\\"*.png\\\": \\\"default\\\",\\n    \\\"*.docx\\\": \\\"default\\\"\\n  },\\n  \\\"diffEditor.ignoreTrimWhitespace\\\": false,\\n  \\\"clang-format.assumeFilename\\\": \\\"/mine/AOS-NET/.clang-format\\\",\\n  \\\"vscode-office.editorLanguage\\\": \\\"zh_CN\\\",\\n  \\\"sync-rsync.autoHideOutput\\\": true,\\n  \\\"prettier.proseWrap\\\": \\\"never\\\",\\n  \\\"[javascript]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"cmake.showOptionsMovedNotification\\\": false,\\n  \\\"codeium.enableConfig\\\": {\\n    \\\"*\\\": true\\n  },\\n\\n  \\\"colon\\\": [0, 1], // The first number specify how much space to add to the left, can be negative.\\n  // The second number is how much space to the right, can be negative.\\n  \\\"assignment\\\": [1, 1], // The same as above.\\n  \\\"arrow\\\": [1, 1], // The same as above.\\n  \\\"comment\\\": 2, // Special how much space to add between the trailing comment and the code.\\n  // If this value is negative, it means don't align the trailing comment.\\n\\n  \\\"dotnetAcquisitionExtension.installTimeoutValue\\\": 60000,\\n\\\"vim.cursorStylePerMode.normal\\\": \\\"block\\\",\\n  \\\"clangd.multiProject.enabled\\\": true,\\n  \\\"Codegeex.Comment.LanguagePreference\\\": \\\"中文\\\",\\n  \\\"C_Cpp.intelliSenseEngine\\\": \\\"disabled\\\",\\n  \\\"searchEverywhere.shouldInitOnStartup\\\": true,\\n  \\\"searchEverywhere.shouldHighlightSymbol\\\": true,\\n  \\\"searchEverywhere.shouldDisplayNotificationInStatusBar\\\": true,\\n  \\\"[c]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"xaver.clang-format\\\"\\n  },\\n  \\\"gitHistory.sideBySide\\\": true,\\n  \\\"C_Cpp.autocomplete\\\": \\\"disabled\\\",\\n  \\\"extensions.experimental.affinity\\\": {\\n    \\\"asvetliakov.vscode-neovim\\\": 1\\n  },\\n  \\\"editor.wordWrap\\\": \\\"on\\\",\\n  \\\"vim.statusBarColorControl\\\": true,\\n  \\\"C_Cpp.configurationWarnings\\\": \\\"disabled\\\",\\n  \\\"C_Cpp.codeFolding\\\": \\\"disabled\\\",\\n  \\\"scm.alwaysShowActions\\\": true,\\n  \\\"terminal.integrated.accessibleViewPreserveCursorPosition\\\": true,\\n  \\\"ime-and-cursor.EnglishIM\\\": \\\"1\\\",\\n  \\\"ime-and-cursor.obtainIMCmd\\\": \\\"fcitx5-remote\\\",\\n  \\\"ime-and-cursor.switchIMCmd\\\": \\\"fcitx5-remote  -t {im}\\\",\\n  \\\"ime-and-cursor.ChineseIM\\\": \\\"2\\\",\\n  \\\"ime-and-cursor.useWithVim\\\": true,\\n  \\\"ime-and-cursor.helpVim\\\": true,\\n  \\\"ime-and-cursor.keepChecking\\\": 2000,\\n  \\\"terminal.integrated.fontFamily\\\": \\\"Hack Nerd Font Mono\\\",\\n  \\\"accessibility.hideAccessibleView\\\": true,\\n  \\\"[jsonc]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\"\\n  },\\n  \\\"terminal.integrated.stickyScroll.enabled\\\": true,\\n  \\\"debug.closeReadonlyTabsOnEnd\\\": true,\\n  \\\"terminal.explorerKind\\\": \\\"external\\\",\\n  \\\"scm.alwaysShowRepositories\\\": true,\\n\\\"Lingma.LocalStoragePath\\\": \\\"/home/<USER>/.lingma\\\",\\n  \\\"terminal.integrated.allowChords\\\": false,\\n  \\\"makefile.configureOnOpen\\\": false,\\n  \\\"cmake.pinnedCommands\\\": [\\n    \\\"workbench.action.tasks.configureTaskRunner\\\",\\n    \\\"workbench.action.tasks.runTask\\\"\\n  ],\\n  \\\"workbench.colorCustomizations\\\": {\\n    \\\"statusBar.noFolderBackground\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.debuggingBackground\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.debuggingForeground\\\": \\\"#ffffff\\\",\\n    \\\"editor.selectionBackground\\\": \\\"#f54813\\\",\\n    \\\"editor.selectionHighlightBackground\\\": \\\"#139bf5\\\",\\n    \\\"editorCursor.foreground\\\": \\\"#ff0015\\\",\\n    \\\"terminalCursor.foreground\\\": \\\"#FF0000\\\",\\n    \\\"statusBar.background\\\": \\\"#005f5f\\\",\\n    \\\"statusBar.foreground\\\": \\\"#ffffff\\\"\\n  },\\n  \\\"codestream.serverUrl\\\": \\\"https://codestream-api-v2-us1.service.newrelic.com\\\",\\n  \\\"vscode-office.editorTheme\\\": \\\"Dracula\\\",\\n  \\\"terminal.integrated.ignoreBracketedPasteMode\\\": true,\\n  \\\"git.autoStash\\\": true,\\n  \\\"git.ignoreMissingGitWarning\\\": true,\\n  \\\"Codegeex.License\\\": \\\"\\\",\\n  \\\"window.confirmSaveUntitledWorkspace\\\": false,\\n  \\\"vim.smartRelativeLine\\\": true,\\n  \\\"gitlens.views.scm.grouped.views\\\": {\\n    \\\"commits\\\": true,\\n    \\\"branches\\\": true,\\n    \\\"remotes\\\": true,\\n    \\\"stashes\\\": false,\\n    \\\"tags\\\": true,\\n    \\\"worktrees\\\": true,\\n    \\\"contributors\\\": true,\\n    \\\"repositories\\\": false,\\n    \\\"searchAndCompare\\\": false,\\n    \\\"launchpad\\\": false\\n  },\\n  \\\"[python]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"ms-python.autopep8\\\"\\n  },\\n  \\\"redhat.telemetry.enabled\\\": true,\\n  \\\"outline-map.debug\\\": true,\\n  \\\"outline-map.findRef.enabled\\\": true,\\n  \\\"outline-map.findRef.uesFindImpl\\\": true,\\n  \\\"[cpp]\\\": {\\n    \\\"editor.defaultFormatter\\\": \\\"llvm-vs-code-extensions.vscode-clangd\\\"\\n  },\\n  \\\"ros.distro\\\": \\\"humble\\\",\\n  // \\\"vim.enableNeovim\\\": true,\\n  \\\"vim.history\\\": 500,\\n  \\\"github.copilot.advanced\\\": {},\\n\\n}\"}", "keybindings": "{\"keybindings\":\"[]\",\"platform\":2}", "extensions": "[{\"identifier\":{\"id\":\"003.autojs6-vscode-ext\",\"uuid\":\"b7e568dc-8ea5-4b33-adbd-7bbd38f65ea9\"},\"displayName\":\"AutoJs6 VSCode Extension\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"1nvitr0.valgrind-task-integration\",\"uuid\":\"32f333d2-e06d-47c0-a41e-c1d41c73e2bc\"},\"displayName\":\"Valgrind Task Integration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"aaron-bond.better-comments\",\"uuid\":\"7a0110bb-231a-4598-aa1b-0769ea46d28b\"},\"displayName\":\"Better Comments\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"adam-bender.commit-message-editor\",\"uuid\":\"b9883563-e6ba-4f6c-b03c-193c80e79c75\"},\"displayName\":\"Commit Message Editor\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"adriano-markovic.c-cpp-makefile-project\",\"uuid\":\"19b11fcc-98f5-4d18-8276-af1d587b535f\"},\"displayName\":\"C/C++ Makefile Project\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ajshort.include-autocomplete\",\"uuid\":\"ced3fc99-2817-4ac0-a24a-87abfecbc359\"},\"displayName\":\"Include Autocomplete\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ajshort.msg\",\"uuid\":\"37da4556-ca94-4d5a-8b9a-84c480a57fb3\"},\"displayName\":\"Msg Language Support\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"akiramiyakoda.cppincludeguard\",\"uuid\":\"a55f38e7-f712-4efb-a461-90a84bd1d169\"},\"displayName\":\"C/C++ Include Guard\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alanmbarr.quotify\",\"uuid\":\"24333107-fe83-459b-8678-27faa19b2d96\"},\"displayName\":\"quotify\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"alefragnani.bookmarks\",\"uuid\":\"b689fcc8-d494-4dbf-a228-2c694a578afc\"},\"displayName\":\"Bookmarks\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alefragnani.project-manager\",\"uuid\":\"1b747f06-3789-4ebd-ac99-f1fe430c3347\"},\"displayName\":\"Project Manager\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"alessandrosofia.ros2-topic-viewer\",\"uuid\":\"0d7e55f8-aeed-46d7-80dd-7d375ccaab56\"},\"displayName\":\"ROS2 Topic Viewer\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alexclewontin.make-lsp-vscode\",\"uuid\":\"8363f172-600c-4816-9ef0-56a6f3458bdd\"},\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alexey-strakh.stackoverflow-search\",\"uuid\":\"c30a2bd6-039e-4dce-aed4-77afff770f87\"},\"displayName\":\"Stackoverflow Instant Search\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"alibaba-cloud.tongyi-lingma\",\"uuid\":\"b90347d7-e182-4b37-8314-4cde59e48d27\"},\"displayName\":\"Lingma - Alibaba Cloud AI Coding Assistant\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"aminer.codegeex\",\"uuid\":\"b91906a1-03a8-46a2-9b63-bf81bc854a30\"},\"displayName\":\"CodeGeeX: AI Coding Assistant\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"anjali.clipboard-history\",\"uuid\":\"c529c9f4-005a-45b0-9fb9-89e0d9b8b990\"},\"displayName\":\"Clipboard History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"antiantisepticeye.vscode-color-picker\",\"uuid\":\"f9598416-bd11-4a41-a1b3-5e842c9de867\"},\"displayName\":\"vscode-color-picker\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"asvetliakov.vscode-neovim\",\"uuid\":\"caf8995c-5426-4bf7-9d01-f7968ebd49bb\"},\"displayName\":\"VSCode Neovim\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"batisteo.vscode-django\",\"uuid\":\"4b41a5a8-170e-4156-b2c0-10efb270abbc\"},\"displayName\":\"Django\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"bdavs.expect\",\"uuid\":\"c02af6e9-b7f3-4d81-b6ba-14583080ee28\"},\"displayName\":\"Expect\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"beishanyufu.ime-and-cursor\",\"uuid\":\"06a5c854-af21-420f-b5e1-dac55eb9b679\"},\"displayName\":\"IME and Cursor\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"benrogerswpg.websearchengine\",\"uuid\":\"cb0fbc6e-09e5-4c8d-b914-f75328a316bb\"},\"displayName\":\"Web Search\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"bierner.color-info\",\"uuid\":\"0f8bd812-5d52-4fff-bd1c-6b0a7ee9cad9\"},\"displayName\":\"Color Info\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"bierner.markdown-mermaid\",\"uuid\":\"f8d0ffc4-66bb-4a9c-8149-ef8f043691a1\"},\"displayName\":\"Markdown Preview Mermaid Support\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"bukas.gbktoutf8\",\"uuid\":\"51604e47-0bb3-43c0-9226-a74eeca7cd84\"},\"displayName\":\"GBKtoUTF8\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"buuug7.gbk2utf8\",\"uuid\":\"d3b03ca3-b9be-4e26-ad84-6b464d023314\"},\"displayName\":\"GBK to UTF8 for vscode\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"cheshirekow.cmake-format\",\"uuid\":\"5338f4d8-048c-433f-bce4-44aa922cedc2\"},\"displayName\":\"cmake-format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"chouzz.vscode-better-align\",\"uuid\":\"f5a6ecde-96ce-4fde-8744-ab88c7727069\"},\"displayName\":\"Better Align\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"chris-noring.node-snippets\",\"uuid\":\"fa17bcf3-8d28-4673-9e23-c448c372e20c\"},\"displayName\":\"node-snippets\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"christian-kohler.path-intellisense\",\"uuid\":\"a41c1549-4053-44d4-bf30-60fc809b4a86\"},\"displayName\":\"Path Intellisense\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"cliffordfajardo.highlight-line-vscode\",\"uuid\":\"0a65801d-48ea-40e8-840a-279ca1283dcb\"},\"displayName\":\"Highlight Line \",\"applicationScoped\":false},{\"identifier\":{\"id\":\"codeinchinese.englishchinesedictionary\",\"uuid\":\"7a93f1bf-1a86-441e-bc0b-4548f5a3cfbb\"},\"displayName\":\"翻译(英汉词典)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"codeium.codeium\",\"uuid\":\"acab4f40-b6db-42ec-bcd1-01802cbdd988\"},\"displayName\":\"Windsurf Plugin (formerly Codeium): AI Coding Autocomplete and Chat for Python, JavaScript, TypeScript, and more\",\"disabled\":true,\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"codestream.codestream\",\"uuid\":\"a59a33cb-9f43-4f1a-890f-5d400cb82389\"},\"displayName\":\"New Relic CodeStream\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"codezombiech.gitignore\",\"uuid\":\"3e891cf9-53cb-49a3-8d01-8f0b1f0afb29\"},\"displayName\":\"gitignore\",\"version\":\"0.10.0\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"coolchyni.beyond-debug\",\"uuid\":\"4c6a1625-43cf-4b46-ac0b-eedb77aba254\"},\"displayName\":\"GDB Debugger - Beyond\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"cschlosser.doxdocgen\",\"uuid\":\"da7e26d5-d57c-4742-ab47-d77fb189e195\"},\"displayName\":\"Doxygen Documentation Generator\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ctf0.local-history-new\",\"uuid\":\"2374c478-3694-46bf-a207-7421a2633ba0\"},\"displayName\":\"Local History (New)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ctf0.makefile-symbol-provider\",\"uuid\":\"3fc110f3-a8b7-4e45-85f0-997b7f5e902f\"},\"displayName\":\"Makefile Symbol Provider\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"cuixiaorui.cvim\",\"uuid\":\"94d3b1bd-737b-4a14-bfb2-9b51c2228d3d\"},\"displayName\":\"CVim\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"cweijan.vscode-office\",\"uuid\":\"936b1be7-8595-4f76-b102-aa6bb915da73\"},\"displayName\":\"Office Viewer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"damiankoper.gdb-debug\",\"uuid\":\"497d7078-f844-42d0-b773-2226b6be78b1\"},\"displayName\":\"GDB Debug\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"danielpinto8zz6.c-cpp-compile-run\",\"uuid\":\"f3b153f9-13ba-4992-9f90-1779d968df49\"},\"displayName\":\"C/C++ Compile Run\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"darkriszty.markdown-table-prettify\",\"uuid\":\"136682fc-7ac4-43b7-a50a-bb7890c39f25\"},\"displayName\":\"Markdown Table Prettifier\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"david-rickard.git-diff-and-merge-tool\",\"uuid\":\"ed6b358a-4167-4eb8-a07c-ec72cbdbc503\"},\"displayName\":\"Git Diff and Merge Tool\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"davidanson.vscode-markdownlint\",\"uuid\":\"daf8b44d-8aae-4da2-80c5-1f770219f643\"},\"displayName\":\"markdownlint\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"deitry.colcon-helper\",\"uuid\":\"65caa881-4cb8-4d55-8ce0-a026684ad423\"},\"displayName\":\"Colcon Tasks\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"docker.docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker DX\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.git-extension-pack\",\"uuid\":\"d23dc2c3-abed-47d8-9b22-7dd31c1f0ed9\"},\"displayName\":\"Git Extension Pack\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.githistory\",\"uuid\":\"5960f38e-0bbe-4644-8f9c-9c8824e82511\"},\"displayName\":\"Git History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-environment-manager\",\"uuid\":\"0c9f60fd-5588-42f7-9176-e80c3ae111ec\"},\"displayName\":\"Python Environment Manager (deprecated)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-extension-pack\",\"uuid\":\"f5188937-53e0-45bb-a16d-61231003fa3b\"},\"displayName\":\"Python Extension Pack\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dotiful.dotfiles-syntax-highlighting\",\"uuid\":\"ffc6f6b3-5082-40f2-9dbd-fe38971dcd41\"},\"displayName\":\"Dotfiles Syntax Highlighting\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"draivin.hscopes\",\"uuid\":\"cbe19d3b-d390-40f9-92a5-561a1f132249\"},\"displayName\":\"HyperScopes\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dzhavat.bracket-pair-toggler\",\"uuid\":\"e052b2e6-71ab-4cb7-8a29-75d6e38ecb8d\"},\"displayName\":\"Bracket Pair Colorization Toggler\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"eamodio.gitlens\",\"uuid\":\"4de763bd-505d-4978-9575-2b7696ecf94e\"},\"displayName\":\"GitLens — Git supercharged\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"editorconfig.editorconfig\",\"uuid\":\"f60a60a6-95ba-42d4-b41c-3d24c1b89588\"},\"displayName\":\"EditorConfig for VS Code\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"emmanuelbeziat.vscode-great-icons\",\"uuid\":\"829a192d-496c-44ac-87f3-0a84ce36a853\"},\"displayName\":\"VSCode Great Icons\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"esbenp.prettier-vscode\",\"uuid\":\"96fa4707-6983-4489-b7c5-d5ffdfdcce90\"},\"displayName\":\"Prettier - Code formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"exiasr.hadolint\",\"uuid\":\"c0f361c5-4179-43c5-9cb5-c4eba77388b4\"},\"displayName\":\"hadolint\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"extr0py.vscode-relative-line-numbers\",\"uuid\":\"4297bbe6-97c8-4ffa-b133-37a31f971277\"},\"displayName\":\"Relative Line Numbers\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"fabiospampinato.vscode-todo-plus\",\"uuid\":\"0661e679-8a32-433e-baeb-8980f53edb55\"},\"displayName\":\"Todo+\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"felipecaputo.git-project-manager\",\"uuid\":\"2bba45f2-4a48-41a6-bb86-d6ba5ab29a9f\"},\"displayName\":\"Git Project Manager\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"formulahendry.auto-rename-tag\",\"uuid\":\"6e440e71-8ed9-4f25-bb78-4b13096b8a03\"},\"displayName\":\"Auto Rename Tag\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"foxundermoon.shell-format\",\"uuid\":\"5fb19573-2183-4cf2-b53d-0fb869dae7ae\"},\"displayName\":\"shell-format\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"franneck94.c-cpp-runner\",\"uuid\":\"0f4eac03-b4ec-4a55-bce1-deb41fde32ab\"},\"displayName\":\"C/C++ Runner\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"gengjian1203.code-maker\",\"uuid\":\"4b9e1a44-db92-4aa3-a355-e95a437012ad\"},\"displayName\":\"Code Maker\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"george-alisson.html-preview-vscode\",\"uuid\":\"18b0ddc7-5d9c-4604-a7fa-10e66a2cb10d\"},\"displayName\":\"HTML Preview\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"gerrnperl.outline-map\",\"uuid\":\"926f4730-3fd1-4d09-be10-56e7402560c5\"},\"displayName\":\"Outline Map\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.copilot\",\"uuid\":\"23c4aeee-f844-43cd-b53e-1113e483f1a6\"},\"displayName\":\"GitHub Copilot\",\"disabled\":true,\"applicationScoped\":true},{\"identifier\":{\"id\":\"github.copilot-chat\",\"uuid\":\"7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f\"},\"displayName\":\"GitHub Copilot Chat\",\"disabled\":true,\"applicationScoped\":true},{\"identifier\":{\"id\":\"go2sh.cmake-integration-vscode\",\"uuid\":\"3677198f-c671-4381-9aa5-b25ad8e20629\"},\"displayName\":\"CMake Integration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"golang.go\",\"uuid\":\"d6f6cfea-4b6f-41f4-b571-6ad2ab7918da\"},\"displayName\":\"Go\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"gruntfuggly.global-config\",\"uuid\":\"9a3d81ec-d459-4139-aa0f-d07452e0e894\"},\"displayName\":\"Global Config\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"gruntfuggly.todo-tree\",\"uuid\":\"261cac81-cd7b-4555-bb41-0c2d2bcd3e70\"},\"displayName\":\"Todo Tree\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"guyutongxue.cpp-reference\",\"uuid\":\"aad6f61c-e4ed-4c3f-a823-307be9931d0e\"},\"displayName\":\"Cpp Reference\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"hancel.google-translate\",\"uuid\":\"eb4df9c1-f1e9-4f1d-a28c-1cde16bb8767\"},\"displayName\":\"Google Translate\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"hars.cppsnippets\",\"uuid\":\"946b6c3e-cb86-4b44-b716-30bcb64ca9f1\"},\"displayName\":\"C/C++ Snippets\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"hnw.vscode-auto-open-markdown-preview\",\"uuid\":\"ccf9294f-f78f-429a-8856-387624db2432\"},\"displayName\":\"Auto-Open Markdown Preview\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"howardzuo.vscode-git-tags\",\"uuid\":\"7accc875-0223-4da4-bb87-412164d18c2b\"},\"displayName\":\"Git Tags\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"huizhou.githd\",\"uuid\":\"e4e21a5c-a882-4d7d-b8d8-cfe05b1436c6\"},\"displayName\":\"Git History Diff\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ibm.output-colorizer\",\"uuid\":\"113b22c8-8125-42ec-8c6b-80c3f5d5fa5f\"},\"displayName\":\"Output Colorizer\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"in4margaret.compareit\",\"uuid\":\"bf5d633c-03fa-4439-9e53-6b4d0c430fcf\"},\"displayName\":\"compareit\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ionutvmi.path-autocomplete\",\"uuid\":\"0d1241e3-fa60-4f24-8f2a-6d7085677c48\"},\"displayName\":\"Path Autocomplete\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"jacobdufault.fuzzy-search\",\"uuid\":\"c2ebe7f7-8974-4ceb-a4a5-aea798305313\"},\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jaehyunshim.vscode-ros2\",\"uuid\":\"c1dddd0b-2e82-44ab-b3d5-a1c36a802f86\"},\"displayName\":\"ROS2\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jannchie.codetime\",\"uuid\":\"79d8e45d-5812-4b3d-8f14-f31b8ebb9570\"},\"displayName\":\"CodeTime\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jannek-aalto.shell-function-outline\",\"uuid\":\"9fbafb2b-d41d-4ab0-82e5-6ffc71ee4eba\"},\"displayName\":\"Shell function outline\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jeff-hykin.better-c-syntax\",\"uuid\":\"4046f8e1-9ff4-423a-9c89-a7061602f13c\"},\"displayName\":\"Better C Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jeff-hykin.better-cpp-syntax\",\"uuid\":\"73767f91-7dcb-43cb-90b3-596d073eea1f\"},\"displayName\":\"Better C++ Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jeff-hykin.better-dockerfile-syntax\",\"uuid\":\"************************************\"},\"displayName\":\"Better DockerFile Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"jeff-hykin.better-shellscript-syntax\",\"uuid\":\"f27ad139-0ff0-4fea-a7c3-d1af80eed6ae\"},\"displayName\":\"Better Shell Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"josetr.cmake-language-support-vscode\",\"uuid\":\"dce141da-393b-4514-9f89-18dc85ccc626\"},\"displayName\":\"CMake Language Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"jsshou.note-sync\",\"uuid\":\"dab25f83-7cb5-47b0-99de-19e93f61ac98\"},\"displayName\":\"Note Sync\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"julianiaquinandi.nvim-ui-modifier\",\"uuid\":\"8bc0b3c7-1630-4ff9-bc52-568e38ad699f\"},\"displayName\":\"Neovim Ui Modifier\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"kbysiec.vscode-search-everywhere\",\"uuid\":\"e97951de-d158-4723-920f-d5c4af21b058\"},\"displayName\":\"Search everywhere\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"kevinrose.vsc-python-indent\",\"uuid\":\"f3cbfb84-b1e1-40ff-b70f-************\"},\"displayName\":\"Python Indent\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"keyring.lua\",\"uuid\":\"d4d8d0a4-54f0-4f10-b8fb-b53f1575c53c\"},\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"kr4is.cpptools-extension-pack\",\"uuid\":\"4c5a69a1-cddc-49a9-a97a-ba5c56722881\"},\"displayName\":\"C/C++ Extension Pack\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"krinql.krinql-vscode\",\"uuid\":\"13e95aa9-c6a4-4415-909d-f724eae22ea8\"},\"displayName\":\"Krinql\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"l-i-v.mql-tools\",\"uuid\":\"c59f46aa-4d17-4341-b138-7478744e7e77\"},\"displayName\":\"MQL Tools\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"leojhonsong.ccpp-extension-pack\",\"uuid\":\"6f0f5b32-3877-4e88-a585-b9deb24cfc43\"},\"displayName\":\"C/C++ Extension Pack\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"lglong519.terminal-tools\",\"uuid\":\"9a352b23-efe3-4e21-9674-43ff1d56abe1\"},\"displayName\":\"Terminal Tools\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"lihuiwang.vue-alias-skip\",\"uuid\":\"e50baa47-85ae-4615-af4a-4d5de18b19b7\"},\"displayName\":\"别名路径跳转\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"llvm-vs-code-extensions.vscode-clangd\",\"uuid\":\"103154cb-b81d-4e1b-8281-c5f4fa563d37\"},\"displayName\":\"clangd\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"madmous.makefile-command-runner\",\"uuid\":\"c5d15a62-fd5a-413b-abc5-7a32244c585d\"},\"displayName\":\"Makefile Command Runner\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mads-hartmann.bash-ide-vscode\",\"uuid\":\"b1b0f283-8246-4e90-832e-a6c409d378d1\"},\"displayName\":\"Bash IDE\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"maelvalais.autoconf\",\"uuid\":\"3ca928a4-41a2-409c-81be-e58c381fce5a\"},\"displayName\":\"autoconf\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"maketes.git-patch-utility\",\"uuid\":\"848c7f4c-f625-4d01-af83-9ffc4befbdfb\"},\"displayName\":\"Git Patch Utility\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"marscode.marscode-extension\",\"uuid\":\"f290d85b-b5cc-451c-a3a0-9cdf457cdbad\"},\"displayName\":\"Trae AI: Coding Assistant\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mccarter.start-git-bash\",\"uuid\":\"3382cdf2-af9d-4b7d-9008-3c22969d6a8c\"},\"displayName\":\"Start git-bash\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"meronz.manpages\",\"uuid\":\"37bba40e-c9e1-493c-ad7a-eb23f3f914df\"},\"displayName\":\"Manpages\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mhutchie.git-graph\",\"uuid\":\"438221f8-1107-4ccd-a6fe-f3b7fe0856b7\"},\"displayName\":\"Git Graph\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"microhobby.linuxkerneldev\",\"uuid\":\"f6be665f-**************-1de4f2d565b1\"},\"displayName\":\"Embedded Linux Kernel Dev\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mintlify.document\",\"uuid\":\"de6400ad-0c12-434b-aa5a-6017c93dac64\"},\"displayName\":\"Mintlify Doc Writer for Python, JavaScript, TypeScript, C++, PHP, Java, C#, Ruby & more\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mishkinf.vscode-edits-history\",\"uuid\":\"e20faa33-48ee-4a1c-862d-349e055969b7\"},\"displayName\":\"Navigate Edits History - Goto Last Edit\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mitaki28.vscode-clang\",\"uuid\":\"f8dbe169-d649-4e4b-adb8-ffa113c1d616\"},\"displayName\":\"C/C++ Clang Command Adapter\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"mkxml.vscode-filesize\",\"uuid\":\"21b3e09e-b3f7-4e20-9302-50039286650d\"},\"displayName\":\"filesize\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mohsen1.prettify-json\",\"uuid\":\"67e66172-30c7-4478-8f5d-6eac4ae755dc\"},\"displayName\":\"Prettify JSON\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"moshfeu.compare-folders\",\"uuid\":\"03241f90-1c77-402a-b17f-1d3cee943969\"},\"displayName\":\"Compare Folders\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"moshfeu.diff-merge\",\"uuid\":\"68b929c5-4adb-451b-9ce0-d2797f56b925\"},\"displayName\":\"Diff & Merge\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-ceintl.vscode-language-pack-zh-hans\",\"uuid\":\"e4ee7751-6514-4731-9cdb-7580ffa9e70b\"},\"displayName\":\"Chinese (Simplified) (简体中文) Language Pack for Visual Studio Code\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"ms-dotnettools.vscode-dotnet-runtime\",\"uuid\":\"1aab81a1-b3d9-4aef-976b-577d5d90fe3f\"},\"displayName\":\".NET Install Tool\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-edgedevtools.vscode-edge-devtools\",\"uuid\":\"11cb120c-f665-45bc-a21e-6de8a5685850\"},\"displayName\":\"Microsoft Edge Tools for VS Code\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-iot.vscode-ros\",\"uuid\":\"30ca9f40-2642-457b-9170-cd706e62f02f\"},\"displayName\":\"ROS\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.autopep8\",\"uuid\":\"5a21b0c3-89ca-46dd-8ada-658518fb94a5\"},\"displayName\":\"autopep8\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.debugpy\",\"uuid\":\"4bd5d2c9-9d65-401a-b0b2-7498d9f17615\"},\"displayName\":\"Python Debugger\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.isort\",\"uuid\":\"4ad0ce32-ff3f-49f0-83b5-93e5dc00cfff\"},\"displayName\":\"isort\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.vscode-pylance\",\"uuid\":\"364d2426-116a-433a-a5d8-a5098dc3afbd\"},\"displayName\":\"Pylance\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter\",\"uuid\":\"6c2f1801-1e7f-45b2-9b5c-7782f1e076e8\"},\"displayName\":\"Jupyter\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-keymap\",\"uuid\":\"9f6dc8db-620c-4844-b8c5-e74914f1be27\"},\"displayName\":\"Jupyter Keymap\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-renderers\",\"uuid\":\"b15c72f8-d5fe-421a-a4f7-27ed9f6addbf\"},\"displayName\":\"Jupyter Notebook Renderers\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-containers\",\"uuid\":\"93ce222b-5f6f-49b7-9ab1-a0463c6238df\"},\"displayName\":\"Dev Containers\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh\",\"uuid\":\"607fd052-be03-4363-b657-2bd62b83d28a\"},\"displayName\":\"Remote - SSH\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh-edit\",\"uuid\":\"bfeaf631-bcff-4908-93ed-fda4ef9a0c5c\"},\"displayName\":\"Remote - SSH: Editing Configuration Files\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-wsl\",\"uuid\":\"f0c5397b-d357-4197-99f0-cb4202f22818\"},\"displayName\":\"WSL\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.vscode-remote-extensionpack\",\"uuid\":\"23d72dfc-8dd1-4e30-926e-8783b4378f13\"},\"displayName\":\"Remote Development\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.azure-repos\",\"uuid\":\"25cfa506-1433-4595-a73f-61666807126d\"},\"displayName\":\"Azure Repos\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.cmake-tools\",\"uuid\":\"7c889349-8749-43d4-8b5e-08939936d7f4\"},\"displayName\":\"CMake Tools\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.cpptools\",\"uuid\":\"690b692e-e8a9-493f-b802-8089d50ac1b2\"},\"displayName\":\"C/C++\",\"disabled\":true,\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.cpptools-extension-pack\",\"uuid\":\"3957b2f6-f086-49b5-a7b4-5da772123130\"},\"displayName\":\"C/C++ Extension Pack\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.cpptools-themes\",\"uuid\":\"99b17261-8f6e-45f0-9ad5-a69c6f509a4f\"},\"displayName\":\"C/C++ Themes\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.hexeditor\",\"uuid\":\"cc7d2112-5178-4472-8e0e-25dced95e7f0\"},\"displayName\":\"Hex Editor\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.makefile-tools\",\"uuid\":\"e09cf600-90a1-414e-92a0-031f1a5391c6\"},\"displayName\":\"Makefile Tools\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.powershell\",\"uuid\":\"40d39ce9-c381-47a0-80c8-a6661f731eab\"},\"displayName\":\"PowerShell\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-explorer\",\"uuid\":\"11858313-52cc-4e57-b3e4-d7b65281e34b\"},\"displayName\":\"Remote Explorer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-repositories\",\"uuid\":\"cf5142f0-3701-4992-980c-9895a750addf\"},\"displayName\":\"Remote Repositories\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-server\",\"uuid\":\"105c0b3c-07a9-4156-a4fc-4141040eb07e\"},\"displayName\":\"Remote - Tunnels\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.vscode-speech\",\"uuid\":\"e6610e16-9699-4e1d-a5d7-9bb1643db131\"},\"displayName\":\"VS Code Speech\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"natizyskunk.sftp\",\"uuid\":\"fe74bfaf-4260-454f-981c-6541d1d31822\"},\"displayName\":\"SFTP\",\"disabled\":true,\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"naumovs.color-highlight\",\"uuid\":\"121396ad-85a1-45ec-9fd1-d95028a847f5\"},\"displayName\":\"Color Highlight\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"nicolaspolomack.epitech-c-cpp-headers\",\"uuid\":\"963437ec-71b8-43bb-9839-32971656ca78\"},\"displayName\":\"EPITECH C/C++ Headers\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"nonanonno.vscode-ros2\",\"uuid\":\"a243dbe2-060a-42e0-84a2-e2d177fa174b\"},\"displayName\":\"ROS2\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"obkoro1.korofileheader\",\"uuid\":\"0398f8a5-da8e-4c21-ae4d-12a40652a517\"},\"displayName\":\"koroFileHeader\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"oderwat.indent-rainbow\",\"uuid\":\"eaa2127d-cb69-4ab9-8505-a60c9ee5f28b\"},\"applicationScoped\":false},{\"identifier\":{\"id\":\"orangex4.vscode-smart-ime\",\"uuid\":\"fff4572f-7774-45d3-a173-d1f85e1d1e0b\"},\"displayName\":\"Smart IME\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"orepor.color-tabs-vscode-ext\",\"uuid\":\"f266b05e-14dc-4514-a584-36ecd07305ba\"},\"displayName\":\"ColorTabs\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"paragdiwan.gitpatch\",\"uuid\":\"1c2d0e20-1a4e-4d22-a8bb-be29a274bf49\"},\"displayName\":\"Git Patch\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"parthr2031.colorful-comments\",\"uuid\":\"593914aa-670d-4885-965f-1539224f8339\"},\"displayName\":\"Colorful Comments\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"penagos.vgdb\",\"uuid\":\"f5bf9ebf-1160-4674-931d-fb287e77dbd0\"},\"displayName\":\"vGDB\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"pierre-payen.gdb-syntax\",\"uuid\":\"b4b82417-3b77-4262-ad03-4b36cbc6bd54\"},\"displayName\":\"GDB syntax\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"pkief.material-icon-theme\",\"uuid\":\"5db78037-f674-459f-a236-db622c427c5b\"},\"displayName\":\"Material Icon Theme\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"rajivshah01234.make-runner\",\"uuid\":\"adac0240-4940-42b2-ab2a-a565f1815f2d\"},\"displayName\":\"Makefile Runner\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rangav.vscode-thunder-client\",\"uuid\":\"2fd56207-78ef-49d4-95d2-9b801eee4dbf\"},\"displayName\":\"Thunder Client\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.vscode-yaml\",\"uuid\":\"2061917f-f76a-458a-8da9-f162de22b97e\"},\"displayName\":\"YAML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redjue.git-commit-plugin\",\"uuid\":\"04270998-5ec4-47ab-abad-46e2d14ee590\"},\"displayName\":\"git-commit-plugin\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"remisa.shellman\",\"uuid\":\"2783c7a6-dc32-471d-ab7f-863ca5e8deb3\"},\"displayName\":\"shellman\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"richterger.perl\",\"uuid\":\"effbf376-b9ad-4395-9b0e-8cf0537fbf04\"},\"displayName\":\"Perl\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"rlivings39.fzf-quick-open\",\"uuid\":\"b0ed338a-0ae4-40bc-abc8-30541d98e90e\"},\"displayName\":\"fzf fuzzy quick open\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"rogalmic.bash-debug\",\"uuid\":\"f4f0748b-9f86-461e-84f9-73bf2e1f91c2\"},\"displayName\":\"Bash Debug\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"rpinski.shebang-snippets\",\"uuid\":\"73b80ea3-7f20-4eb7-ab8c-e70ac36224df\"},\"displayName\":\"Shebang Snippets\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"rsbondi.highlight-words\",\"uuid\":\"0774aeb9-5809-4b36-9289-f852f253d6f9\"},\"displayName\":\"highlight-words\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ryu1kn.edit-with-shell\",\"uuid\":\"370f75d3-459d-498a-80ed-e380c87750c5\"},\"displayName\":\"Edit with Shell Command\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ryu1kn.partial-diff\",\"uuid\":\"79afa437-682e-4fa2-a4fd-87844832a1dc\"},\"displayName\":\"Partial Diff\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sandcastle.vscode-open\",\"uuid\":\"84d4a562-c19b-4b30-b8f8-8e02eaf02fa2\"},\"displayName\":\"Open\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"scootersoftware.bcompare-vscode\",\"uuid\":\"ee6f8a8d-2194-430a-bb92-b6b0d4d60a2b\"},\"displayName\":\"Beyond Compare\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sensecoder.mql5filestemplatewizard\",\"uuid\":\"0ec0bd44-a0b1-4499-8f59-3a5df8ab0889\"},\"displayName\":\"MQL5 Files Template Wizard\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"shakram02.bash-beautify\",\"uuid\":\"3cd12c20-0143-473f-b7f3-0d5f15cb7249\"},\"displayName\":\"Bash Beautify\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"shardulm94.trailing-spaces\",\"uuid\":\"6ad45f5a-09ec-44e5-b363-867ddc1ec674\"},\"displayName\":\"Trailing Spaces\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"shd101wyy.markdown-preview-enhanced\",\"uuid\":\"3b1db1fc-c7f7-4bd6-9fa4-b499dfa99a8a\"},\"displayName\":\"Markdown Preview Enhanced\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"shopify.ruby-lsp\",\"uuid\":\"ef656a13-3703-477b-9f36-672a0c949b96\"},\"displayName\":\"Ruby LSP\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sirtori.indenticator\",\"uuid\":\"fcbdb08e-4048-40e8-a674-fecc476f4b93\"},\"displayName\":\"Indenticator\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sleistner.vscode-fileutils\",\"uuid\":\"d637104e-1fd7-4063-98fc-8afe46012c9b\"},\"displayName\":\"File Utils\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"slhsxcmy.vscode-double-line-numbers\",\"uuid\":\"e276460b-41fb-4b5d-a107-f72cfb9d5105\"},\"displayName\":\"Double Line Numbers\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sourcegraph.sourcegraph\",\"uuid\":\"6faf9533-e655-435d-8157-13d5e285d271\"},\"displayName\":\"Search by Sourcegraph\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"stannum.stannum-makefile-syntax\",\"uuid\":\"e916d532-a0d2-4cc2-87fd-ac4999f6dd7c\"},\"displayName\":\"Stannum Makefile Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"stateful.marquee\",\"uuid\":\"c64f81d3-a4bd-4afc-b116-31a5f58a0c11\"},\"displayName\":\"Marquee\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"streetsidesoftware.code-spell-checker\",\"uuid\":\"f6dbd813-b0a0-42c1-90ea-10dde9d925a7\"},\"displayName\":\"Code Spell Checker\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"sumneko.lua\",\"uuid\":\"3a15b5a7-be12-47e3-8445-88ee3eabc8b2\"},\"displayName\":\"Lua\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"syw-sustech.high-ros2-snippets\",\"uuid\":\"1f5862b3-5a06-43fb-9c35-4856a0cecaad\"},\"displayName\":\"High ROS2 Snippets\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tabnine.tabnine-vscode\",\"uuid\":\"75da638c-c45a-44ea-aa3b-8570a3559810\"},\"displayName\":\"Tabnine AI\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tadayosi.vscode-makefile-outliner\",\"uuid\":\"ed100a48-ec9e-4306-bc9d-1f7bd42d814d\"},\"displayName\":\"Makefile Outliner\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"taiyuuki.chinese-color\",\"uuid\":\"06c55939-21c6-4304-9841-7945fc109936\"},\"displayName\":\"Chinese Colors\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"tatosjb.fuzzy-search\",\"uuid\":\"91284ae9-ef73-468f-8157-4ddaae91b956\"},\"displayName\":\"Fuzzy search\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tdennis4496.cmantic\",\"uuid\":\"0e95c0fe-a577-4091-8339-9faece454473\"},\"displayName\":\"C-mantic\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"tencent-cloud.coding-copilot\",\"uuid\":\"f505f28f-327d-4169-9890-6fda3c27f66c\"},\"displayName\":\"腾讯云代码助手 CodeBuddy\",\"version\":\"3.1.12\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"tetradresearch.vscode-h2o\",\"uuid\":\"1319eaac-22a7-4792-96d4-6d73b58ebea7\"},\"displayName\":\"Shell Script Command Completion\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"timonwong.shellcheck\",\"uuid\":\"f95d8fff-f70a-4ae5-bb06-5c47ddbc8fc6\"},\"displayName\":\"ShellCheck\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"truman.autocomplate-shell\",\"uuid\":\"f4d3cd15-6434-4267-a9d3-7e5f08c174da\"},\"displayName\":\"AutoComplate shell\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"uctakeoff.vscode-counter\",\"uuid\":\"7789bd61-a874-4bf0-a8b4-d9e8d920af63\"},\"displayName\":\"VS Code Counter\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"unbug.codelf\",\"uuid\":\"485e8574-a825-4190-9589-bb6837b4eebe\"},\"displayName\":\"Codelf\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"usernamehw.errorlens\",\"uuid\":\"9d8c32ab-354c-4daf-a9bf-20b633734435\"},\"displayName\":\"Error Lens\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"vadimcn.vscode-lldb\",\"uuid\":\"bee31e34-a44b-4a76-9ec2-e9fd1439a0f6\"},\"displayName\":\"CodeLLDB\",\"version\":\"1.11.4\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vincaslt.highlight-matching-tag\",\"uuid\":\"aae00486-7e92-42b3-91b0-2b419e4f3875\"},\"displayName\":\"Highlight Matching Tag\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"vinirossa.vscode-gitandgithub-pack\",\"uuid\":\"dc1a111a-a14f-43c0-b414-1bdfbdeb9a7c\"},\"displayName\":\"Git & GitHub Extension Pack\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"visualstudioexptteam.intellicode-api-usage-examples\",\"uuid\":\"9fa2a00e-3bfa-4c2a-abc4-a865bb2b5cf3\"},\"displayName\":\"IntelliCode API Usage Examples\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"visualstudioexptteam.vscodeintellicode\",\"uuid\":\"876e8f93-74d0-4f4f-91b7-34a09f19f444\"},\"displayName\":\"IntelliCode\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-ext.sync-rsync\",\"uuid\":\"c7cbc356-60a8-4b32-8eb3-d6058aa0e627\"},\"displayName\":\"Sync-Rsync\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-icons-team.vscode-icons\",\"uuid\":\"9ccc1dd7-7ec4-4a46-bd4f-7d7b8b9d322a\"},\"displayName\":\"vscode-icons\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscodevim.vim\",\"uuid\":\"d96e79c6-8b25-4be3-8545-0e0ecefcae03\"},\"displayName\":\"Vim\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vsls-contrib.gitdoc\",\"uuid\":\"7be174a5-ac74-4496-bf8b-8cc6cc60408c\"},\"displayName\":\"GitDoc\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"webfreak.debug\",\"uuid\":\"2fd22b8e-b3b8-4e7f-9a28-a5e2d1bdd0d4\"},\"displayName\":\"Native Debug\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"wenhaohuang.code-counter\",\"uuid\":\"6f0cfda0-f57c-4d0c-b326-cf6c2aaefeee\"},\"displayName\":\"Code Counter\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"wholroyd.jinja\",\"uuid\":\"c941a679-d500-46a8-b2a9-************\"},\"displayName\":\"Jinja\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"wintersteiger.hex-bin-dec\",\"uuid\":\"18f9037b-a8b0-4ec0-b2d5-3a8f86477293\"},\"displayName\":\"hex-bin-dec\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"wlhe.c-cpp-snippets\",\"uuid\":\"5ec229d2-66e5-4d59-8d71-9e103164cf9f\"},\"displayName\":\"C/Cpp Snippets\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"woozy-masta.shell-script-ide\",\"uuid\":\"373b1850-1e5e-4449-8f95-478c99f06976\"},\"displayName\":\"Shell script IDE\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"xaver.clang-format\",\"uuid\":\"04208f17-8aed-4d49-b0a2-fbce829efbeb\"},\"displayName\":\"Clang-Format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"xen.ccpp-theme\",\"uuid\":\"affe3756-3e43-4e57-930b-e3e3e7c44fa4\"},\"displayName\":\"C/C++ Theme\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"xpo.local-history\",\"uuid\":\"2930ad9a-d82e-4a32-8c4c-452c7ea5ec59\"},\"displayName\":\"Local History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"xshrim.txt-syntax\",\"uuid\":\"c5900888-28e9-40d6-84d0-d1645e62f4cc\"},\"displayName\":\"Txt Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"xyz.local-history\",\"uuid\":\"25adc849-5c3a-4dca-95b3-55a6461077ee\"},\"displayName\":\"Local History\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"yzhang.markdown-all-in-one\",\"uuid\":\"98790d67-10fa-497c-9113-f6c7489207b2\"},\"displayName\":\"Markdown All in One\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zainchen.json\",\"uuid\":\"311c4d7f-e81f-47f8-9956-6a9919ddef43\"},\"displayName\":\"json\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"zchrissirhcz.cmake-highlight\",\"uuid\":\"b84547cc-dc67-41ee-8322-4346fab316c7\"},\"displayName\":\"CMake Highlight\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zenor.makefile-creator\",\"uuid\":\"128ef024-4d99-43d6-9b09-488f8d186cbd\"},\"displayName\":\"Makefile Creator\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"zhaouv.vscode-markdown-everywhere\",\"uuid\":\"f56661a9-0f9e-4d0d-ba0b-4b1054b9ec61\"},\"displayName\":\"Markdown Everywhere\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zhuangtongfa.material-theme\",\"uuid\":\"26a529c9-2654-4b95-a63f-02f6a52429e6\"},\"displayName\":\"One Dark Pro\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ziyasal.vscode-open-in-github\",\"uuid\":\"1d4d80c9-0d33-48a1-88a8-19ff80bc8e2a\"},\"displayName\":\"Open in GitHub, Bitbucket, Gitlab, VisualStudio.com !\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"zobo.php-intellisense\",\"uuid\":\"3eddb68d-a5dd-43fb-be03-6f508d98fe97\"},\"displayName\":\"PHP IntelliSense\",\"disabled\":true,\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.panel.chat.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.typeHierarchyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.memoryUsage\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clangd.ast\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"txtsyntaxHighlightExplore\\\",\\\"isHidden\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"filters\\\",\\\"isHidden\\\":false,\\\"order\\\":3}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"clangd.typeHierarchyView\\\",\\\"isHidden\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"clangd.memoryUsage\\\",\\\"isHidden\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"clangd.ast\\\",\\\"isHidden\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"registeredCommands\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"txtsyntaxHighlightExplore\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"filters\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"thunder-client-sidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"treeLocalHistoryExplorer\\\",\\\"isHidden\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"go.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"go.package.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.sync\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.activityBar.location\":\"default\",\"workbench.statusbar.hidden\":\"[\\\"status.workspaceTrust.1694152563774\\\",\\\"status.workspaceTrust.1694152933738\\\",\\\"status.workspaceTrust.1694152942530\\\",\\\"status.workspaceTrust.d886b2d78dde614933bf6c8f02c405e5\\\",\\\"status.workspaceTrust.cd934ded0e3613df5096483ebae2b7f6\\\",\\\"status.workspaceTrust.1694168745420\\\",\\\"status.workspaceTrust.1694333418672\\\",\\\"status.workspaceTrust.1694333532021\\\",\\\"status.workspaceTrust.1694393411418\\\",\\\"status.workspaceTrust.1694480755391\\\",\\\"status.workspaceTrust.1694480772890\\\",\\\"status.workspaceTrust.1694483015220\\\",\\\"status.workspaceTrust.8090d8e6f09bf3d4146939a50607d447\\\",\\\"status.workspaceTrust.1694484416987\\\",\\\"status.workspaceTrust.1694484426069\\\",\\\"status.workspaceTrust.04136605a9dd73d9caad9563de44cab6\\\",\\\"status.workspaceTrust.1694485066699\\\",\\\"status.workspaceTrust.b5f9273703edec19013a0bc9dd17ebb8\\\",\\\"status.workspaceTrust.1694567343000\\\",\\\"status.workspaceTrust.1694567540492\\\",\\\"status.workspaceTrust.4f3acc81184adbf49615434fb0428171\\\",\\\"status.workspaceTrust.1694570796599\\\",\\\"status.workspaceTrust.7b71023d7bcad6fa34614bac7137fa75\\\",\\\"status.workspaceTrust.b529fd44f3d6dcee3be4a34121a2928b\\\",\\\"status.workspaceTrust.1694528461228\\\",\\\"status.workspaceTrust.1694612202383\\\",\\\"status.workspaceTrust.1694613500035\\\",\\\"status.workspaceTrust.abcd0d85c72dcdb7b54456716430a6cb\\\",\\\"status.workspaceTrust.21a763d14b2107e9dc8ebf819cf36be7\\\",\\\"status.workspaceTrust.8015f2e02c052790738e2048803016d9\\\",\\\"status.workspaceTrust.1694697439538\\\",\\\"status.workspaceTrust.fb87a4755c3e53075e747af822e0f7e0\\\",\\\"status.workspaceTrust.1694697783178\\\",\\\"status.workspaceTrust.1694738771039\\\",\\\"status.workspaceTrust.1694738796961\\\",\\\"status.workspaceTrust.1694738893662\\\",\\\"status.workspaceTrust.1694738907348\\\",\\\"status.workspaceTrust.1694757796857\\\",\\\"status.workspaceTrust.1694758254470\\\",\\\"status.workspaceTrust.1694758279286\\\",\\\"status.workspaceTrust.1694759865606\\\",\\\"status.workspaceTrust.792216a17cea1a6000622837ca644b0a\\\",\\\"status.workspaceTrust.6b4ecc8238b7a1b605642f5facc47357\\\",\\\"status.workspaceTrust.08bca756d826977e1b3c907e908ff11f\\\",\\\"status.workspaceTrust.1694786842091\\\",\\\"status.workspaceTrust.a7a372835a3c027ef8350a5cc4f0a4c2\\\",\\\"status.workspaceTrust.1694826847214\\\",\\\"status.workspaceTrust.1694832826304\\\",\\\"status.workspaceTrust.1694832912948\\\",\\\"status.workspaceTrust.f06cd7915085bcff5215b36aa6762508\\\",\\\"status.workspaceTrust.1694872351741\\\",\\\"status.workspaceTrust.105c8ae3a17c7b24eb83d24563935010\\\",\\\"status.workspaceTrust.1694879429596\\\",\\\"status.workspaceTrust.1694880763024\\\",\\\"status.workspaceTrust.1694880906320\\\",\\\"status.workspaceTrust.1694922073376\\\",\\\"status.workspaceTrust.1694936969724\\\",\\\"status.workspaceTrust.1694937148435\\\",\\\"status.workspaceTrust.1694937206768\\\",\\\"status.workspaceTrust.1694998682148\\\",\\\"status.workspaceTrust.1694998926549\\\",\\\"status.workspaceTrust.ae38400cdac3b89d6a1d583079c7ac35\\\",\\\"status.workspaceTrust.1695003091245\\\",\\\"status.workspaceTrust.f75243f7a72dab7233d1367dc53f9757\\\",\\\"status.workspaceTrust.1695003774236\\\",\\\"status.workspaceTrust.68ecf6934982c2ad925acc98cf51793a\\\",\\\"status.workspaceTrust.4ae2839b3850e9ecdf3bcb8cb85bd5c4\\\",\\\"status.workspaceTrust.1695039216529\\\",\\\"status.workspaceTrust.1695191825129\\\",\\\"status.workspaceTrust.d85492dca0fd49476621bc4471995bbf\\\",\\\"status.workspaceTrust.0f6c49149a8d666c2c72bd2c638a2dff\\\",\\\"status.workspaceTrust.1695389788486\\\",\\\"status.workspaceTrust.1695389811966\\\",\\\"status.workspaceTrust.5a6a92f3c7b0276d819beba303ded89b\\\",\\\"status.workspaceTrust.3cec2b31ebefe681b5a800c53be4d00d\\\",\\\"status.workspaceTrust.23d48def710217135001c7f519534f20\\\",\\\"status.workspaceTrust.1695436418191\\\",\\\"status.workspaceTrust.b86dc36288847023f32771e863f8fd4f\\\",\\\"status.workspaceTrust.1695521053338\\\",\\\"status.workspaceTrust.1695817226443\\\",\\\"status.workspaceTrust.d34afea8166a01e0cd8f094464e1346e\\\",\\\"status.workspaceTrust.1695820997382\\\",\\\"status.workspaceTrust.1695823394584\\\",\\\"status.workspaceTrust.1695876506413\\\",\\\"status.workspaceTrust.1695879180003\\\",\\\"status.workspaceTrust.f4d852e0738407f3fb68b94415e55c8b\\\",\\\"status.workspaceTrust.1695882982100\\\",\\\"status.workspaceTrust.1695883005411\\\",\\\"status.workspaceTrust.31d7148787518bc03c2e153040584b83\\\",\\\"status.workspaceTrust.aaf109fdbdb102efdb280c0ee4237fad\\\",\\\"status.workspaceTrust.1695884915003\\\",\\\"status.workspaceTrust.1695885448454\\\",\\\"status.workspaceTrust.1695885453444\\\",\\\"status.workspaceTrust.1695887599664\\\",\\\"status.workspaceTrust.1695970156955\\\",\\\"status.workspaceTrust.1695973023420\\\",\\\"status.workspaceTrust.1695974952891\\\",\\\"status.workspaceTrust.1695975403406\\\",\\\"status.workspaceTrust.1695975443885\\\",\\\"status.workspaceTrust.1695975574977\\\",\\\"status.workspaceTrust.1695975586060\\\",\\\"status.workspaceTrust.1695975832040\\\",\\\"status.workspaceTrust.1695975865951\\\",\\\"status.workspaceTrust.1695975887223\\\",\\\"status.workspaceTrust.1695976105394\\\",\\\"status.workspaceTrust.1695976119647\\\",\\\"status.workspaceTrust.1695976228998\\\",\\\"status.workspaceTrust.1695976243927\\\",\\\"status.workspaceTrust.1696406545543\\\",\\\"status.workspaceTrust.1696643053020\\\",\\\"status.workspaceTrust.1696644242968\\\",\\\"status.workspaceTrust.1696648358609\\\",\\\"status.workspaceTrust.1696665151683\\\",\\\"status.workspaceTrust.405e5ac9c7898e728a455b55d9c39e96\\\",\\\"status.workspaceTrust.1696665526721\\\",\\\"status.workspaceTrust.bd9373e7fb8bb493c718bc16997ec5bf\\\",\\\"status.workspaceTrust.1696684674723\\\",\\\"status.workspaceTrust.1696685167326\\\",\\\"status.workspaceTrust.1696685187473\\\",\\\"status.workspaceTrust.1696687102078\\\",\\\"status.workspaceTrust.1696728819647\\\",\\\"status.workspaceTrust.1696728829365\\\",\\\"status.workspaceTrust.4afce446e3bbf3694d1cd5f8188e065f\\\",\\\"status.workspaceTrust.1696730712603\\\",\\\"status.workspaceTrust.1696843849067\\\",\\\"status.workspaceTrust.1696843947880\\\",\\\"status.workspaceTrust.b5866944d197b673bfaa63418113c13e\\\",\\\"status.workspaceTrust.1696923322078\\\",\\\"status.workspaceTrust.1696940455316\\\",\\\"status.workspaceTrust.f39d48b837f7db903b2fad5a62e2597b\\\",\\\"status.workspaceTrust.1696941024135\\\",\\\"status.workspaceTrust.1696941086886\\\",\\\"status.workspaceTrust.34eb29b8d8839099c51e37ee9c5ee885\\\",\\\"status.workspaceTrust.072fead77ba78d7bb7101a8735c243be\\\",\\\"status.workspaceTrust.1696985712224\\\",\\\"status.workspaceTrust.1696986472937\\\",\\\"status.workspaceTrust.1696986552196\\\",\\\"status.workspaceTrust.1697031958361\\\",\\\"status.workspaceTrust.1697072375058\\\",\\\"status.workspaceTrust.1697072387801\\\",\\\"status.workspaceTrust.1697072918673\\\",\\\"status.workspaceTrust.1697072936812\\\",\\\"status.workspaceTrust.f19b518609cb11dc549f3828bee65072\\\",\\\"status.workspaceTrust.858f1a22787e650fed6ebbd0e7fc700d\\\",\\\"status.workspaceTrust.1697101184726\\\",\\\"status.workspaceTrust.9da858628a673221ac04d4ceb906ea3f\\\",\\\"status.workspaceTrust.1697118566327\\\",\\\"status.workspaceTrust.1697118713584\\\",\\\"status.workspaceTrust.3d43c6320e1760112036be801329ffd7\\\",\\\"status.workspaceTrust.1697209242545\\\",\\\"status.workspaceTrust.1697209306035\\\",\\\"status.workspaceTrust.1697209340890\\\",\\\"status.workspaceTrust.36f144f4b6e4944305e7775baebf73a7\\\",\\\"status.workspaceTrust.1697347119003\\\",\\\"status.workspaceTrust.1697352017068\\\",\\\"status.workspaceTrust.1697357108067\\\",\\\"status.workspaceTrust.1697534109150\\\",\\\"status.workspaceTrust.c2c8295787ca568f46c9e2713ddcb28a\\\",\\\"status.workspaceTrust.1697541757278\\\",\\\"status.workspaceTrust.1697584902599\\\",\\\"status.workspaceTrust.1697591239423\\\",\\\"status.workspaceTrust.343801a5e9b3d22ca9a5a0039e82446b\\\",\\\"status.workspaceTrust.b83c3423039d61366c1b82f809959493\\\",\\\"status.workspaceTrust.55df77d25d994100f6491b67263653f8\\\",\\\"status.workspaceTrust.1697704664394\\\",\\\"status.workspaceTrust.7fc34ad7f9de17346702d3c51304dd4c\\\",\\\"status.workspaceTrust.1697861840083\\\",\\\"status.workspaceTrust.1697862955221\\\",\\\"status.workspaceTrust.1697898027141\\\",\\\"status.workspaceTrust.d9fad5d8c1ae3561b688a13fd07584bb\\\",\\\"status.workspaceTrust.1697900423515\\\",\\\"status.workspaceTrust.1697900877780\\\",\\\"status.workspaceTrust.1697938333304\\\",\\\"status.workspaceTrust.788211b3f12dfd636963e326ca65cedf\\\",\\\"status.workspaceTrust.1697957565681\\\",\\\"status.workspaceTrust.a0d7b19472df2ce15ec83c84237fc5cc\\\",\\\"status.workspaceTrust.1698022914600\\\",\\\"status.workspaceTrust.1698110410095\\\",\\\"status.workspaceTrust.1698117901278\\\",\\\"status.workspaceTrust.1698117907226\\\",\\\"status.workspaceTrust.9831f1bc932fa45e25111f16ecaf9115\\\",\\\"status.workspaceTrust.1698129059679\\\",\\\"status.workspaceTrust.69b4e894bc3c88832986dd630ce60dd4\\\",\\\"status.workspaceTrust.1698129531094\\\",\\\"status.workspaceTrust.1698132394702\\\",\\\"status.workspaceTrust.1698144754383\\\",\\\"status.workspaceTrust.1698150131668\\\",\\\"status.workspaceTrust.1698150140587\\\",\\\"status.workspaceTrust.060d7961dcb91efb8c70b7ab3683f2aa\\\",\\\"status.workspaceTrust.1698199146729\\\",\\\"status.workspaceTrust.1698201736677\\\",\\\"status.workspaceTrust.1698201878729\\\",\\\"status.workspaceTrust.1698204027701\\\",\\\"status.workspaceTrust.1698223206295\\\",\\\"status.workspaceTrust.1698241294938\\\",\\\"status.workspaceTrust.1695048678478\\\",\\\"status.workspaceTrust.1695048700386\\\",\\\"status.workspaceTrust.b7f4a67635e15ff911616af6c9e98cd1\\\",\\\"status.workspaceTrust.1698281843368\\\",\\\"status.workspaceTrust.1698281876514\\\",\\\"status.workspaceTrust.1698282933279\\\",\\\"status.workspaceTrust.700265bc457c40b57ea0936ed35920bc\\\",\\\"status.workspaceTrust.1698285204515\\\",\\\"status.workspaceTrust.1698387340361\\\",\\\"status.workspaceTrust.880fc5a618a5cd31a5439ab8208cfc7d\\\",\\\"status.workspaceTrust.1698415542202\\\",\\\"status.workspaceTrust.1698417580602\\\",\\\"status.workspaceTrust.1698417591260\\\",\\\"status.workspaceTrust.0833756abcd7c2eb9c7292fd82eb060e\\\",\\\"status.workspaceTrust.1698451601486\\\",\\\"status.workspaceTrust.c8db8d143a25f52ba5f4d659da964003\\\",\\\"status.workspaceTrust.1698491949433\\\",\\\"status.workspaceTrust.1698498793963\\\",\\\"status.workspaceTrust.1698498808649\\\",\\\"status.workspaceTrust.0878b017032d9bb915cb7da83bf32667\\\",\\\"status.workspaceTrust.1698544538378\\\",\\\"status.workspaceTrust.bb20580ea1a1eea3470713ac89c46876\\\",\\\"status.workspaceTrust.1698573624388\\\",\\\"status.workspaceTrust.14e23b60e251645bb184a60e73ed3c92\\\",\\\"status.workspaceTrust.611f52ac018ba6881d5cc05fc783f0f9\\\"]\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.cmake-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.bookmarks\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.TongyiLingma\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.sftp\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.codegeex-sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.coding-copilot-webviews-login\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.local-history\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.trae\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.project-dashboard\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.codeium\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.codestream-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.localHistory\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.krinql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.vscode-edge-devtools-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.cspell-info-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.marscode\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.cspell-regexp-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.package-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.hexExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.makefile__viewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.gitlensInspect\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.gitlensPatch\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.todo\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.todo-tree-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.coder-maker-sidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.searchResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.foldersCompare\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.tabnine\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.mintdocs\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.marquee\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.sourcegraph-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.chatgpt-china-view-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.cloudmusic\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":false,\\\"visible\\\":true,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"pinned\\\":false,\\\"visible\\\":true,\\\"order\\\":6},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.azurePanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.view.extension.outline-map\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101},{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.673ab037-75a4-4438-99f8-077f850bc3ba\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark Xen-ccpp-theme-themes-ccpp_theme-json\\\",\\\"label\\\":\\\"C/C++ Theme\\\",\\\"settingsId\\\":\\\"C/C++ Theme\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"emphasis\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"strong\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"header\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.diff\\\",\\\"meta.diff.header\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"markup.inserted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"markup.deleted\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"markup.changed\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"underline italic\\\"},\\\"scope\\\":[\\\"invalid\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\",\\\"fontStyle\\\":\\\"underline italic\\\"},\\\"scope\\\":[\\\"invalid.deprecated\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"entity.name.filename\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"markup.error\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":[\\\"markup.underline\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"markup.bold\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"markup.heading\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"markup.italic\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"beginning.punctuation.definition.list.markdown\\\",\\\"beginning.punctuation.definition.quote.markdown\\\",\\\"punctuation.definition.link.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"markup.inline.raw\\\",\\\"markup.raw.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"markup.underline.link\\\",\\\"markup.underline.link.image\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.link.reference.def.restructuredtext\\\",\\\"punctuation.definition.directive.restructuredtext\\\",\\\"string.other.link.description\\\",\\\"string.other.link.title\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.name.directive.restructuredtext\\\",\\\"markup.quote\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.separator.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"fenced_code.block.language\\\",\\\"markup.raw.inner.restructuredtext\\\",\\\"markup.fenced_code.block.markdown punctuation.definition.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"punctuation.definition.constant.restructuredtext\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"markup.heading.markdown punctuation.definition.string.begin\\\",\\\"markup.heading.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.paragraph.markdown punctuation.definition.string.begin\\\",\\\"meta.paragraph.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.begin\\\",\\\"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"normal\\\"},\\\"scope\\\":[\\\"entity.name.type.class\\\",\\\"entity.name.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"keyword.expressions-and-types.swift\\\",\\\"keyword.other.this\\\",\\\"variable.language\\\",\\\"variable.language punctuation.definition.variable.php\\\",\\\"variable.other.readwrite.instance.ruby\\\",\\\"variable.parameter.function.language.special\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"comment\\\",\\\"punctuation.definition.comment\\\",\\\"unused.comment\\\",\\\"wildcard.comment\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"comment keyword.codetag.notation\\\",\\\"comment.block.documentation keyword\\\",\\\"comment.block.documentation storage.type.class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"comment.block.documentation entity.name.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"comment.block.documentation entity.name.type punctuation.definition.bracket\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"comment.block.documentation variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"constant\\\",\\\"variable.other.constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"constant.character.escape\\\",\\\"constant.character.string.escape\\\",\\\"constant.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"entity.name.tag\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.parent-selector\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"meta.function-call.object\\\",\\\"meta.function-call.php\\\",\\\"meta.function-call.static\\\",\\\"meta.method-call.java meta.method\\\",\\\"meta.method.groovy\\\",\\\"support.function.any-method.lua\\\",\\\"keyword.operator.function.infix\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"entity.name.variable.parameter\\\",\\\"meta.at-rule.function variable\\\",\\\"meta.at-rule.mixin variable\\\",\\\"meta.function.arguments variable.other.php\\\",\\\"meta.selectionset.graphql meta.arguments.graphql variable.arguments.graphql\\\",\\\"variable.parameter\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"meta.decorator variable.other.readwrite\\\",\\\"meta.decorator variable.other.property\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"meta.decorator variable.other.object\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"keyword\\\",\\\"punctuation.definition.keyword\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"keyword.control.new\\\",\\\"keyword.operator.new\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.selector\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"support\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"support.function.magic\\\",\\\"support.variable\\\",\\\"variable.other.predefined\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"support.function\\\",\\\"support.type.property-name\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"constant.other.symbol.hashkey punctuation.definition.constant.ruby\\\",\\\"entity.other.attribute-name.placeholder punctuation\\\",\\\"entity.other.attribute-name.pseudo-class punctuation\\\",\\\"entity.other.attribute-name.pseudo-element punctuation\\\",\\\"meta.group.double.toml\\\",\\\"meta.group.toml\\\",\\\"meta.object-binding-pattern-variable punctuation.destructuring\\\",\\\"punctuation.colon.graphql\\\",\\\"punctuation.definition.block.scalar.folded.yaml\\\",\\\"punctuation.definition.block.scalar.literal.yaml\\\",\\\"punctuation.definition.block.sequence.item.yaml\\\",\\\"punctuation.definition.entity.other.inherited-class\\\",\\\"punctuation.function.swift\\\",\\\"punctuation.separator.dictionary.key-value\\\",\\\"punctuation.separator.hash\\\",\\\"punctuation.separator.inheritance\\\",\\\"punctuation.separator.key-value\\\",\\\"punctuation.separator.key-value.mapping.yaml\\\",\\\"punctuation.separator.namespace\\\",\\\"punctuation.separator.pointer-access\\\",\\\"punctuation.separator.slice\\\",\\\"string.unquoted.heredoc punctuation.definition.string\\\",\\\"support.other.chomping-indicator.yaml\\\",\\\"punctuation.separator.annotation\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"keyword.operator.other.powershell\\\",\\\"keyword.other.statement-separator.powershell\\\",\\\"meta.brace.round\\\",\\\"meta.function-call punctuation\\\",\\\"punctuation.definition.arguments.begin\\\",\\\"punctuation.definition.arguments.end\\\",\\\"punctuation.definition.entity.begin\\\",\\\"punctuation.definition.entity.end\\\",\\\"punctuation.definition.tag.cs\\\",\\\"punctuation.definition.type.begin\\\",\\\"punctuation.definition.type.end\\\",\\\"punctuation.section.scope.begin\\\",\\\"punctuation.section.scope.end\\\",\\\"punctuation.terminator.expression.php\\\",\\\"storage.type.generic.java\\\",\\\"string.template meta.brace\\\",\\\"string.template punctuation.accessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"meta.string-contents.quoted.double punctuation.definition.variable\\\",\\\"punctuation.definition.interpolation.begin\\\",\\\"punctuation.definition.interpolation.end\\\",\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded.begin\\\",\\\"punctuation.section.embedded.coffee\\\",\\\"punctuation.section.embedded.end\\\",\\\"punctuation.section.embedded.end source.php\\\",\\\"punctuation.section.embedded.end source.ruby\\\",\\\"punctuation.definition.variable.makefile\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.function.target.makefile\\\",\\\"entity.name.section.toml\\\",\\\"entity.name.tag.yaml\\\",\\\"variable.other.key.toml\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"constant.other.date\\\",\\\"constant.other.timestamp\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic underline\\\",\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"variable.other.alias.yaml\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"storage\\\",\\\"meta.implementation storage.type.objc\\\",\\\"meta.interface-or-protocol storage.type.objc\\\",\\\"source.groovy storage.type.def\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.type\\\",\\\"keyword.primitive-datatypes.swift\\\",\\\"keyword.type.cs\\\",\\\"meta.protocol-list.objc\\\",\\\"meta.return-type.objc\\\",\\\"source.go storage.type\\\",\\\"source.groovy storage.type\\\",\\\"source.java storage.type\\\",\\\"source.powershell entity.other.attribute-name\\\",\\\"storage.class.std.rust\\\",\\\"storage.type.attribute.swift\\\",\\\"storage.type.c\\\",\\\"storage.type.core.rust\\\",\\\"storage.type.cs\\\",\\\"storage.type.groovy\\\",\\\"storage.type.objc\\\",\\\"storage.type.php\\\",\\\"storage.type.haskell\\\",\\\"storage.type.ocaml\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"entity.name.type.type-parameter\\\",\\\"meta.indexer.mappedtype.declaration entity.name.type\\\",\\\"meta.type.parameters entity.name.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"string.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.character.escape.backslash.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.capture.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"string.regexp punctuation.definition.string.begin\\\",\\\"string.regexp punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"punctuation.definition.character-class.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.assertion.regexp\\\",\\\"keyword.operator.negation.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\"},\\\"scope\\\":[\\\"meta.assertion.look-ahead.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"string\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#E9F284\\\"},\\\"scope\\\":[\\\"punctuation.definition.string.begin\\\",\\\"punctuation.definition.string.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FE\\\"},\\\"scope\\\":[\\\"punctuation.support.type.property-name.begin\\\",\\\"punctuation.support.type.property-name.end\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"string.quoted.docstring.multi\\\",\\\"string.quoted.docstring.multi.python punctuation.definition.string.begin\\\",\\\"string.quoted.docstring.multi.python punctuation.definition.string.end\\\",\\\"string.quoted.docstring.multi.python constant.character.escape\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"constant.other.key.perl\\\",\\\"support.variable.property\\\",\\\"variable.other.constant.js\\\",\\\"variable.other.constant.ts\\\",\\\"variable.other.constant.tsx\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#FFB86C\\\"},\\\"scope\\\":[\\\"meta.import variable.other.readwrite\\\",\\\"meta.variable.assignment.destructured.object.coffee variable\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"normal\\\",\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.import variable.other.readwrite.alias\\\",\\\"meta.export variable.other.readwrite.alias\\\",\\\"meta.variable.assignment.destructured.object.coffee variable variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.selectionset.graphql variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"meta.selectionset.graphql meta.arguments variable\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\"},\\\"scope\\\":[\\\"entity.name.fragment.graphql\\\",\\\"variable.fragment.graphql\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"constant.other.symbol.hashkey.ruby\\\",\\\"keyword.operator.dereference.java\\\",\\\"keyword.operator.navigation.groovy\\\",\\\"meta.scope.for-loop.shell punctuation.definition.string.begin\\\",\\\"meta.scope.for-loop.shell punctuation.definition.string.end\\\",\\\"meta.scope.for-loop.shell string\\\",\\\"storage.modifier.import\\\",\\\"punctuation.section.embedded.begin.tsx\\\",\\\"punctuation.section.embedded.end.tsx\\\",\\\"punctuation.section.embedded.begin.jsx\\\",\\\"punctuation.section.embedded.end.jsx\\\",\\\"punctuation.separator.list.comma.css\\\",\\\"constant.language.empty-list.haskell\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"source.shell variable.other\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"normal\\\",\\\"foreground\\\":\\\"#BD93F9\\\"},\\\"scope\\\":[\\\"support.constant\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.scope.prerequisites.makefile\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\"},\\\"scope\\\":[\\\"meta.attribute-selector.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\"},\\\"scope\\\":[\\\"punctuation.definition.attribute-selector.end.bracket.square.scss\\\",\\\"punctuation.definition.attribute-selector.begin.bracket.square.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6272A4\\\"},\\\"scope\\\":[\\\"meta.preprocessor.haskell\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"log.error\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"log.warning\\\"]},{},{},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"storage.type\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#8BE9FD\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"storage.type.built-in\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"keyword\\\",\\\"entity.name.operatorv\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF5555\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"keyword.operator.logical\\\",\\\"keyword.operator.comparison\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"punctuation.separator.pointer-access\\\",\\\"punctuation.separator.dot-access\\\",\\\"punctuation.section\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"constant.numeric\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EAF48C\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"string\\\",\\\"string.quoted.double\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#F5F5EF\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"entity.name.namespace\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#FF79C6\\\",\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"meta.preprocessor.macro\\\"]},{},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"regular\\\"},\\\"scope\\\":[\\\"meta.function-call.c\\\",\\\"meta.function.definition.c\\\",\\\"entity.name.function.c\\\",\\\"entity.name.function.definition.cpp\\\",\\\"entity.name.function.call.cpp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#5FEA77\\\",\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":[\\\"entity.name.function.member.cpp\\\"]}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"TODO: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"memberOperatorOverload\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"operatorOverload\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"type\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"class\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"enum\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"macro\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff79c6\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"enumMember\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#8be9fd\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"label\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"TODO.0.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"method.classScope.virtual\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope.declaration.virtual\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method.classScope.declaration\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.declaration.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"method\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.classScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.classScope.declaration.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.declaration.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function.defaultLibrary\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.1.1: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"method.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"function\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#5fea77\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"TODO.2: ccpp_comment1\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff0000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"property\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#bcaaa4\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"parameter\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"parameter.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.local\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ffb86c\\\",\\\"_bold\\\":false,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.functionScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.classScope.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.fileScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"property.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.classScope.readonly.static\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.fileScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":true,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.globalScope\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.global\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":false,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}},{\\\"_selector\\\":\\\"variable.globalScope.readonly\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ff5555\\\",\\\"_bold\\\":true,\\\"_underline\\\":true,\\\"_italic\\\":false,\\\"_strikethrough\\\":false}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"Xen.ccpp-theme\\\",\\\"_extensionIsBuiltin\\\":false,\\\"_extensionName\\\":\\\"ccpp-theme\\\",\\\"_extensionPublisher\\\":\\\"Xen\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"terminal.background\\\":\\\"#282a36\\\",\\\"terminal.foreground\\\":\\\"#f5f5ef\\\",\\\"terminal.ansiBrightBlack\\\":\\\"#6272a4\\\",\\\"terminal.ansiBrightRed\\\":\\\"#ff6e6e\\\",\\\"terminal.ansiBrightGreen\\\":\\\"#69ff94\\\",\\\"terminal.ansiBrightYellow\\\":\\\"#ffffa5\\\",\\\"terminal.ansiBrightBlue\\\":\\\"#d6acff\\\",\\\"terminal.ansiBrightMagenta\\\":\\\"#ff92df\\\",\\\"terminal.ansiBrightCyan\\\":\\\"#a4ffff\\\",\\\"terminal.ansiBrightWhite\\\":\\\"#ffffff\\\",\\\"terminal.ansiBlack\\\":\\\"#21222c\\\",\\\"terminal.ansiRed\\\":\\\"#ff5555\\\",\\\"terminal.ansiGreen\\\":\\\"#5fea77\\\",\\\"terminal.ansiYellow\\\":\\\"#eaf48c\\\",\\\"terminal.ansiBlue\\\":\\\"#bd93f9\\\",\\\"terminal.ansiMagenta\\\":\\\"#ff79c6\\\",\\\"terminal.ansiCyan\\\":\\\"#8be9fd\\\",\\\"terminal.ansiWhite\\\":\\\"#f5f5ef\\\",\\\"focusBorder\\\":\\\"#6272a4\\\",\\\"foreground\\\":\\\"#f5f5ef\\\",\\\"selection.background\\\":\\\"#bd93f9\\\",\\\"errorForeground\\\":\\\"#ff5555\\\",\\\"button.background\\\":\\\"#44475a\\\",\\\"button.foreground\\\":\\\"#f5f5ef\\\",\\\"button.secondaryBackground\\\":\\\"#282a36\\\",\\\"button.secondaryForeground\\\":\\\"#f5f5ef\\\",\\\"button.secondaryHoverBackground\\\":\\\"#343746\\\",\\\"dropdown.background\\\":\\\"#343746\\\",\\\"dropdown.border\\\":\\\"#191a21\\\",\\\"dropdown.foreground\\\":\\\"#f5f5ef\\\",\\\"input.background\\\":\\\"#282a36\\\",\\\"input.foreground\\\":\\\"#f5f5ef\\\",\\\"input.border\\\":\\\"#191a21\\\",\\\"input.placeholderForeground\\\":\\\"#6272a4\\\",\\\"inputOption.activeBorder\\\":\\\"#bd93f9\\\",\\\"inputValidation.infoBorder\\\":\\\"#ff79c6\\\",\\\"inputValidation.warningBorder\\\":\\\"#ffb86c\\\",\\\"inputValidation.errorBorder\\\":\\\"#ff5555\\\",\\\"badge.foreground\\\":\\\"#f5f5ef\\\",\\\"badge.background\\\":\\\"#44475a\\\",\\\"progressBar.background\\\":\\\"#ff79c6\\\",\\\"list.activeSelectionBackground\\\":\\\"#44475a\\\",\\\"list.activeSelectionForeground\\\":\\\"#f5f5ef\\\",\\\"list.dropBackground\\\":\\\"#44475a\\\",\\\"list.focusBackground\\\":\\\"#44475a75\\\",\\\"list.highlightForeground\\\":\\\"#8be9fd\\\",\\\"list.hoverBackground\\\":\\\"#44475a75\\\",\\\"list.inactiveSelectionBackground\\\":\\\"#5fea7750\\\",\\\"list.warningForeground\\\":\\\"#ffb86c\\\",\\\"list.errorForeground\\\":\\\"#ff5555\\\",\\\"activityBar.background\\\":\\\"#191a21\\\",\\\"activityBar.inactiveForeground\\\":\\\"#6272a4\\\",\\\"activityBar.foreground\\\":\\\"#f5f5ef\\\",\\\"activityBar.activeBorder\\\":\\\"#ff79c680\\\",\\\"activityBar.activeBackground\\\":\\\"#bd93f910\\\",\\\"activityBarBadge.background\\\":\\\"#ff79c6\\\",\\\"activityBarBadge.foreground\\\":\\\"#f5f5ef\\\",\\\"sideBar.background\\\":\\\"#21222c\\\",\\\"sideBarTitle.foreground\\\":\\\"#f5f5ef\\\",\\\"sideBarSectionHeader.background\\\":\\\"#282a36\\\",\\\"sideBarSectionHeader.border\\\":\\\"#191a21\\\",\\\"editorGroup.border\\\":\\\"#bd93f9\\\",\\\"editorGroup.dropBackground\\\":\\\"#44475a70\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#191a21\\\",\\\"tab.activeBackground\\\":\\\"#424450\\\",\\\"tab.activeForeground\\\":\\\"#f5f5ef\\\",\\\"tab.border\\\":\\\"#191a21\\\",\\\"tab.activeBorderTop\\\":\\\"#ff79c680\\\",\\\"tab.inactiveBackground\\\":\\\"#21222c\\\",\\\"tab.inactiveForeground\\\":\\\"#6272a4\\\",\\\"editor.foreground\\\":\\\"#f5f5ef\\\",\\\"editor.background\\\":\\\"#282a36\\\",\\\"editorLineNumber.foreground\\\":\\\"#6272a4\\\",\\\"editor.selectionBackground\\\":\\\"#5fea7740\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#5fea7780\\\",\\\"editor.foldBackground\\\":\\\"#21222c80\\\",\\\"editor.wordHighlightBackground\\\":\\\"#8be9fd50\\\",\\\"editor.wordHighlightStrongBackground\\\":\\\"#5fea7750\\\",\\\"editor.findMatchBackground\\\":\\\"#ffb86cd0\\\",\\\"editor.findMatchHighlightBackground\\\":\\\"#ffffff40\\\",\\\"editor.findRangeHighlightBackground\\\":\\\"#44475a75\\\",\\\"editor.hoverHighlightBackground\\\":\\\"#8be9fd50\\\",\\\"editor.lineHighlightBorder\\\":\\\"#5fea7740\\\",\\\"editorLink.activeForeground\\\":\\\"#8be9fd\\\",\\\"editor.rangeHighlightBackground\\\":\\\"#bd93f915\\\",\\\"editor.snippetTabstopHighlightBackground\\\":\\\"#282a36\\\",\\\"editor.snippetTabstopHighlightBorder\\\":\\\"#6272a4\\\",\\\"editor.snippetFinalTabstopHighlightBackground\\\":\\\"#282a36\\\",\\\"editor.snippetFinalTabstopHighlightBorder\\\":\\\"#5fea77\\\",\\\"editorWhitespace.foreground\\\":\\\"#ffffff1a\\\",\\\"editorIndentGuide.background\\\":\\\"#ffffff1a\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#ffffff45\\\",\\\"editorRuler.foreground\\\":\\\"#ffffff1a\\\",\\\"editorCodeLens.foreground\\\":\\\"#6272a4\\\",\\\"editorBracketHighlight.foreground1\\\":\\\"#f5f5ef\\\",\\\"editorBracketHighlight.foreground2\\\":\\\"#ff79c6\\\",\\\"editorBracketHighlight.foreground3\\\":\\\"#8be9fd\\\",\\\"editorBracketHighlight.foreground4\\\":\\\"#5fea77\\\",\\\"editorBracketHighlight.foreground5\\\":\\\"#bd93f9\\\",\\\"editorBracketHighlight.foreground6\\\":\\\"#ffb86c\\\",\\\"editorBracketHighlight.unexpectedBracket.foreground\\\":\\\"#ff5555\\\",\\\"editorOverviewRuler.border\\\":\\\"#191a21\\\",\\\"editorOverviewRuler.selectionHighlightForeground\\\":\\\"#ffb86c\\\",\\\"editorOverviewRuler.wordHighlightForeground\\\":\\\"#8be9fd\\\",\\\"editorOverviewRuler.wordHighlightStrongForeground\\\":\\\"#5fea77\\\",\\\"editorOverviewRuler.modifiedForeground\\\":\\\"#8be9fd80\\\",\\\"editorOverviewRuler.addedForeground\\\":\\\"#5fea7780\\\",\\\"editorOverviewRuler.deletedForeground\\\":\\\"#ff555580\\\",\\\"editorOverviewRuler.errorForeground\\\":\\\"#ff555580\\\",\\\"editorOverviewRuler.warningForeground\\\":\\\"#ffb86c80\\\",\\\"editorOverviewRuler.infoForeground\\\":\\\"#8be9fd80\\\",\\\"editorError.foreground\\\":\\\"#ff5555\\\",\\\"editorWarning.foreground\\\":\\\"#8be9fd\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#8be9fd80\\\",\\\"editorGutter.addedBackground\\\":\\\"#5fea7780\\\",\\\"editorGutter.deletedBackground\\\":\\\"#ff555580\\\",\\\"gitDecoration.modifiedResourceForeground\\\":\\\"#8be9fd\\\",\\\"gitDecoration.deletedResourceForeground\\\":\\\"#ff5555\\\",\\\"gitDecoration.untrackedResourceForeground\\\":\\\"#5fea77\\\",\\\"gitDecoration.ignoredResourceForeground\\\":\\\"#6272a4\\\",\\\"gitDecoration.conflictingResourceForeground\\\":\\\"#ffb86c\\\",\\\"diffEditor.insertedTextBackground\\\":\\\"#5fea7720\\\",\\\"diffEditor.removedTextBackground\\\":\\\"#ff555550\\\",\\\"editorWidget.background\\\":\\\"#21222c\\\",\\\"editorSuggestWidget.background\\\":\\\"#21222c\\\",\\\"editorSuggestWidget.foreground\\\":\\\"#f5f5ef\\\",\\\"editorSuggestWidget.selectedBackground\\\":\\\"#44475a\\\",\\\"editorHoverWidget.background\\\":\\\"#282a36\\\",\\\"editorHoverWidget.border\\\":\\\"#6272a4\\\",\\\"editorMarkerNavigation.background\\\":\\\"#21222c\\\",\\\"peekView.border\\\":\\\"#44475a\\\",\\\"peekViewEditor.background\\\":\\\"#282a36\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#f1fa8c80\\\",\\\"peekViewResult.background\\\":\\\"#21222c\\\",\\\"peekViewResult.fileForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewResult.lineForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#f1fa8c80\\\",\\\"peekViewResult.selectionBackground\\\":\\\"#44475a\\\",\\\"peekViewResult.selectionForeground\\\":\\\"#f5f5ef\\\",\\\"peekViewTitle.background\\\":\\\"#191a21\\\",\\\"peekViewTitleDescription.foreground\\\":\\\"#6272a4\\\",\\\"peekViewTitleLabel.foreground\\\":\\\"#f5f5ef\\\",\\\"merge.currentHeaderBackground\\\":\\\"#5fea7790\\\",\\\"merge.incomingHeaderBackground\\\":\\\"#bd93f990\\\",\\\"editorOverviewRuler.currentContentForeground\\\":\\\"#5fea77\\\",\\\"editorOverviewRuler.incomingContentForeground\\\":\\\"#bd93f9\\\",\\\"panel.background\\\":\\\"#282a36\\\",\\\"panel.border\\\":\\\"#bd93f9\\\",\\\"panelTitle.activeBorder\\\":\\\"#ff79c6\\\",\\\"panelTitle.activeForeground\\\":\\\"#f5f5ef\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#6272a4\\\",\\\"statusBar.background\\\":\\\"#191a21\\\",\\\"statusBar.foreground\\\":\\\"#f5f5ef\\\",\\\"statusBar.debuggingBackground\\\":\\\"#ff5555\\\",\\\"statusBar.debuggingForeground\\\":\\\"#191a21\\\",\\\"statusBar.noFolderBackground\\\":\\\"#191a21\\\",\\\"statusBar.noFolderForeground\\\":\\\"#f5f5ef\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#ff5555\\\",\\\"statusBarItem.prominentHoverBackground\\\":\\\"#ffb86c\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#282a36\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#bd93f9\\\",\\\"titleBar.activeBackground\\\":\\\"#191a21\\\",\\\"titleBar.activeForeground\\\":\\\"#69ff94\\\",\\\"titleBar.inactiveBackground\\\":\\\"#44475c\\\",\\\"titleBar.inactiveForeground\\\":\\\"#69ff94\\\",\\\"extensionButton.prominentForeground\\\":\\\"#f5f5ef\\\",\\\"extensionButton.prominentBackground\\\":\\\"#5fea7790\\\",\\\"extensionButton.prominentHoverBackground\\\":\\\"#5fea7760\\\",\\\"pickerGroup.border\\\":\\\"#bd93f9\\\",\\\"pickerGroup.foreground\\\":\\\"#8be9fd\\\",\\\"debugToolBar.background\\\":\\\"#21222c\\\",\\\"walkThrough.embeddedEditorBackground\\\":\\\"#21222c\\\",\\\"settings.headerForeground\\\":\\\"#f5f5ef\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#ffb86c\\\",\\\"settings.dropdownBackground\\\":\\\"#21222c\\\",\\\"settings.dropdownForeground\\\":\\\"#f5f5ef\\\",\\\"settings.dropdownBorder\\\":\\\"#191a21\\\",\\\"settings.checkboxBackground\\\":\\\"#21222c\\\",\\\"settings.checkboxForeground\\\":\\\"#f5f5ef\\\",\\\"settings.checkboxBorder\\\":\\\"#191a21\\\",\\\"settings.textInputBackground\\\":\\\"#21222c\\\",\\\"settings.textInputForeground\\\":\\\"#f5f5ef\\\",\\\"settings.textInputBorder\\\":\\\"#191a21\\\",\\\"settings.numberInputBackground\\\":\\\"#21222c\\\",\\\"settings.numberInputForeground\\\":\\\"#f5f5ef\\\",\\\"settings.numberInputBorder\\\":\\\"#191a21\\\",\\\"breadcrumb.foreground\\\":\\\"#6272a4\\\",\\\"breadcrumb.background\\\":\\\"#282a36\\\",\\\"breadcrumb.focusForeground\\\":\\\"#f5f5ef\\\",\\\"breadcrumb.activeSelectionForeground\\\":\\\"#f5f5ef\\\",\\\"breadcrumbPicker.background\\\":\\\"#191a21\\\",\\\"listFilterWidget.background\\\":\\\"#343746\\\",\\\"listFilterWidget.outline\\\":\\\"#424450\\\",\\\"listFilterWidget.noMatchesOutline\\\":\\\"#ff5555\\\"},\\\"watch\\\":false}\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.loadedModules\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.excludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"nps/lastSessionDate\":\"Thu Sep 14 2023\",\"nps/sessionCount\":\"2\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"cpp.1.lastSessionDate\":\"Sat Oct 21 2023\",\"cpp.1.sessionCount\":\"3\",\"java.2.lastSessionDate\":\"Sat Oct 21 2023\",\"java.2.sessionCount\":\"3\",\"javascript.1.lastSessionDate\":\"Sat Oct 21 2023\",\"javascript.1.sessionCount\":\"3\",\"typescript.1.lastSessionDate\":\"Sat Oct 21 2023\",\"typescript.1.sessionCount\":\"3\",\"csharp.1.lastSessionDate\":\"Sat Oct 21 2023\",\"csharp.1.sessionCount\":\"3\",\"workbench.telemetryOptOutShown\":\"true\",\"memento/gettingStartedService\":\"{\\\"settingsSync\\\":{\\\"done\\\":true},\\\"settingsSyncWeb\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"pickAFolderTask-Other\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createPythonFolder\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonDataScienceWelcome#python.createNewNotebook\\\":{\\\"done\\\":true},\\\"installGit\\\":{\\\"done\\\":true},\\\"terminal\\\":{\\\"done\\\":true},\\\"alefragnani.project-manager#projectManagerWelcome#saveYourFavoriteProjects\\\":{\\\"done\\\":true},\\\"settings\\\":{\\\"done\\\":true},\\\"alefragnani.project-manager#projectManagerWelcome#autoDetectGitRepositories\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#create.project\\\":{\\\"done\\\":true},\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true}}\",\"encryption.migratedToGnomeLibsecret\":\"true\",\"workbench.panel.alignment\":\"center\",\"no-updates-running-as-admin\":\"true\",\"themeUpdatedNotificationShown\":\"true\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false}]\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"ces/skipSurvey\":\"1.82.1\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"cpp\\\":[\\\"ms-vscode.cpptools-extension-pack\\\"],\\\"plaintext\\\":[\\\"ms-vscode.cmake-tools\\\"]}\",\"http.linkProtectionTrustedDomains\":\"[\\\"https://codegeex.cn\\\",\\\"https://app.tabnine.com\\\",\\\"https://**************:28010\\\",\\\"*.llvm.org\\\",\\\"*\\\"]\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"ms-vscode.remote-server\\\",\\\"name\\\":\\\"Remote - Tunnels\\\",\\\"allowed\\\":true}]\",\"terminal.integrated.showTerminalConfigPrompt\":\"false\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\":2,\\\"workbench.view.extension.outline-map\\\":2,\\\"workbench.views.service.auxiliarybar.673ab037-75a4-4438-99f8-077f850bc3ba\\\":2},\\\"viewLocations\\\":{\\\"txtsyntaxHighlightExplore\\\":\\\"workbench.panel.chat\\\",\\\"filters\\\":\\\"workbench.panel.chat\\\",\\\"workbench.panel.chat.view.copilot\\\":\\\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070\\\",\\\"outline\\\":\\\"workbench.views.service.auxiliarybar.673ab037-75a4-4438-99f8-077f850bc3ba\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"workbench.panel.chatSidebar.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.TongyiLingma.state.hidden\":\"[{\\\"id\\\":\\\"TongyiLingMa.Chat\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.bookmarks.state.hidden\":\"[{\\\"id\\\":\\\"bookmarksExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"bookmarksHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.chatgpt-china-view-container.state.hidden\":\"[{\\\"id\\\":\\\"chatgpt-china\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cloudmusic.state.hidden\":\"[{\\\"id\\\":\\\"cloudmusic-account\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-local\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-playlist\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-radio\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudmusic-queue\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cmake-view.state.hidden\":\"[{\\\"id\\\":\\\"cmake.projectStatus\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cmake.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codegeex-sidebar.state.hidden\":\"[{\\\"id\\\":\\\"codegeex-qa\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codeium.state.hidden\":\"[{\\\"id\\\":\\\"codeium.chatPanelView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"codeium.searchPanelView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.coder-maker-sidebar.state.hidden\":\"[{\\\"id\\\":\\\"coder-maker-tool\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"coder-maker-tool-bk\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codestream-activitybar.state.hidden\":\"[{\\\"id\\\":\\\"activitybar.codestream\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.foldersCompare.state.hidden\":\"[{\\\"id\\\":\\\"foldersCompareAppService\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceOnlyA\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceOnlyB\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"foldersCompareAppServiceIdenticals\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.account\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.json-views.state.hidden\":\"[{\\\"id\\\":\\\"jsonOutline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.krinql.state.hidden\":\"[{\\\"id\\\":\\\"loginView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sideBarView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.local-history.state.hidden\":\"[{\\\"id\\\":\\\"localHistoryFileBrowser\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"localHistoryDiffBrowser\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile-command-runner.state.hidden\":\"[{\\\"id\\\":\\\"makefile\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.marquee.state.hidden\":\"[{\\\"id\\\":\\\"marquee\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.mintdocs.state.hidden\":\"[{\\\"id\\\":\\\"docs\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"formatOptions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"progress\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"languageOptions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"team\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"hotkeyOptions\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.outline-map.state.hidden\":\"[{\\\"id\\\":\\\"outline-map-view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.package-explorer.state.hidden\":\"[{\\\"id\\\":\\\"pythonEnvironments\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workspaceEnvironments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.project-manager.state.hidden\":\"[{\\\"id\\\":\\\"projectsExplorerFavorites\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerGit\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerSVN\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerAny\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerMercurial\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerVSCode\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectManagerHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sourcegraph-view.state.hidden\":\"[{\\\"id\\\":\\\"sourcegraph.searchSidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sourcegraph.files\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sourcegraph.helpSidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.tabnine.state.hidden\":\"[{\\\"id\\\":\\\"tabnine.chat\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.chat.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.authenticate\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.loading\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.thunder-client.state.hidden\":\"[{\\\"id\\\":\\\"thunder-client-sidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.todo-tree-container.state.hidden\":\"[{\\\"id\\\":\\\"todo-tree-view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.todo.state.hidden\":\"[{\\\"id\\\":\\\"todo.views.1files\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"todo.views.2embedded\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.vscode-edge-devtools-view.state.hidden\":\"[{\\\"id\\\":\\\"vscode-edge-devtools-view.targets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-edge-devtools-view.help-links\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.zhihu-explorer.state.hidden\":\"[{\\\"id\\\":\\\"zhihu-feed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"zhihu-hotStories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"zhihu-collection\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteHub.views.workspaceRepositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.ab051650-a8ed-47eb-9f38-fe25e83a4070.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false,\\\"order\\\":0}]\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"alefragnani.project-manager#projectManagerWelcome\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"saveYourFavoriteProjects\\\",\\\"autoDetectGitRepositories\\\",\\\"findAndOpenProjects\\\",\\\"organizeWithTags\\\",\\\"exclusiveSideBar\\\",\\\"workingWithRemotes\\\"],\\\"manaullyOpened\\\":false}],[\\\"eamodio.gitlens#welcome\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"get-started\\\",\\\"core-features\\\",\\\"pro-features\\\",\\\"pro-trial\\\",\\\"pro-upgrade\\\",\\\"pro-reactivate\\\",\\\"pro-paid\\\",\\\"visualize\\\",\\\"launchpad\\\",\\\"code-collab\\\",\\\"integrations\\\",\\\"more\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"explore.commands\\\",\\\"open.wslwindow\\\",\\\"create.project\\\",\\\"open.project\\\",\\\"linux.environment\\\",\\\"install.tools\\\",\\\"run.debug\\\",\\\"come.back\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.cpptools#cppWelcome\\\",{\\\"firstSeen\\\":1726583832285,\\\"stepIDs\\\":[\\\"awaiting.activation.mac\\\",\\\"awaiting.activation.linux\\\",\\\"awaiting.activation.windows\\\",\\\"awaiting.activation.windows10\\\",\\\"awaiting.activation.windows11\\\",\\\"no.compilers.found.mac\\\",\\\"no.compilers.found.linux\\\",\\\"no.compilers.found.windows\\\",\\\"no.compilers.found.windows10\\\",\\\"no.compilers.found.windows11\\\",\\\"verify.compiler.mac\\\",\\\"verify.compiler.linux\\\",\\\"verify.compiler.windows\\\",\\\"verify.compiler.windows10\\\",\\\"verify.compiler.windows11\\\",\\\"create.cpp.file\\\",\\\"relaunch.developer.command.prompt.windows\\\",\\\"run.project.mac\\\",\\\"run.project.linux\\\",\\\"run.project.windows\\\",\\\"customize.debugging.linux\\\",\\\"customize.debugging.windows\\\",\\\"customize.debugging.mac\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough\\\",{\\\"firstSeen\\\":1729419779166,\\\"stepIDs\\\":[\\\"editCommitRepo\\\",\\\"createGitHubPullRequest\\\",\\\"continueOn\\\",\\\"openRepo\\\",\\\"remoteIndicator\\\"],\\\"manaullyOpened\\\":false}]]\",\"codegeexLogin-NevinXuHui\":\"[{\\\"id\\\":\\\"aminer.codegeex\\\",\\\"name\\\":\\\"CodeGeeX: AI Code AutoComplete, Chat, Auto Comment\\\",\\\"allowed\\\":true}]\",\"codegeexLogin-徐辉\":\"[{\\\"id\\\":\\\"aminer.codegeex\\\",\\\"name\\\":\\\"CodeGeeX: AI Code AutoComplete, Chat, Auto Comment\\\",\\\"allowed\\\":true}]\",\"codeium_auth-hui xu\":\"[{\\\"id\\\":\\\"codeium.codeium\\\",\\\"name\\\":\\\"Codeium: AI Coding Autocomplete and Chat for Python, Javascript, Typescript, Java, Go, and more\\\",\\\"allowed\\\":true}]\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"tabnine.signInUsingAuthToken\\\",\\\"value\\\":17},{\\\"key\\\":\\\"go.tools.install\\\",\\\"value\\\":53},{\\\"key\\\":\\\"clangd.restart\\\",\\\"value\\\":69},{\\\"key\\\":\\\"clangd.install\\\",\\\"value\\\":70},{\\\"key\\\":\\\"gitignore.addgitignore\\\",\\\"value\\\":72}]}\",\"commandPalette.mru.counter\":\"73\",\"extensionTips/promptedExecutableTips\":\"{\\\"mvn\\\":[\\\"vscjava.vscode-java-pack\\\"],\\\"docker\\\":[\\\"ms-vscode-remote.remote-containers\\\"]}\",\"https://sourcegraph.com-SOURCEGRAPH_AUTH\":\"[{\\\"id\\\":\\\"sourcegraph.sourcegraph\\\",\\\"name\\\":\\\"Sourcegraph\\\",\\\"allowed\\\":true}]\",\"memento/workbench.editor.keybindings\":\"{\\\"searchHistory\\\":[\\\"fu z\\\",\\\"f\\\",\\\"qia\\\",\\\"前进\\\",\\\"hout\\\",\\\"h\\\",\\\"hou\\\",\\\"后退\\\",\\\"fa\\\",\\\"返回\\\",\\\"fu'zu\\\",\\\"辅助\\\",\\\"code\\\",\\\"codeG\\\",\\\"codeGee\\\",\\\"sho\\\",\\\"搜索\\\",\\\"chash\\\",\\\"chazh\\\",\\\"\\\\\\\"Control+Shift+F\\\\\\\"\\\",\\\"c\\\",\\\"cha\\\",\\\"查找\\\"]}\",\"mergeEditorCloseWithConflicts\":\"true\",\"remote.explorerType\":\"tunnel,ssh-remote\",\"snippets.usageTimestamps\":\"[[\\\"snippets/shebang-shellscript.json/shebang-bash\\\",1676194626665],[\\\"snippets/snippets.json/misc.am-I-not-root\\\",1676272863173],[\\\"snippets/snippets.json/math.precision\\\",1688815622130],[\\\"snippets/builtins.json/LINES\\\",1690941815194]]\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"tabnine.tabnine-vscode\\\",\\\"name\\\":\\\"Tabnine AI\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.PowerShell.state.hidden\":\"[{\\\"id\\\":\\\"PowerShellCommands\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.fnMapExplorer.state.hidden\":\"[{\\\"id\\\":\\\"fnMapView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.localHistory.state.hidden\":\"[{\\\"id\\\":\\\"treeLocalHistory\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.project-dashboard.state.hidden\":\"[{\\\"id\\\":\\\"projectDashboard.dashboard\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false}]\",\"workbench.view.sync.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.sync.conflicts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.remoteActivity\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.machines\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.localActivity\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.sync.troubleshoot\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.sync.externalActivity\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.a3e2775a-da79-4fe8-b19d-0640cb4eeae2.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"nps/isCandidate\":\"false\",\"nps/skipVersion\":\"1.81.0\",\"tabs-list-width-horizontal\":\"162\",\"workbench.welcomePage.hiddenCategories\":\"[\\\"github.copilot\\\"]\",\"extension.features.state\":\"{\\\"github.copilot-chat\\\":{\\\"copilot\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[1743988447712,1743988449111,1743988457300,1743988458814,1743988473694,1743988911153,1743989043621,1743989190407,1743989271987,1743989377040,1744276734989,1744514349788,1744514350978,1744514365942,1744514365990,1744514528497,1744514538361,1744515111681,1744515285523,1744515920824,1745023782410,1745504129098,1745504138139,1745504240423,1745504241590,1745506713769,1745506721732,1745507016771,1745507043204,1745551962739,1745884333028,1746255737703,1746255738419,1746255746816,1746255746832,1746255992122,1746255992942,1746256155029,1746257078352,1746257459059,1746257626807,1746257893367,1746257974974,1746258106873,1746258371384,1746258547818,1746258831469,1746259175684,1746259636440,1746261220469,1746269686526,1746275314626,1746276007292,1746278187807,1746278188481,1746278191268,1746278192595,1746278956875]}}}\",\"menu.hiddenCommands\":\"{\\\"EditorTitle\\\":[\\\"githd.viewHistory\\\",\\\"git.openFile\\\"]}\",\"workbench.view.extension.vscode-serial-monitor-tools.state.hidden\":\"[{\\\"id\\\":\\\"vscode-serial-monitor.monitor0\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor1\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor2\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.liveshare.state.hidden\":\"[{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false}]\",\"github-NevinXuHui\":\"[{\\\"id\\\":\\\"github.vscode-pull-request-github\\\",\\\"name\\\":\\\"GitHub 拉取请求和问题\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"vscode.github\\\",\\\"name\\\":\\\"GitHub\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.codespaces\\\",\\\"name\\\":\\\"GitHub Codespaces\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.copilot\\\",\\\"name\\\":\\\"GitHub Copilot\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"codestream.codestream\\\",\\\"name\\\":\\\"New Relic CodeStream\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.references-view.state.hidden\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppReferencesView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sftp.state.hidden\":\"[{\\\"id\\\":\\\"remoteExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.673ab037-75a4-4438-99f8-077f850bc3ba.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.ca710766-28ab-453c-97ff-8c5fad96bd64.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"outline-map-view\\\",\\\"isHidden\\\":false}]\",\"timeline.excludeSources\":\"[\\\"git-history\\\"]\",\"workbench.views.service.auxiliarybar.ecad586d-ab5a-462d-8a6c-db3514f8e592.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.85e9c2eb-a0de-4e42-86a3-1847ac58ceeb.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.linuxdev-options.state.hidden\":\"[{\\\"id\\\":\\\"linuxDevCmdView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.platformio.state.hidden\":\"[{\\\"id\\\":\\\"platformio-ide.projectTasks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"platformio-ide.quickAccess\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.vscode-chatgpt-view-container.state.hidden\":\"[{\\\"id\\\":\\\"vscode-chatgpt.view\\\",\\\"isHidden\\\":false}]\",\"extensionsAssistant/deprecated\":\"[\\\"austin.code-gnu-global\\\",\\\"gencay.vscode-chatgpt\\\",\\\"ms-vsliveshare.vsliveshare-audio\\\",\\\"rebornix.ruby\\\",\\\"wingrunr21.vscode-ruby\\\"]\",\"extensionsAssistant/fileExtensionsSuggestionIgnore\":\"[\\\"in\\\",\\\"4\\\",\\\"default\\\",\\\"specs\\\",\\\"old\\\",\\\"pub\\\",\\\"out\\\",\\\"desktop\\\",\\\"supp\\\",\\\"pc\\\",\\\"tmp\\\",\\\"crt\\\"]\",\"remote.tunnels.toRestore.ssh-remote+*************.1429597751\":\"[]\",\"remoteTunnelServicePromptedPreview\":\"true\",\"remoteTunnelServiceUsed\":\"{\\\"hostName\\\":\\\"nevinxu-gtr\\\",\\\"timeStamp\\\":1686020933564}\",\"settingsEditor2.splitViewWidth\":\"263\",\"tabs-list-width-vertical\":\"320\",\"userDataProfiles.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.profiles.export.preview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.profiles.import.preview\\\",\\\"isHidden\\\":false}]\",\"views.cachedViewContainerLocations\":\"[[\\\"workbench.views.service.auxiliarybar.b45416fb-2091-430c-a0c9-d9c01c75ed6c\\\",2]]\",\"views.cachedViewPositions\":\"[[\\\"outline\\\",{\\\"containerId\\\":\\\"workbench.views.service.auxiliarybar.b45416fb-2091-430c-a0c9-d9c01c75ed6c\\\"}]]\",\"workbench.view.extension.chatgpt-vscode-view-container.state.hidden\":\"[{\\\"id\\\":\\\"chatgpt-vscode.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cmake__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"cmake.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.jupyter-variables.state.hidden\":\"[{\\\"id\\\":\\\"jupyterViewVariables\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.jupyter.state.hidden\":\"[{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefilem-activitybar.state.hidden\":\"[{\\\"id\\\":\\\"makefilem.tools\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.tabnine-access.state.hidden\":\"[{\\\"id\\\":\\\"tabnine-today\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine-home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine-notifications\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.chat\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"tabnine.empty\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.583394b5-7948-4f56-9f2d-01588be69654.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.8c22f005-ce6d-42f2-8921-a481a24be319.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"outline-map-view\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.b45416fb-2091-430c-a0c9-d9c01c75ed6c.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.e07724a0-3ba9-4130-bea4-f24a91132432.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.fa7544f7-ac1e-4503-a679-0d9972ab1c54.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.githd-explorer.state.hidden\":\"[{\\\"id\\\":\\\"committedFiles\\\",\\\"isHidden\\\":false}]\",\"editor.contrib.largeFileOptimizationsWarner\":\"true\",\"workbench.views.service.panel.f9745eed-1d0f-4df1-b75f-ce915cdee999.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"aminer.codegeex\\\",\\\"name\\\":\\\"CodeGeeX: AI Code AutoComplete, Chat, Auto Comment\\\",\\\"allowed\\\":true}]\",\"remote.tunnels.toRestore.ssh-remote+**************.1310277496\":\"[{\\\"remoteHost\\\":\\\"localhost\\\",\\\"remotePort\\\":6677,\\\"localPort\\\":6677,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"localhost:6677\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"localhost:6677\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localPort\\\":1124,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:1124\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1124\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":41483,\\\"localPort\\\":41483,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:41483\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:41483\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":35577,\\\"localPort\\\":35577,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:35577\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:35577\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":42741,\\\"localPort\\\":42741,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:42741\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:42741\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"remote.tunnels.toRestore.ssh-remote+nevinxu.top.1310277496\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu.-58814727\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localAddress\\\":\\\"127.0.0.1:1123\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1123\\\"},\\\"localPort\\\":1123,\\\"closeable\\\":true,\\\"hasRunningProcess\\\":false,\\\"privacy\\\":\\\"private\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"}}]\",\"remote.tunnels.toRestore.wsl+arch.1310277496\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localPort\\\":1123,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:1123\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1123\\\"},\\\"runningProcess\\\":\\\"/root/.vscode-server/data/User/globalStorage/tabnine.tabnine-vscode/binaries/4.5.28/x86_64-unknown-linux-musl/TabNine\\\\u0000--no-lsp=true\\\\u0000--client=vscode\\\\u0000--client-metadata\\\\u0000clientVersion=1.81.1\\\\u0000pluginVersion=3.8.8\\\\u0000t9-vscode-AutoImportEnabled=true\\\\u0000t9-vscode-TSAutoImportEnabled=true\\\\u0000t9-vscode-JSAutoImportEnabled=true\\\\u0000vscode-telemetry-enabled=true\\\\u0000vscode-remote=true\\\\u0000vscode-remote-name=wsl\\\\u0000vscode-extension-kind=2\\\\u0000vscode-theme-name=C/C++ Theme\\\\u0000vscode-theme-kind=Dark\\\\u0000vscode-machine-id=d5748f1f1f3232b1d15ee89de17dc761e519ae77d9819174ae82a5f904d07ea7\\\\u0000vscode-is-new-app-install=false\\\\u0000vscode-session-id=0c50e50b-a094-4394-a5db-029ab185fe761693784396397\\\\u0000vscode-language=zh-cn\\\\u0000vscode-app-name=Visual Studio Code\\\\u0000vscode-beta-channel-enabled=false\\\\u0000vscode-status-customization=#005f5f\\\\u0000vscode-inline-api-enabled=true\\\\u0000ide-restart-counter=0\\\\u0000--no_bootstrap\\\\u0000\\\",\\\"hasRunningProcess\\\":true,\\\"pid\\\":1584,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"remote.tunnels.toRestore.wsl+ubuntu.1310277496\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":40133,\\\"localPort\\\":40133,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:40133\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:40133\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localPort\\\":1123,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:1123\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1123\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"workbench.panel.testResults.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.testResults.view\\\",\\\"isHidden\\\":false}]\",\"extensionsAssistant/ignored_recommendations\":\"[\\\"ms-vscode.cmake-tools\\\"]\",\"remote.tunnelsView.autoForwardNeverShow\":\"true\",\"remote.tunnels.toRestore.ssh-remote+**************.1310277496\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localAddress\\\":\\\"127.0.0.1:1124\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1124\\\"},\\\"localPort\\\":1124,\\\"closeable\\\":true,\\\"hasRunningProcess\\\":false,\\\"privacy\\\":\\\"private\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"}}]\",\"remote.tunnels.toRestore.wsl+Arch.-117107280\":\"[]\",\"remote.tunnels.toRestore.wsl+arch.-117107280\":\"[]\",\"remote.tunnels.toRestore.wsl+Arch.466081401\":\"[]\",\"remote.tunnels.toRestore.wsl+Arch.-1736068890\":\"[]\",\"remote.tunnels.toRestore.wsl+Arch.1940937733\":\"[]\",\"remote.tunnels.toRestore.wsl+Arch.1310277496\":\"[]\",\"remote.tunnels.toRestore.wsl+arch.-1736068890\":\"[]\",\"remote.tunnels.toRestore.wsl+arch.-58814727\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu-20.04.-58814727\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu-20.04.1940937733\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu-20.04.1310277496\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu-22.04.504750915\":\"[]\",\"remote.tunnels.toRestore.wsl+ubuntu-22.04.504750915\":\"[]\",\"remote.tunnels.toRestore.wsl+ubuntu-22.04.-965257596\":\"[{\\\"remoteHost\\\":\\\"localhost\\\",\\\"remotePort\\\":8384,\\\"localPort\\\":8384,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"localhost:8384\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"localhost:8384\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"用户转发\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"remote.tunnels.toRestore.wsl+Ubuntu-22.04.-965257596\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":1123,\\\"localPort\\\":1123,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:1123\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:1123\\\"},\\\"runningProcess\\\":\\\"/root/.vscode-server/data/User/globalStorage/tabnine.tabnine-vscode/binaries/4.13.0/x86_64-unknown-linux-musl/TabNine\\\\u0000--no-lsp=true\\\\u0000--client=vscode\\\\u0000--client-metadata\\\\u0000clientVersion=1.82.2\\\\u0000pluginVersion=3.17.0\\\\u0000t9-vscode-AutoImportEnabled=true\\\\u0000t9-vscode-TSAutoImportEnabled=true\\\\u0000t9-vscode-JSAutoImportEnabled=true\\\\u0000vscode-telemetry-enabled=true\\\\u0000vscode-remote=true\\\\u0000vscode-remote-name=wsl\\\\u0000vscode-extension-kind=2\\\\u0000vscode-theme-name=C/C++ Theme\\\\u0000vscode-theme-kind=Dark\\\\u0000vscode-machine-id=30efa88f813ea8dc6e63b66078fb5002638221bc3b1f2cecfe699ae174984798\\\\u0000vscode-is-new-app-install=true\\\\u0000vscode-session-id=e7fdb369-9e35-4ac4-a773-89c4759db1891695879322207\\\\u0000vscode-language=zh-cn\\\\u0000vscode-app-name=Visual Studio Code\\\\u0000vscode-beta-channel-enabled=false\\\\u0000vscode-status-customization=#005f5f\\\\u0000vscode-inline-api-enabled=true\\\\u0000ide-restart-counter=0\\\\u0000--no_bootstrap\\\\u0000\\\",\\\"hasRunningProcess\\\":true,\\\"pid\\\":3326,\\\"source\\\":{\\\"source\\\":1,\\\"description\\\":\\\"Auto Forwarded\\\"},\\\"privacy\\\":\\\"private\\\"},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":49152,\\\"localPort\\\":49152,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:49152\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:49152\\\"},\\\"runningProcess\\\":\\\"/root/.vscode-server/data/User/globalStorage/tabnine.tabnine-vscode/binaries/4.13.0/x86_64-unknown-linux-musl/TabNine-deep-local\\\\u0000--settings\\\\u0000/root/.config/TabNine/local.settingsce94127bSmall\\\\u0000--settings-version\\\\u00001\\\\u0000--client\\\\u0000vscode\\\\u0000--client-metadata\\\\u0000clientVersion=1.82.2\\\\u0000pluginVersion=3.17.0\\\\u0000t9-vscode-AutoImportEnabled=true\\\\u0000t9-vscode-TSAutoImportEnabled=true\\\\u0000t9-vscode-JSAutoImportEnabled=true\\\\u0000vscode-telemetry-enabled=true\\\\u0000vscode-remote=true\\\\u0000vscode-remote-name=wsl\\\\u0000vscode-extension-kind=2\\\\u0000vscode-theme-name=C/C++ Theme\\\\u0000vscode-theme-kind=Dark\\\\u0000vscode-machine-id=30efa88f813ea8dc6e63b66078fb5002638221bc3b1f2cecfe699ae174984798\\\\u0000vscode-is-new-app-install=true\\\\u0000vscode-session-id=e7fdb369-9e35-4ac4-a773-89c4759db1891695879322207\\\\u0000vscode-language=zh-cn\\\\u0000vscode-app-name=Visual Studio Code\\\\u0000vscode-beta-channel-enabled=false\\\\u0000vscode-status-customization=#005f5f\\\\u0000vscode-inline-api-enabled=true\\\\u0000ide-restart-counter=0\\\\u0000\\\",\\\"hasRunningProcess\\\":true,\\\"pid\\\":3463,\\\"source\\\":{\\\"source\\\":1,\\\"description\\\":\\\"Auto Forwarded\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"remote.tunnels.toRestore.wsl+Ubuntu-22.04.1379520614\":\"[]\",\"remote.tunnels.toRestore.wsl+Ubuntu-22.04.596532851\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":33789,\\\"localPort\\\":33789,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:33789\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:33789\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":1,\\\"description\\\":\\\"Auto Forwarded\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"remote.tunnels.toRestore.wsl+ubuntu-22.04.1379520614\":\"[]\",\"extensions.trustedPublishers\":\"{\\\"gengjian1203\\\":{\\\"publisher\\\":\\\"gengjian1203\\\",\\\"publisherDisplayName\\\":\\\"gengjian1203\\\"}}\"}}"}