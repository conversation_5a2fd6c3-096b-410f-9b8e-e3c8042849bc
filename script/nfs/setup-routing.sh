#!/bin/bash

# 网络路由配置脚本
# 用途: 配置特定IP/域名走指定网卡

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助
show_help() {
    echo "网络路由配置脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -a, --add-route         添加路由规则"
    echo "  -d, --delete-route      删除路由规则"
    echo "  -l, --list-routes       列出当前路由规则"
    echo "  -r, --reset-routes      重置所有自定义路由"
    echo
    echo "交互式配置:"
    echo "  $0                      启动交互式配置"
    echo
    echo "示例:"
    echo "  $0 -a                   添加新的路由规则"
    echo "  $0 -l                   查看当前路由"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查网络工具
check_network_tools() {
    local missing_tools=()
    
    for tool in ip route iptables; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要的网络工具: ${missing_tools[*]}"
        log_info "安装: sudo apt install iproute2 iptables"
        exit 1
    fi
}

# 显示网络接口
show_interfaces() {
    log_info "可用网络接口:"
    echo "----------------------------------------"
    ip link show | grep -E "^[0-9]+:" | while read line; do
        interface=$(echo "$line" | cut -d: -f2 | tr -d ' ')
        state=$(echo "$line" | grep -o 'state [A-Z]*' | cut -d' ' -f2)
        echo "  $interface ($state)"
    done
    echo "----------------------------------------"
}

# 显示当前路由表
show_current_routes() {
    log_info "当前路由表:"
    echo "----------------------------------------"
    echo "主路由表:"
    ip route show
    echo
    echo "自定义路由表 (table 100-110):"
    for table in {100..110}; do
        if ip route show table "$table" 2>/dev/null | grep -q .; then
            echo "Table $table:"
            ip route show table "$table"
            echo
        fi
    done
    echo "----------------------------------------"
}

# 显示策略规则
show_policy_rules() {
    log_info "当前策略规则:"
    echo "----------------------------------------"
    ip rule show
    echo "----------------------------------------"
}

# 解析域名到IP
resolve_domain() {
    local domain="$1"
    local ips
    
    if [[ "$domain" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "$domain"
        return
    fi
    
    ips=$(dig +short "$domain" A | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$')
    if [[ -n "$ips" ]]; then
        echo "$ips"
    else
        log_error "无法解析域名: $domain"
        return 1
    fi
}

# 添加路由规则
add_route_rule() {
    log_info "添加路由规则配置向导"
    echo
    
    # 显示网络接口
    show_interfaces
    echo
    
    # 输入目标地址
    read -p "请输入目标IP或域名: " target
    if [[ -z "$target" ]]; then
        log_error "目标地址不能为空"
        return 1
    fi
    
    # 解析IP地址
    log_info "解析目标地址..."
    local ips
    ips=$(resolve_domain "$target")
    if [[ $? -ne 0 ]]; then
        return 1
    fi
    
    echo "解析结果:"
    echo "$ips" | while read ip; do
        echo "  $ip"
    done
    echo
    
    # 选择网络接口
    read -p "请输入目标网卡接口名 (如: eth0, wlan0): " interface
    if [[ -z "$interface" ]]; then
        log_error "网卡接口不能为空"
        return 1
    fi
    
    # 检查接口是否存在
    if ! ip link show "$interface" >/dev/null 2>&1; then
        log_error "网卡接口 $interface 不存在"
        return 1
    fi
    
    # 获取接口网关
    local gateway
    gateway=$(ip route show dev "$interface" | grep default | awk '{print $3}' | head -1)
    if [[ -z "$gateway" ]]; then
        read -p "请输入 $interface 的网关地址: " gateway
        if [[ -z "$gateway" ]]; then
            log_error "网关地址不能为空"
            return 1
        fi
    fi
    
    log_info "将使用网关: $gateway"
    
    # 选择路由表
    read -p "请输入路由表ID (100-110, 默认100): " table_id
    table_id=${table_id:-100}
    
    if [[ ! "$table_id" =~ ^[0-9]+$ ]] || [[ "$table_id" -lt 100 ]] || [[ "$table_id" -gt 110 ]]; then
        log_error "路由表ID必须是100-110之间的数字"
        return 1
    fi
    
    # 确认配置
    echo
    log_info "配置摘要:"
    echo "  目标: $target"
    echo "  IP地址: $(echo "$ips" | tr '\n' ' ')"
    echo "  网卡: $interface"
    echo "  网关: $gateway"
    echo "  路由表: $table_id"
    echo
    
    read -p "确认添加此路由规则? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "取消操作"
        return 0
    fi
    
    # 创建路由表
    log_info "配置路由表 $table_id..."
    
    # 添加默认路由到指定表
    if ! ip route show table "$table_id" | grep -q default; then
        ip route add default via "$gateway" dev "$interface" table "$table_id"
        log_success "添加默认路由到表 $table_id"
    fi
    
    # 为每个IP添加策略规则和路由
    echo "$ips" | while read ip; do
        if [[ -n "$ip" ]]; then
            # 添加策略规则
            if ! ip rule show | grep -q "to $ip lookup $table_id"; then
                ip rule add to "$ip" table "$table_id"
                log_success "添加策略规则: $ip -> table $table_id"
            else
                log_warning "策略规则已存在: $ip"
            fi
            
            # 添加具体路由
            if ! ip route show table "$table_id" | grep -q "$ip"; then
                ip route add "$ip" via "$gateway" dev "$interface" table "$table_id"
                log_success "添加路由: $ip via $gateway dev $interface"
            else
                log_warning "路由已存在: $ip"
            fi
        fi
    done
    
    # 保存配置到文件
    local config_file="/etc/network-routing-rules.conf"
    echo "# $(date): $target via $interface (table $table_id)" >> "$config_file"
    echo "$ips" | while read ip; do
        if [[ -n "$ip" ]]; then
            echo "ip rule add to $ip table $table_id" >> "$config_file"
            echo "ip route add $ip via $gateway dev $interface table $table_id" >> "$config_file"
        fi
    done
    echo "" >> "$config_file"
    
    log_success "路由规则添加完成!"
    log_info "配置已保存到: $config_file"
    
    # 刷新路由缓存
    ip route flush cache
    
    # 测试连接
    echo
    read -p "是否测试连接? (y/N): " test_conn
    if [[ "$test_conn" =~ ^[Yy]$ ]]; then
        echo "$ips" | head -1 | while read first_ip; do
            if [[ -n "$first_ip" ]]; then
                log_info "测试连接到 $first_ip..."
                if ping -c 3 -W 5 "$first_ip" >/dev/null 2>&1; then
                    log_success "连接测试成功"
                else
                    log_warning "连接测试失败，请检查配置"
                fi
            fi
        done
    fi
}

# 删除路由规则
delete_route_rule() {
    log_info "删除路由规则"
    echo
    
    # 显示当前规则
    show_policy_rules
    echo
    
    read -p "请输入要删除的目标IP: " target_ip
    if [[ -z "$target_ip" ]]; then
        log_error "目标IP不能为空"
        return 1
    fi
    
    # 查找相关规则
    local rules
    rules=$(ip rule show | grep "to $target_ip")
    
    if [[ -z "$rules" ]]; then
        log_warning "未找到相关规则: $target_ip"
        return 0
    fi
    
    echo "找到以下规则:"
    echo "$rules"
    echo
    
    read -p "确认删除这些规则? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "取消操作"
        return 0
    fi
    
    # 删除策略规则
    while read -r line; do
        if [[ "$line" =~ to\ $target_ip\ lookup\ ([0-9]+) ]]; then
            local table_id="${BASH_REMATCH[1]}"
            ip rule del to "$target_ip" table "$table_id" 2>/dev/null || true
            log_success "删除策略规则: $target_ip table $table_id"
            
            # 删除路由表中的路由
            ip route del "$target_ip" table "$table_id" 2>/dev/null || true
            log_success "删除路由: $target_ip table $table_id"
        fi
    done <<< "$rules"
    
    # 刷新路由缓存
    ip route flush cache
    
    log_success "删除完成"
}

# 重置所有自定义路由
reset_custom_routes() {
    log_warning "这将删除所有自定义路由规则 (table 100-110)"
    read -p "确认重置? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "取消操作"
        return 0
    fi
    
    log_info "重置自定义路由规则..."
    
    # 删除自定义策略规则
    ip rule show | grep "lookup 1[0-9][0-9]" | while read line; do
        if [[ "$line" =~ ([0-9]+): ]]; then
            local priority="${BASH_REMATCH[1]}"
            ip rule del pref "$priority" 2>/dev/null || true
        fi
    done
    
    # 清空自定义路由表
    for table in {100..110}; do
        ip route flush table "$table" 2>/dev/null || true
    done
    
    # 删除配置文件
    rm -f /etc/network-routing-rules.conf
    
    # 刷新路由缓存
    ip route flush cache
    
    log_success "重置完成"
}

# 创建恢复脚本
create_restore_script() {
    local config_file="/etc/network-routing-rules.conf"
    local restore_script="/usr/local/bin/restore-network-routes.sh"
    
    if [[ ! -f "$config_file" ]]; then
        return 0
    fi
    
    cat > "$restore_script" << 'EOF'
#!/bin/bash
# 网络路由规则恢复脚本
# 开机自动执行以恢复自定义路由规则

config_file="/etc/network-routing-rules.conf"

if [[ -f "$config_file" ]]; then
    echo "恢复网络路由规则..."
    
    # 等待网络就绪
    sleep 5
    
    # 执行配置命令
    grep "^ip " "$config_file" | while read cmd; do
        eval "$cmd" 2>/dev/null || true
    done
    
    # 刷新路由缓存
    ip route flush cache
    
    echo "网络路由规则恢复完成"
fi
EOF
    
    chmod +x "$restore_script"
    
    # 创建systemd服务
    cat > /etc/systemd/system/restore-network-routes.service << EOF
[Unit]
Description=Restore Network Routes
After=network-online.target
Wants=network-online.target

[Service]
Type=oneshot
ExecStart=$restore_script
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable restore-network-routes.service
    
    log_info "已创建开机恢复脚本: $restore_script"
}

# 主菜单
show_menu() {
    echo
    echo "========================================"
    echo "        网络路由配置工具"
    echo "========================================"
    echo "1. 添加路由规则"
    echo "2. 删除路由规则"  
    echo "3. 查看当前路由"
    echo "4. 查看策略规则"
    echo "5. 重置所有自定义路由"
    echo "6. 退出"
    echo "========================================"
}

# 交互式菜单
interactive_menu() {
    while true; do
        show_menu
        read -p "请选择操作 (1-6): " choice
        
        case $choice in
            1)
                add_route_rule
                ;;
            2)
                delete_route_rule
                ;;
            3)
                show_current_routes
                ;;
            4)
                show_policy_rules
                ;;
            5)
                reset_custom_routes
                ;;
            6)
                log_info "退出"
                break
                ;;
            *)
                log_error "无效选择，请重试"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
    done
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--add-route)
                add_route_rule
                exit 0
                ;;
            -d|--delete-route)
                delete_route_rule
                exit 0
                ;;
            -l|--list-routes)
                show_current_routes
                show_policy_rules
                exit 0
                ;;
            -r|--reset-routes)
                reset_custom_routes
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
}

# 主函数
main() {
    echo "========================================"
    echo "        网络路由配置脚本"
    echo "========================================"
    
    check_permissions
    check_network_tools
    
    if [[ $# -eq 0 ]]; then
        interactive_menu
    else
        parse_args "$@"
    fi
    
    create_restore_script
    
    log_success "操作完成!"
}

# 执行主函数
main "$@"