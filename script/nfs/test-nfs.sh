#!/bin/bash

# NFS连接测试脚本
# 用途: 全面测试WSL和RK3588之间的NFS连接
# 作者: Auto-generated
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
SERVER_IP=""
CLIENT_IP=""
MOUNT_BASE="/mine"
TEST_DIR="/tmp/nfs_test_$$"
VERBOSE=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# 显示帮助
show_help() {
    echo "NFS连接测试脚本"
    echo
    echo "用法: $0 [选项] <server_ip> [client_ip]"
    echo
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -v, --verbose       详细输出"
    echo "  -m, --mount-base    指定挂载基础目录 (默认: /mine)"
    echo "  --server-only       仅测试服务器端"
    echo "  --client-only       仅测试客户端"
    echo "  --performance       执行性能测试"
    echo
    echo "示例:"
    echo "  $0 *************                    # 基础连接测试"
    echo "  $0 -v ************* *************   # 详细测试"
    echo "  $0 --performance *************      # 性能测试"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -m|--mount-base)
                MOUNT_BASE="$2"
                shift 2
                ;;
            --server-only)
                SERVER_ONLY=true
                shift
                ;;
            --client-only)
                CLIENT_ONLY=true
                shift
                ;;
            --performance)
                PERFORMANCE_TEST=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$SERVER_IP" ]]; then
                    SERVER_IP="$1"
                elif [[ -z "$CLIENT_IP" ]]; then
                    CLIENT_IP="$1"
                else
                    log_error "多余的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [[ -z "$SERVER_IP" ]]; then
        log_error "请指定NFS服务器IP地址"
        show_help
        exit 1
    fi
}

# 检测运行环境
detect_environment() {
    if grep -q Microsoft /proc/version 2>/dev/null; then
        echo "wsl"
    elif [[ -f /proc/device-tree/model ]] && grep -q "RK3588" /proc/device-tree/model 2>/dev/null; then
        echo "rk3588"
    else
        echo "linux"
    fi
}

# 基础网络测试
test_network() {
    log_info "测试网络连接..."

    # Ping测试
    log_verbose "执行ping测试..."
    if ping -c 3 -W 5 "$SERVER_IP" >/dev/null 2>&1; then
        log_success "网络连接正常"
        if [[ "$VERBOSE" == "true" ]]; then
            log_verbose "详细ping结果:"
            ping -c 3 "$SERVER_IP" | sed 's/^/  /'
        fi
    else
        log_error "无法连接到服务器 $SERVER_IP"
        log_error "请检查:"
        log_error "  1. 服务器IP地址是否正确"
        log_error "  2. 网络连接是否正常"
        log_error "  3. 防火墙设置是否阻止连接"
        return 1
    fi

    # 端口测试
    log_verbose "测试NFS相关端口..."
    local ports=(111 2049 20048)
    local open_ports=()
    local closed_ports=()

    for port in "${ports[@]}"; do
        if command -v nc >/dev/null 2>&1; then
            if timeout 5 nc -z "$SERVER_IP" "$port" 2>/dev/null; then
                log_verbose "端口 $port 开放"
                open_ports+=("$port")
            else
                log_warning "端口 $port 可能未开放或被防火墙阻止"
                closed_ports+=("$port")
            fi
        else
            log_verbose "nc命令不可用，跳过端口测试"
            break
        fi
    done

    if [[ ${#open_ports[@]} -gt 0 ]]; then
        log_verbose "开放的端口: ${open_ports[*]}"
    fi

    if [[ ${#closed_ports[@]} -gt 0 ]]; then
        log_verbose "关闭的端口: ${closed_ports[*]}"
    fi
}

# RPC服务测试
test_rpc() {
    log_info "测试RPC服务..."

    if ! command -v rpcinfo >/dev/null 2>&1; then
        log_warning "rpcinfo命令不可用，跳过RPC测试"
        log_info "安装建议: sudo apt install rpcbind"
        return 0
    fi

    log_verbose "查询RPC服务..."
    if timeout 10 rpcinfo -p "$SERVER_IP" >/dev/null 2>&1; then
        log_success "RPC服务正常"

        # 检查NFS相关服务
        local nfs_services
        nfs_services=$(rpcinfo -p "$SERVER_IP" 2>/dev/null | grep -E "(nfs|mount|portmap)")

        if [[ -n "$nfs_services" ]]; then
            log_success "发现NFS相关服务"
            if [[ "$VERBOSE" == "true" ]]; then
                log_verbose "NFS相关RPC服务:"
                echo "$nfs_services" | sed 's/^/  /'
            fi
        else
            log_warning "未发现NFS相关RPC服务"
            log_info "请检查NFS服务器是否正确启动"
        fi
    else
        log_error "RPC服务异常或超时"
        log_error "可能的原因:"
        log_error "  1. RPC服务未启动"
        log_error "  2. 防火墙阻止RPC端口(111)"
        log_error "  3. 网络连接问题"
        return 1
    fi
}

# NFS导出测试
test_exports() {
    log_info "测试NFS导出..."
    
    if command -v showmount >/dev/null 2>&1; then
        if showmount -e "$SERVER_IP" >/dev/null 2>&1; then
            log_success "NFS导出正常"
            log_info "可用的导出:"
            showmount -e "$SERVER_IP" | sed 's/^/  /'
        else
            log_error "无法获取NFS导出列表"
            return 1
        fi
    else
        log_warning "showmount命令不可用，跳过导出测试"
    fi
}

# 挂载测试
test_mount() {
    log_info "测试NFS挂载..."
    
    # 创建临时挂载点
    mkdir -p "$TEST_DIR"
    
    # 尝试挂载
    if sudo mount -t nfs4 "$SERVER_IP:/mine" "$TEST_DIR" 2>/dev/null; then
        log_success "NFS挂载成功"
        
        # 列出内容
        if [[ "$VERBOSE" == "true" ]]; then
            log_verbose "挂载点内容:"
            ls -la "$TEST_DIR" | sed 's/^/  /'
        fi
        
        # 测试读写
        test_read_write
        
        # 卸载
        sudo umount "$TEST_DIR"
        log_verbose "挂载点已卸载"
    else
        log_error "NFS挂载失败"
        rmdir "$TEST_DIR" 2>/dev/null || true
        return 1
    fi
    
    rmdir "$TEST_DIR" 2>/dev/null || true
}

# 读写测试
test_read_write() {
    log_info "测试文件读写..."
    
    local test_file="$TEST_DIR/test_$(date +%s).txt"
    local test_content="NFS测试文件 - $(date)"
    
    # 写入测试
    if echo "$test_content" | sudo tee "$test_file" >/dev/null 2>&1; then
        log_success "文件写入成功"
    else
        log_error "文件写入失败"
        return 1
    fi
    
    # 读取测试
    if [[ -f "$test_file" ]] && grep -q "NFS测试文件" "$test_file" 2>/dev/null; then
        log_success "文件读取成功"
    else
        log_error "文件读取失败"
        return 1
    fi
    
    # 删除测试文件
    sudo rm -f "$test_file" 2>/dev/null || true
    log_verbose "测试文件已清理"
}

# 性能测试
test_performance() {
    if [[ "$PERFORMANCE_TEST" != "true" ]]; then
        return
    fi
    
    log_info "执行性能测试..."
    
    # 创建临时挂载点
    mkdir -p "$TEST_DIR"
    
    if sudo mount -t nfs4 "$SERVER_IP:/mine" "$TEST_DIR" 2>/dev/null; then
        local perf_dir="$TEST_DIR/perf_test"
        sudo mkdir -p "$perf_dir"
        
        # 写入性能测试
        log_info "测试写入性能..."
        local start_time=$(date +%s.%N)
        sudo dd if=/dev/zero of="$perf_dir/test_write.dat" bs=1M count=10 2>/dev/null
        local end_time=$(date +%s.%N)
        local write_time=$(echo "$end_time - $start_time" | bc -l)
        log_success "写入10MB用时: ${write_time}秒"
        
        # 读取性能测试
        log_info "测试读取性能..."
        start_time=$(date +%s.%N)
        sudo dd if="$perf_dir/test_write.dat" of=/dev/null bs=1M 2>/dev/null
        end_time=$(date +%s.%N)
        local read_time=$(echo "$end_time - $start_time" | bc -l)
        log_success "读取10MB用时: ${read_time}秒"
        
        # 清理测试文件
        sudo rm -rf "$perf_dir"
        sudo umount "$TEST_DIR"
    else
        log_error "无法挂载进行性能测试"
    fi
    
    rmdir "$TEST_DIR" 2>/dev/null || true
}

# 服务器端测试
test_server() {
    if [[ "$CLIENT_ONLY" == "true" ]]; then
        return
    fi
    
    log_info "执行服务器端测试..."
    
    # 检查NFS服务状态
    if systemctl is-active --quiet nfs-kernel-server; then
        log_success "NFS服务器运行正常"
    else
        log_error "NFS服务器未运行"
        return 1
    fi
    
    # 检查导出配置
    if [[ -f /etc/exports ]] && grep -q "/nfs/share" /etc/exports; then
        log_success "导出配置存在"
        if [[ "$VERBOSE" == "true" ]]; then
            log_verbose "导出配置:"
            grep "/nfs/share" /etc/exports | sed 's/^/  /'
        fi
    else
        log_warning "导出配置可能有问题"
    fi
    
    # 检查共享目录
    if [[ -d /mine ]]; then
        log_success "共享目录存在"
        if [[ "$VERBOSE" == "true" ]]; then
            log_verbose "共享目录权限:"
            ls -ld /mine | sed 's/^/  /'
        fi
    else
        log_error "共享目录不存在"
        return 1
    fi
}

# 客户端测试
test_client() {
    if [[ "$SERVER_ONLY" == "true" ]]; then
        return
    fi
    
    log_info "执行客户端测试..."
    
    # 检查NFS客户端工具
    local tools=(mount.nfs4 rpcinfo showmount)
    for tool in "${tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            log_verbose "工具 $tool 可用"
        else
            log_warning "工具 $tool 不可用"
        fi
    done
    
    # 检查RPC服务
    if systemctl is-active --quiet rpcbind; then
        log_success "RPC服务运行正常"
    else
        log_warning "RPC服务未运行"
    fi
    
    # 检查自动挂载服务
    if systemctl is-enabled --quiet autofs 2>/dev/null; then
        if systemctl is-active --quiet autofs; then
            log_success "自动挂载服务运行正常"
        else
            log_warning "自动挂载服务未运行"
        fi
    else
        log_info "自动挂载服务未配置"
    fi
}

# 生成测试报告
generate_report() {
    echo
    echo "========================================"
    echo "           NFS测试报告"
    echo "========================================"
    echo "测试时间: $(date)"
    echo "服务器IP: $SERVER_IP"
    echo "客户端IP: ${CLIENT_IP:-本机}"
    echo "运行环境: $(detect_environment)"
    echo "挂载目录: $MOUNT_BASE"
    echo "----------------------------------------"
    
    # 统计测试结果
    local total_tests=0
    local passed_tests=0
    
    # 这里可以添加更详细的测试结果统计
    echo "测试完成"
    echo "========================================"
}

# 清理函数
cleanup() {
    log_verbose "清理临时文件..."
    sudo umount "$TEST_DIR" 2>/dev/null || true
    rmdir "$TEST_DIR" 2>/dev/null || true
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "========================================"
    echo "         NFS连接测试脚本"
    echo "========================================"
    
    parse_args "$@"
    
    log_info "开始NFS连接测试..."
    log_info "服务器: $SERVER_IP"
    log_info "环境: $(detect_environment)"
    
    # 执行测试
    test_network
    test_rpc
    test_exports
    test_server
    test_client
    test_mount
    test_performance
    
    generate_report
    
    log_success "所有测试完成!"
}

# 执行主函数
main "$@"
