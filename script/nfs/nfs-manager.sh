#!/bin/bash

# NFS管理脚本
# 用途: 管理WSL和RK3588之间的NFS连接
# 作者: Auto-generated
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="/etc/nfs-manager.conf"
DEFAULT_SERVER_IP=""
DEFAULT_MOUNT_BASE="/mine"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载配置
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
    fi
}

# 保存配置
save_config() {
    sudo tee "$CONFIG_FILE" << EOF
# NFS管理器配置文件
DEFAULT_SERVER_IP="$DEFAULT_SERVER_IP"
DEFAULT_MOUNT_BASE="$DEFAULT_MOUNT_BASE"
EOF
}

# 显示帮助
show_help() {
    echo "NFS管理脚本 - 管理WSL和RK3588之间的NFS连接"
    echo
    echo "用法: $0 <命令> [选项]"
    echo
    echo "命令:"
    echo "  server <action>     管理NFS服务器 (WSL端)"
    echo "  client <action>     管理NFS客户端 (RK3588端)"
    echo "  mount <target>      挂载NFS共享"
    echo "  umount <target>     卸载NFS共享"
    echo "  status              显示NFS状态"
    echo "  test                测试NFS连接"
    echo "  config              配置管理"
    echo
    echo "服务器操作 (server):"
    echo "  start               启动NFS服务器"
    echo "  stop                停止NFS服务器"
    echo "  restart             重启NFS服务器"
    echo "  status              查看服务器状态"
    echo "  exports             查看导出列表"
    echo "  reload              重新加载导出配置"
    echo
    echo "客户端操作 (client):"
    echo "  mount               挂载所有NFS共享"
    echo "  umount              卸载所有NFS共享"
    echo "  status              查看客户端状态"
    echo "  auto-start          启用自动挂载"
    echo "  auto-stop           禁用自动挂载"
    echo
    echo "挂载目标:"
    echo "  all                 所有共享目录"
    echo "  data                数据目录"
    echo "  logs                日志目录"
    echo "  config              配置目录"
    echo "  backup              备份目录"
    echo
    echo "示例:"
    echo "  $0 server start"
    echo "  $0 client mount"
    echo "  $0 mount data"
    echo "  $0 status"
    echo "  $0 test 192.168.1.100"
}

# 检测运行环境
detect_environment() {
    if grep -q Microsoft /proc/version 2>/dev/null; then
        echo "wsl"
    elif [[ -f /proc/device-tree/model ]] && grep -q "RK3588" /proc/device-tree/model 2>/dev/null; then
        echo "rk3588"
    else
        echo "linux"
    fi
}

# 服务器管理 (WSL端)
manage_server() {
    local action="$1"
    
    case "$action" in
        start)
            log_info "启动NFS服务器..."
            sudo systemctl start rpcbind
            sudo systemctl start nfs-kernel-server
            log_success "NFS服务器已启动"
            ;;
        stop)
            log_info "停止NFS服务器..."
            sudo systemctl stop nfs-kernel-server
            sudo systemctl stop rpcbind
            log_success "NFS服务器已停止"
            ;;
        restart)
            log_info "重启NFS服务器..."
            sudo systemctl restart rpcbind
            sudo systemctl restart nfs-kernel-server
            log_success "NFS服务器已重启"
            ;;
        status)
            echo "NFS服务器状态:"
            echo "----------------------------------------"
            systemctl status rpcbind --no-pager -l
            echo
            systemctl status nfs-kernel-server --no-pager -l
            ;;
        exports)
            echo "NFS导出列表:"
            echo "----------------------------------------"
            sudo exportfs -v
            ;;
        reload)
            log_info "重新加载NFS导出配置..."
            sudo exportfs -ra
            log_success "导出配置已重新加载"
            ;;
        *)
            log_error "未知的服务器操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 客户端管理 (RK3588端)
manage_client() {
    local action="$1"
    
    case "$action" in
        mount)
            log_info "挂载所有NFS共享..."
            mount_nfs "all"
            ;;
        umount)
            log_info "卸载所有NFS共享..."
            umount_nfs "all"
            ;;
        status)
            echo "NFS客户端状态:"
            echo "----------------------------------------"
            systemctl status rpcbind --no-pager -l
            echo
            if systemctl is-enabled autofs >/dev/null 2>&1; then
                systemctl status autofs --no-pager -l
            fi
            echo
            echo "已挂载的NFS:"
            df -h | grep nfs || echo "无NFS挂载"
            ;;
        auto-start)
            log_info "启用自动挂载..."
            sudo systemctl enable autofs
            sudo systemctl start autofs
            log_success "自动挂载已启用"
            ;;
        auto-stop)
            log_info "禁用自动挂载..."
            sudo systemctl stop autofs
            sudo systemctl disable autofs
            log_success "自动挂载已禁用"
            ;;
        *)
            log_error "未知的客户端操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 挂载NFS
mount_nfs() {
    local target="$1"
    local server_ip="${2:-$DEFAULT_SERVER_IP}"
    local mount_base="${3:-$DEFAULT_MOUNT_BASE}"
    
    if [[ -z "$server_ip" ]]; then
        log_error "请指定服务器IP地址"
        exit 1
    fi
    
    case "$target" in
        all)
            sudo mkdir -p "$mount_base"
            sudo mount -t nfs4 "$server_ip:/mine" "$mount_base"
            log_success "已挂载主目录: $mount_base"
            ;;
        data)
            sudo mkdir -p "$mount_base/data"
            sudo mount -t nfs4 "$server_ip:/mine/data" "$mount_base/data"
            log_success "已挂载数据目录: $mount_base/data"
            ;;
        logs)
            sudo mkdir -p "$mount_base/logs"
            sudo mount -t nfs4 "$server_ip:/mine/logs" "$mount_base/logs"
            log_success "已挂载日志目录: $mount_base/logs"
            ;;
        config)
            sudo mkdir -p "$mount_base/config"
            sudo mount -t nfs4 "$server_ip:/mine/config" "$mount_base/config"
            log_success "已挂载配置目录: $mount_base/config"
            ;;
        backup)
            sudo mkdir -p "$mount_base/backup"
            sudo mount -t nfs4 "$server_ip:/mine/backup" "$mount_base/backup"
            log_success "已挂载备份目录: $mount_base/backup"
            ;;
        *)
            log_error "未知的挂载目标: $target"
            exit 1
            ;;
    esac
}

# 卸载NFS
umount_nfs() {
    local target="$1"
    local mount_base="${2:-$DEFAULT_MOUNT_BASE}"
    
    case "$target" in
        all)
            sudo umount -a -t nfs4 2>/dev/null || true
            log_success "已卸载所有NFS挂载"
            ;;
        data)
            sudo umount "$mount_base/data" 2>/dev/null || true
            log_success "已卸载数据目录"
            ;;
        logs)
            sudo umount "$mount_base/logs" 2>/dev/null || true
            log_success "已卸载日志目录"
            ;;
        config)
            sudo umount "$mount_base/config" 2>/dev/null || true
            log_success "已卸载配置目录"
            ;;
        backup)
            sudo umount "$mount_base/backup" 2>/dev/null || true
            log_success "已卸载备份目录"
            ;;
        *)
            log_error "未知的卸载目标: $target"
            exit 1
            ;;
    esac
}

# 显示状态
show_status() {
    local env=$(detect_environment)
    
    echo "NFS系统状态"
    echo "========================================"
    echo "运行环境: $env"
    echo "配置文件: $CONFIG_FILE"
    echo "默认服务器: ${DEFAULT_SERVER_IP:-未配置}"
    echo "默认挂载点: $DEFAULT_MOUNT_BASE"
    echo
    
    case "$env" in
        wsl)
            echo "WSL NFS服务器状态:"
            echo "----------------------------------------"
            if systemctl is-active --quiet nfs-kernel-server; then
                echo "✓ NFS服务器运行中"
                echo "✓ 服务器IP: $(hostname -I | awk '{print $1}')"
                echo "✓ 导出目录:"
                sudo exportfs -v | sed 's/^/  /'
            else
                echo "✗ NFS服务器未运行"
            fi
            ;;
        rk3588|linux)
            echo "NFS客户端状态:"
            echo "----------------------------------------"
            if systemctl is-active --quiet rpcbind; then
                echo "✓ RPC服务运行中"
            else
                echo "✗ RPC服务未运行"
            fi
            
            if systemctl is-active --quiet autofs; then
                echo "✓ 自动挂载服务运行中"
            else
                echo "✗ 自动挂载服务未运行"
            fi
            
            echo "✓ 已挂载的NFS:"
            df -h | grep nfs | sed 's/^/  /' || echo "  无NFS挂载"
            ;;
    esac
    
    echo "========================================"
}

# 测试连接
test_connection() {
    local server_ip="${1:-$DEFAULT_SERVER_IP}"
    
    if [[ -z "$server_ip" ]]; then
        log_error "请指定服务器IP地址"
        exit 1
    fi
    
    log_info "测试与 $server_ip 的NFS连接..."
    
    # 测试网络连接
    if ! ping -c 3 "$server_ip" >/dev/null 2>&1; then
        log_error "网络连接失败"
        exit 1
    fi
    log_success "网络连接正常"
    
    # 测试RPC服务
    if ! rpcinfo -p "$server_ip" | grep -q nfs >/dev/null 2>&1; then
        log_error "NFS服务未运行"
        exit 1
    fi
    log_success "NFS服务正常"
    
    # 测试导出列表
    log_info "可用的导出:"
    showmount -e "$server_ip" | sed 's/^/  /'
    
    # 测试挂载
    local temp_mount="/tmp/nfs_test_$$"
    mkdir -p "$temp_mount"
    
    if sudo mount -t nfs4 "$server_ip:/mine" "$temp_mount"; then
        log_success "挂载测试成功"
        ls -la "$temp_mount" | head -5 | sed 's/^/  /'
        sudo umount "$temp_mount"
    else
        log_error "挂载测试失败"
    fi
    
    rmdir "$temp_mount"
}

# 配置管理
manage_config() {
    local action="$1"
    
    case "$action" in
        show)
            echo "当前配置:"
            echo "----------------------------------------"
            if [[ -f "$CONFIG_FILE" ]]; then
                cat "$CONFIG_FILE"
            else
                echo "配置文件不存在"
            fi
            ;;
        set-server)
            read -p "请输入NFS服务器IP: " server_ip
            DEFAULT_SERVER_IP="$server_ip"
            save_config
            log_success "服务器IP已设置为: $server_ip"
            ;;
        set-mount)
            read -p "请输入挂载基础目录: " mount_base
            DEFAULT_MOUNT_BASE="$mount_base"
            save_config
            log_success "挂载目录已设置为: $mount_base"
            ;;
        *)
            echo "配置管理:"
            echo "  $0 config show        显示当前配置"
            echo "  $0 config set-server  设置服务器IP"
            echo "  $0 config set-mount   设置挂载目录"
            ;;
    esac
}

# 主函数
main() {
    load_config
    
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        server)
            manage_server "$@"
            ;;
        client)
            manage_client "$@"
            ;;
        mount)
            mount_nfs "$@"
            ;;
        umount)
            umount_nfs "$@"
            ;;
        status)
            show_status
            ;;
        test)
            test_connection "$@"
            ;;
        config)
            manage_config "$@"
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
