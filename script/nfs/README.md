# WSL NFS服务器配置指南

## 概述

本文档提供在WSL (Windows Subsystem for Linux) 环境下配置NFS (Network File System) 服务器，并与RK3588开发板进行远程文件系统挂载的完整解决方案。

## 系统要求

### WSL环境要求
- Windows 10 版本 2004 及以上 或 Windows 11
- WSL2 (推荐)
- Ubuntu 20.04 LTS 或更高版本
- 至少 4GB 可用内存
- 足够的存储空间用于共享目录

### RK3588环境要求
- RK3588开发板
- Linux内核 4.9+ (支持NFS客户端)
- 网络连接 (WiFi/以太网)
- 足够的挂载点权限

## 架构说明

```
┌─────────────────┐    NFS协议     ┌─────────────────┐
│   WSL2 环境     │ ◄──────────► │   RK3588 设备   │
│                 │   (TCP 2049)   │                 │
│ ┌─────────────┐ │              │ ┌─────────────┐ │
│ │ NFS服务器   │ │              │ │ NFS客户端   │ │
│ │ (nfs-server)│ │              │ │ (nfs-utils) │ │
│ └─────────────┘ │              │ └─────────────┘ │
│                 │              │        │        │
│ ┌─────────────┐ │              │ ┌─────────────┐ │
│ │ 共享目录    │ │              │ │ 挂载点      │ │
│ │ /mine       │ │              │ │ /mine       │ │
│ └─────────────┘ │              │ └─────────────┘ │
└─────────────────┘              └─────────────────┘
```

## 支持的NFS版本

### NFS协议版本
- **NFSv3**: 传统版本，兼容性好，无状态协议
- **NFSv4**: 现代版本，支持安全认证，有状态协议
- **NFSv4.1**: 增强版本，支持并行访问
- **NFSv4.2**: 最新版本，支持服务器端复制

### 传输协议
- **TCP**: 可靠传输，推荐用于WAN
- **UDP**: 快速传输，适用于LAN
- **RDMA**: 高性能网络，适用于数据中心

## 依赖软件

### WSL端依赖
```bash
# NFS服务器
nfs-kernel-server
nfs-common
rpcbind
portmap

# 网络工具
net-tools
netstat-nat
iptables

# 文件系统工具
cifs-utils
samba-common

# 监控工具 (可选)
# nfsstat (包含在nfs-common中)
# nfswatch (需要单独安装)
```

### RK3588端依赖
```bash
# NFS客户端
nfs-common
nfs-utils
rpcbind

# 挂载工具
mount
umount
autofs

# 网络工具
curl
wget
ping
telnet
```

## 配置步骤

### 1. WSL环境准备

#### 1.1 启用WSL2
```powershell
# 在Windows PowerShell (管理员) 中执行
wsl --install
wsl --set-default-version 2
```

#### 1.2 安装Ubuntu
```powershell
wsl --install -d Ubuntu-20.04
```

#### 1.3 配置USB设备访问
安装usbipd-win工具以在WSL中访问USB设备：
```powershell
# 在Windows中安装
winget install usbipd
```

### 2. NFS服务器安装

#### 2.1 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

#### 2.2 安装NFS服务器
```bash
sudo apt install -y nfs-kernel-server nfs-common rpcbind \
    net-tools iptables cifs-utils
```

#### 2.3 创建共享目录
```bash
# 创建NFS共享根目录
sudo mkdir -p /nfs/share
sudo mkdir -p /nfs/share/data
sudo mkdir -p /nfs/share/logs
sudo mkdir -p /nfs/share/config

# 设置权限
sudo chown -R nobody:nogroup /nfs/share
sudo chmod -R 755 /nfs/share
```

#### 2.4 配置NFS导出
```bash
# 备份原配置
sudo cp /etc/exports /etc/exports.backup

# 配置导出目录
sudo tee /etc/exports << 'EOF'
# NFS导出配置
/nfs/share *(rw,sync,no_subtree_check,no_root_squash)
/nfs/share/data *(rw,sync,no_subtree_check,all_squash,anonuid=1000,anongid=1000)
/nfs/share/logs *(ro,sync,no_subtree_check)
/nfs/share/config *(rw,sync,no_subtree_check,root_squash)
EOF
```

### 3. RK3588客户端配置

#### 3.1 安装NFS客户端
```bash
# 在RK3588上安装NFS客户端
sudo apt update
sudo apt install -y nfs-common rpcbind autofs
```

#### 3.2 创建挂载点
```bash
# 创建挂载目录
sudo mkdir -p /mine
sudo mkdir -p /mine/data
sudo mkdir -p /mine/logs
sudo mkdir -p /mine/config

# 设置权限
sudo chmod 755 /mine
```

#### 3.3 配置自动挂载
```bash
# 配置autofs主配置文件
sudo tee -a /etc/auto.master << 'EOF'
/mine /etc/auto.nfs --timeout=60
EOF

# 创建NFS自动挂载配置
sudo tee /etc/auto.nfs << 'EOF'
data -fstype=nfs4,rw WSL_SERVER_IP:/mine/data
logs -fstype=nfs4,ro WSL_SERVER_IP:/mine/logs
config -fstype=nfs4,rw WSL_SERVER_IP:/mine/config
EOF
```

## 服务配置

### WSL NFC服务器服务
创建systemd服务文件：

```bash
sudo tee /etc/systemd/system/nfc-server.service << 'EOF'
[Unit]
Description=NFC Server for RK3588 Communication
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/nfc-server
Restart=always
RestartSec=5
Environment=LIBNFC_LOG_LEVEL=1

[Install]
WantedBy=multi-user.target
EOF
```

### 启用服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable nfc-server
sudo systemctl start nfc-server
```

## 网络配置

### 防火墙设置
```bash
# WSL端开放端口
sudo ufw allow 4242/tcp
sudo ufw allow 4243/udp

# 如果使用Windows防火墙，需要在Windows中开放相应端口
```

### 网络发现
```bash
# 在RK3588上发现WSL服务器
nmap -p 4242 ***********/24

# 测试连接
telnet <WSL_IP> 4242
```

## 故障排除

### 常见问题

1. **USB设备无法访问**
   ```bash
   # 检查USB设备
   lsusb
   
   # 在Windows中共享USB设备到WSL
   usbipd wsl list
   usbipd wsl attach --busid <BUSID>
   ```

2. **权限问题**
   ```bash
   # 添加用户到dialout组
   sudo usermod -a -G dialout $USER
   
   # 设置设备权限
   sudo chmod 666 /dev/ttyUSB*
   ```

3. **网络连接问题**
   ```bash
   # 检查端口监听
   netstat -tlnp | grep 4242
   
   # 检查防火墙
   sudo ufw status
   ```

## 下一步

请参考以下文档继续配置：
- `install.sh` - 自动安装脚本
- `rk3588-setup.md` - RK3588端详细配置
- `device-mount.md` - 设备挂载配置
- `test-scripts/` - 测试脚本目录

## 参考资料

- [libnfc官方文档](https://github.com/nfc-tools/libnfc)
- [RK3588技术参考手册](https://www.rock-chips.com/a/en/products/RK35_Series/2022/0926/1660.html)
- [WSL USB设备访问指南](https://docs.microsoft.com/en-us/windows/wsl/connect-usb)
