#!/bin/bash

# WSL NFS服务器自动安装脚本
# 用途: 在WSL环境中自动配置NFS服务器
# 作者: Auto-generated
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 环境变量
WSL_ENV=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        exit 1
    fi
}

# 检查WSL环境
check_wsl() {
    if grep -q Microsoft /proc/version 2>/dev/null ||
       [[ -f /proc/sys/fs/binfmt_misc/WSLInterop ]] ||
       [[ "$WSL_DISTRO_NAME" != "" ]] ||
       grep -q "wsl" /proc/version 2>/dev/null ||
       [[ -d /mnt/c ]] && [[ -f /etc/wsl.conf ]]; then
        log_info "检测到WSL环境，将使用WSL优化配置"
        WSL_ENV=true
    elif systemctl --version >/dev/null 2>&1 && ! systemctl is-system-running >/dev/null 2>&1; then
        log_info "检测到容器化环境，使用WSL兼容配置"
        WSL_ENV=true
    else
        log_info "检测到标准Linux环境"
        WSL_ENV=false
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    log_success "网络连接正常"
}

# 更新系统
update_system() {
    log_info "更新系统包..."

    # 先清理apt锁定
    cleanup_apt_locks

    # 清理包缓存
    sudo apt clean

    # 更新包索引
    if ! sudo apt update; then
        log_error "包索引更新失败"
        exit 1
    fi

    # 升级系统包 (可选)
    sudo apt upgrade -y || log_warning "系统升级部分失败，继续安装..."

    # 修复可能的包依赖问题
    sudo apt --fix-broken install -y || log_warning "包依赖修复失败，继续..."

    log_success "系统更新完成"
}

# 清理apt锁定问题
cleanup_apt_locks() {
    log_info "清理apt锁定问题..."

    # 停止可能的apt进程
    sudo pkill -f apt 2>/dev/null || true
    sudo pkill -f dpkg 2>/dev/null || true

    # 清理锁文件
    sudo rm -f /var/lib/dpkg/lock-frontend 2>/dev/null || true
    sudo rm -f /var/lib/dpkg/lock 2>/dev/null || true
    sudo rm -f /var/cache/apt/archives/lock 2>/dev/null || true

    # 修复损坏的包配置
    sudo dpkg --configure -a 2>/dev/null || true

    # 设置非交互模式
    export DEBIAN_FRONTEND=noninteractive

    log_success "apt锁定问题清理完成"
}

# 检查NFS是否已安装
check_nfs_installed() {
    if command -v exportfs >/dev/null 2>&1 && command -v rpc.nfsd >/dev/null 2>&1; then
        log_success "NFS工具已安装，跳过安装步骤"
        return 0
    else
        return 1
    fi
}

# 安装NFS服务器
install_nfs_server() {
    log_info "安装NFS服务器..."

    # 检查是否已安装
    if check_nfs_installed; then
        return 0
    fi

    # 清理apt锁定
    cleanup_apt_locks

    # 定义包列表
    local core_packages="rpcbind nfs-common nfs-kernel-server"
    local optional_packages="net-tools iptables cifs-utils"

    # 安装核心包
    log_info "安装核心NFS包..."
    for package in $core_packages; do
        log_info "安装 $package..."

        if [[ "$package" == "nfs-kernel-server" ]]; then
            # NFS服务器包可能遇到systemd问题
            log_info "安装 $package (可能遇到systemd问题，这是正常的)..."
            if sudo apt install -y "$package" 2>&1 | tee /tmp/nfs_install.log; then
                log_success "$package 安装成功"
            else
                # 检查是否因为systemd问题导致的"失败"
                if grep -q "Could not execute systemctl" /tmp/nfs_install.log ||
                   grep -q "正在设置 nfs-kernel-server" /tmp/nfs_install.log ||
                   grep -q "Created symlink" /tmp/nfs_install.log; then
                    log_warning "$package 安装遇到systemd问题，但包可能已安装"
                    # 检查关键文件是否存在
                    if [[ -f /usr/sbin/exportfs ]] && [[ -f /usr/sbin/rpc.nfsd ]]; then
                        log_success "$package 核心文件已安装"
                    else
                        log_error "$package 安装失败"
                        return 1
                    fi
                else
                    log_error "$package 安装失败"
                    return 1
                fi
            fi
        else
            if sudo apt install -y "$package"; then
                log_success "$package 安装成功"
            else
                log_error "$package 安装失败，这是必需的包"
                return 1
            fi
        fi
    done

    # 安装可选包
    log_info "安装可选工具包..."
    for package in $optional_packages; do
        log_info "尝试安装 $package..."
        if sudo apt install -y "$package"; then
            log_success "$package 安装成功"
        else
            log_warning "$package 安装失败，跳过（非必需）"
        fi
    done

    # 验证核心工具
    local required_commands="exportfs rpcinfo showmount"
    local missing_commands=()

    for cmd in $required_commands; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        log_error "请检查包安装是否成功"
        return 1
    fi

    log_success "NFS服务器安装完成"
}

# 创建共享目录
create_shared_directories() {
    log_info "创建NFS共享目录..."

    # 创建目录结构
    sudo mkdir -p /mine/{data,logs,config,backup}

    # 设置权限
    sudo chown -R nobody:nogroup /mine
    sudo chmod -R 755 /mine

    # 创建测试文件
    echo "NFS Server Test File - $(date)" | sudo tee /mine/test.txt
    echo "Data directory for RK3588" | sudo tee /mine/data/README.txt
    echo "Logs directory for RK3588" | sudo tee /mine/logs/README.txt
    echo "Config directory for RK3588" | sudo tee /mine/config/README.txt

    log_success "共享目录创建完成"
}

# 配置NFS导出
configure_nfs_exports() {
    log_info "配置NFS导出..."
    
    # 备份原配置
    if [[ -f /etc/exports ]]; then
        sudo cp /etc/exports /etc/exports.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 创建新的导出配置
    if [[ "$WSL_ENV" == "true" ]]; then
        # WSL环境：添加insecure选项
        sudo tee /etc/exports << 'EOF'
# NFS导出配置 - WSL优化版本
# 格式: 目录 客户端(选项)

# 主共享目录 - 读写访问，无子树检查，允许root访问
/mine *(rw,sync,no_subtree_check,no_root_squash,insecure)

# 数据目录 - 读写访问，映射到普通用户
/mine/data *(rw,sync,no_subtree_check,all_squash,anonuid=1000,anongid=1000,insecure)

# 日志目录 - 只读访问
/mine/logs *(ro,sync,no_subtree_check,insecure)

# 配置目录 - 读写访问，root权限压缩
/mine/config *(rw,sync,no_subtree_check,root_squash,insecure)

# 备份目录 - 只读访问
/mine/backup *(ro,sync,no_subtree_check,root_squash,insecure)
EOF
    else
        # 标准Linux环境：标准配置
        sudo tee /etc/exports << 'EOF'
# NFS导出配置 - 标准版本
# 格式: 目录 客户端(选项)

# 主共享目录 - 读写访问，无子树检查，允许root访问
/mine *(rw,sync,no_subtree_check,no_root_squash)

# 数据目录 - 读写访问，映射到普通用户
/mine/data *(rw,sync,no_subtree_check,all_squash,anonuid=1000,anongid=1000)

# 日志目录 - 只读访问
/mine/logs *(ro,sync,no_subtree_check)

# 配置目录 - 读写访问，root权限压缩
/mine/config *(rw,sync,no_subtree_check,root_squash)

# 备份目录 - 只读访问
/mine/backup *(ro,sync,no_subtree_check,root_squash)
EOF
    fi
    
    log_success "NFS导出配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查ufw是否安装
    if command -v ufw >/dev/null 2>&1; then
        # 开放NFS相关端口
        sudo ufw allow 2049/tcp  # NFS
        sudo ufw allow 111/tcp   # RPC portmapper
        sudo ufw allow 111/udp   # RPC portmapper
        sudo ufw allow 20048/tcp # mountd
        sudo ufw allow 32765:32768/tcp # 动态端口范围
        sudo ufw allow 32765:32768/udp # 动态端口范围
        
        log_success "防火墙规则配置完成"
    else
        log_warning "未检测到ufw，请手动配置防火墙"
    fi
}

# 启动NFS服务
start_nfs_services() {
    log_info "启动NFS服务..."

    if [[ "$WSL_ENV" == "true" ]]; then
        # WSL环境：使用特殊启动方法
        log_info "WSL环境：使用手动启动方法..."

        # 创建必要的目录
        sudo mkdir -p /var/lib/nfs/rpc_pipefs
        sudo mkdir -p /var/lib/nfs/v4recovery
        sudo mkdir -p /run/rpc_pipefs

        # 启动RPC服务
        log_info "启动RPC服务..."
        if ! sudo systemctl start rpcbind 2>/dev/null; then
            log_warning "systemctl启动RPC失败，尝试手动启动..."
            sudo /usr/sbin/rpcbind -w 2>/dev/null || true
        fi

        # 挂载必要的文件系统
        if ! mountpoint -q /run/rpc_pipefs 2>/dev/null; then
            sudo mount -t rpc_pipefs rpc_pipefs /run/rpc_pipefs 2>/dev/null || true
        fi

        if ! mountpoint -q /proc/fs/nfsd 2>/dev/null; then
            sudo modprobe nfsd 2>/dev/null || true
            sudo mount -t nfsd nfsd /proc/fs/nfsd 2>/dev/null || true
        fi

        # 启动NFS服务器
        log_info "启动NFS服务器..."
        if ! sudo systemctl start nfs-kernel-server 2>/dev/null; then
            log_warning "systemctl启动NFS失败，尝试手动启动..."

            # 手动启动NFS守护进程
            sudo /usr/sbin/rpc.nfsd 8 2>/dev/null || true
            sleep 1
            sudo /usr/sbin/rpc.mountd 2>/dev/null || true
        fi

        # 创建WSL启动脚本
        create_wsl_startup_script
        
        log_success "WSL环境NFS服务启动完成"

    else
        # 标准Linux环境：正常启动
        log_info "启动RPC服务..."
        if sudo systemctl enable rpcbind && sudo systemctl start rpcbind; then
            log_success "RPC服务启动成功"
        else
            log_error "RPC服务启动失败"
            return 1
        fi

        # 等待RPC服务完全启动
        sleep 2

        # 启动并启用NFS服务器
        log_info "启动NFS服务器..."
        if sudo systemctl enable nfs-kernel-server && sudo systemctl start nfs-kernel-server; then
            log_success "NFS服务器启动成功"
        else
            log_error "NFS服务器启动失败"
            log_info "尝试查看错误日志: sudo journalctl -u nfs-kernel-server -n 20"
            return 1
        fi

        # 等待服务完全启动
        sleep 3
    fi

    # 重新导出NFS共享
    log_info "导出NFS共享..."
    if sudo exportfs -ra; then
        log_success "NFS共享导出成功"
    else
        log_warning "NFS共享导出失败，尝试强制导出..."
        sudo exportfs -a 2>/dev/null || true
    fi

    log_success "NFS服务启动完成"
}

# 创建WSL启动脚本
create_wsl_startup_script() {
    log_info "创建WSL启动脚本..."
    
    # 创建启动脚本
    cat > /tmp/start-nfs.sh << 'EOF'
#!/bin/bash
# WSL NFS服务启动脚本

echo "启动WSL NFS服务..."

# 创建必要的目录
sudo mkdir -p /var/lib/nfs/rpc_pipefs
sudo mkdir -p /var/lib/nfs/v4recovery
sudo mkdir -p /run/rpc_pipefs

# 启动RPC服务
if ! sudo systemctl start rpcbind 2>/dev/null; then
    echo "使用手动方式启动RPC..."
    sudo /usr/sbin/rpcbind -w 2>/dev/null || true
fi

# 挂载必要的文件系统
if ! mountpoint -q /run/rpc_pipefs 2>/dev/null; then
    sudo mount -t rpc_pipefs rpc_pipefs /run/rpc_pipefs 2>/dev/null || true
fi

if ! mountpoint -q /proc/fs/nfsd 2>/dev/null; then
    sudo modprobe nfsd 2>/dev/null || true
    sudo mount -t nfsd nfsd /proc/fs/nfsd 2>/dev/null || true
fi

# 启动NFS服务器
if ! sudo systemctl start nfs-kernel-server 2>/dev/null; then
    echo "使用手动方式启动NFS..."
    sudo /usr/sbin/rpc.nfsd 8 2>/dev/null || true
    sleep 1
    sudo /usr/sbin/rpc.mountd 2>/dev/null || true
fi

# 导出NFS共享
sudo exportfs -ra

echo "NFS服务启动完成!"
echo "服务器IP: $(hostname -I | awk '{print $1}')"
EOF
    
    # 安装启动脚本
    sudo cp /tmp/start-nfs.sh /usr/local/bin/
    sudo chmod +x /usr/local/bin/start-nfs.sh
    
    # 创建快捷启动脚本
    cat > /tmp/nfs-start.sh << 'EOF'
#!/bin/bash
# NFS快捷启动脚本
/usr/local/bin/start-nfs.sh
EOF
    
    sudo cp /tmp/nfs-start.sh /mine/
    sudo chmod +x /mine/nfs-start.sh
    
    log_success "WSL启动脚本创建完成"
    log_info "WSL重启后运行: /mine/nfs-start.sh 或 start-nfs.sh"
}

# 验证安装
verify_installation() {
    log_info "验证NFS服务器安装..."

    local verification_failed=false

    # 检查RPC服务状态
    if systemctl is-active --quiet rpcbind; then
        log_success "RPC服务运行正常"
    else
        log_error "RPC服务未运行"
        verification_failed=true
    fi

    # 检查NFS服务器状态
    if systemctl is-active --quiet nfs-kernel-server; then
        log_success "NFS服务器运行正常"
    else
        log_error "NFS服务器未运行"
        log_info "查看错误日志: sudo journalctl -u nfs-kernel-server -n 10"
        verification_failed=true
    fi

    # 检查共享目录
    if [[ -d /mine ]]; then
        log_success "共享目录存在"
        log_info "目录权限: $(ls -ld /mine | awk '{print $1, $3, $4}')"
    else
        log_error "共享目录不存在"
        verification_failed=true
    fi

    # 检查导出列表
    if sudo exportfs -v | grep -q "/mine"; then
        log_success "NFS导出配置正确"
    else
        log_error "NFS导出配置错误或为空"
        log_info "检查 /etc/exports 文件内容"
        verification_failed=true
    fi

    # 检查端口监听
    if command -v netstat >/dev/null 2>&1; then
        if netstat -tlnp 2>/dev/null | grep -q ":2049"; then
            log_success "NFS端口(2049)正在监听"
        else
            log_warning "NFS端口(2049)未监听"
        fi
    fi

    if [[ "$verification_failed" == "true" ]]; then
        log_error "验证失败，请检查上述错误"
        return 1
    fi

    # 显示服务器信息
    echo
    log_info "NFS服务器信息:"
    echo "----------------------------------------"
    echo "服务器IP: $(hostname -I | awk '{print $1}')"
    echo "共享目录: /mine"
    echo "导出列表:"
    sudo exportfs -v | sed 's/^/  /'
    echo "----------------------------------------"

    log_success "NFS服务器验证通过"
}

# 创建客户端连接脚本
create_client_script() {
    log_info "创建客户端连接脚本..."
    
    cat > /tmp/nfs-client-setup.sh << 'EOF'
#!/bin/bash
# RK3588 NFS客户端安装脚本

# 获取WSL服务器IP
WSL_SERVER_IP="$1"

if [[ -z "$WSL_SERVER_IP" ]]; then
    echo "用法: $0 <WSL_SERVER_IP>"
    echo "示例: $0 *************"
    exit 1
fi

# 安装NFS客户端
sudo apt update
sudo apt install -y nfs-common rpcbind autofs

# 创建挂载点
sudo mkdir -p /mnt/nfs/{data,logs,config,backup}

# 测试挂载
echo "测试挂载NFS共享..."
sudo mount -t nfs4 ${WSL_SERVER_IP}:/mine /mine

if [[ $? -eq 0 ]]; then
    echo "NFS挂载成功!"
    ls -la /mine/
    sudo umount /mine
else
    echo "NFS挂载失败，请检查网络连接和防火墙设置"
fi

# 配置自动挂载
sudo tee -a /etc/auto.master << EOL
/mine /etc/auto.nfs --timeout=60
EOL

sudo tee /etc/auto.nfs << EOL
data -fstype=nfs4,rw ${WSL_SERVER_IP}:/mine/data
logs -fstype=nfs4,ro ${WSL_SERVER_IP}:/mine/logs
config -fstype=nfs4,rw ${WSL_SERVER_IP}:/mine/config
backup -fstype=nfs4,ro ${WSL_SERVER_IP}:/mine/backup
EOL

# 启动autofs
sudo systemctl enable autofs
sudo systemctl start autofs

echo "NFS客户端配置完成!"
EOF

    chmod +x /tmp/nfs-client-setup.sh
    sudo cp /tmp/nfs-client-setup.sh /mine/

    log_success "客户端脚本创建完成: /mine/nfs-client-setup.sh"
}

# 显示使用说明
show_usage() {
    echo
    log_info "安装完成! 使用说明:"
    echo "----------------------------------------"
    echo "1. WSL服务器IP: $(hostname -I | awk '{print $1}')"
    echo "2. 在RK3588上运行以下命令连接:"
    echo "   ./install-nfs-client.sh $(hostname -I | awk '{print $1}')"
    echo
    echo "3. 或者手动挂载:"
    echo "   sudo mount -t nfs4 $(hostname -I | awk '{print $1}'):/mine /mine"
    echo
    echo "4. 管理命令:"
    echo "   sudo systemctl status nfs-kernel-server  # 查看服务状态"
    echo "   sudo exportfs -ra                        # 重新导出"
    echo "   sudo exportfs -v                         # 查看导出列表"
    echo "   showmount -e localhost                   # 查看可挂载的导出"
    echo
    echo "5. 使用管理工具:"
    echo "   ./nfs-manager.sh status                  # 查看状态"
    echo "   ./nfs-manager.sh server start            # 启动服务器"
    echo "   ./test-nfs.sh $(hostname -I | awk '{print $1}')  # 测试连接"
    echo
    echo "6. 故障排除:"
    echo "   如果遇到问题，请查看日志:"
    echo "   sudo journalctl -u nfs-kernel-server -n 20"
    echo "   sudo journalctl -u rpcbind -n 20"
    echo "----------------------------------------"
}

# 主函数
main() {
    echo "========================================"
    echo "    WSL NFS服务器自动安装脚本"
    echo "========================================"
    
    check_root
    check_wsl
    check_network
    update_system
    install_nfs_server
    create_shared_directories
    configure_nfs_exports
    configure_firewall
    start_nfs_services
    verify_installation
    create_client_script
    show_usage
    
    log_success "NFS服务器安装完成!"
}

# 执行主函数
main "$@"
