# NFS快速开始指南

## 概述

本指南帮助您快速在WSL和RK3588之间建立NFS连接。

## 前提条件

- WSL2 环境 (Windows 10/11)
- RK3588 开发板
- 两设备在同一网络中
- 具有sudo权限

## 快速安装

### 1. WSL端 (NFS服务器)

```bash
# 下载并运行安装脚本
cd nfc
chmod +x install-nfs-server.sh
./install-nfs-server.sh
```

安装完成后，记录显示的WSL服务器IP地址。

### 2. RK3588端 (NFS客户端)

```bash
# 下载客户端脚本
wget http://WSL_SERVER_IP:8000/install-nfs-client.sh
# 或者从共享目录复制
scp user@WSL_SERVER_IP:/nfs/share/install-nfs-client.sh .

# 运行安装脚本
chmod +x install-nfs-client.sh
./install-nfs-client.sh WSL_SERVER_IP
```

将 `WSL_SERVER_IP` 替换为实际的WSL服务器IP地址。

## 验证连接

### 测试连接
```bash
# 在RK3588上运行
./test-nfs.sh WSL_SERVER_IP
```

### 手动挂载测试
```bash
# 创建挂载点
sudo mkdir -p /mine

# 挂载NFS共享
sudo mount -t nfs4 WSL_SERVER_IP:/mine /mine

# 查看内容
ls -la /mine

# 卸载
sudo umount /mine
```

## 使用NFS管理器

安装完成后，可以使用NFS管理器脚本进行日常管理：

```bash
# 使脚本可执行
chmod +x nfs-manager.sh

# 查看状态
./nfs-manager.sh status

# 在WSL端管理服务器
./nfs-manager.sh server start    # 启动服务器
./nfs-manager.sh server stop     # 停止服务器
./nfs-manager.sh server status   # 查看状态

# 在RK3588端管理客户端
./nfs-manager.sh client mount    # 挂载所有共享
./nfs-manager.sh client umount   # 卸载所有共享
./nfs-manager.sh client status   # 查看状态

# 挂载特定目录
./nfs-manager.sh mount data      # 挂载数据目录
./nfs-manager.sh mount logs      # 挂载日志目录
./nfs-manager.sh mount config    # 挂载配置目录

# 测试连接
./nfs-manager.sh test WSL_SERVER_IP
```

## 目录结构

安装完成后的目录结构：

```
WSL端 (/mine/):
├── data/          # 数据目录 (读写)
├── logs/          # 日志目录 (只读)
├── config/        # 配置目录 (读写)
├── backup/        # 备份目录 (只读)
└── test.txt       # 测试文件

RK3588端 (/mine/):
├── data/          # 挂载点
├── logs/          # 挂载点
├── config/        # 挂载点
└── backup/        # 挂载点
```

## 自动挂载

如果启用了自动挂载，访问以下目录会自动挂载对应的NFS共享：

```bash
# 自动挂载示例
ls /mine/data    # 自动挂载数据目录
ls /mine/logs    # 自动挂载日志目录
ls /mine/config  # 自动挂载配置目录
```

## 常用命令

### 服务管理
```bash
# WSL端
sudo systemctl start nfs-kernel-server    # 启动NFS服务器
sudo systemctl stop nfs-kernel-server     # 停止NFS服务器
sudo systemctl status nfs-kernel-server   # 查看服务状态
sudo exportfs -ra                         # 重新导出共享

# RK3588端
sudo systemctl start rpcbind              # 启动RPC服务
sudo systemctl start autofs               # 启动自动挂载
```

### 查看信息
```bash
# 查看导出列表
sudo exportfs -v                          # 在WSL端
showmount -e WSL_SERVER_IP                # 在RK3588端

# 查看挂载状态
df -h | grep nfs                          # 查看已挂载的NFS
mount | grep nfs                          # 查看挂载详情
```

### 手动挂载/卸载
```bash
# 挂载
sudo mount -t nfs4 WSL_SERVER_IP:/mine/data /mine/data

# 卸载
sudo umount /mine/data
sudo umount -a -t nfs4                    # 卸载所有NFS
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查防火墙
   sudo ufw status
   sudo ufw allow 2049/tcp
   
   # 检查服务状态
   sudo systemctl status nfs-kernel-server
   ```

2. **权限被拒绝**
   ```bash
   # 检查目录权限
   ls -ld /mine
   sudo chown -R nobody:nogroup /mine
   ```

3. **挂载失败**
   ```bash
   # 检查RPC服务
   sudo systemctl status rpcbind
   rpcinfo -p WSL_SERVER_IP
   ```

4. **自动挂载不工作**
   ```bash
   # 重启autofs服务
   sudo systemctl restart autofs
   
   # 检查配置
   cat /etc/auto.master
   cat /etc/auto.nfs
   ```

### 日志查看
```bash
# 系统日志
sudo journalctl -u nfs-kernel-server      # WSL端
sudo journalctl -u autofs                 # RK3588端

# NFS统计
nfsstat -s                                # 服务器统计
nfsstat -c                                # 客户端统计
```

## 性能优化

### 挂载选项优化
```bash
# 高性能挂载选项
sudo mount -t nfs4 -o rsize=32768,wsize=32768,hard,intr,timeo=14,retrans=2 \
    WSL_SERVER_IP:/mine /mine
```

### 网络优化
```bash
# 调整网络缓冲区
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 安全配置

### 限制客户端访问
编辑 `/etc/exports` 文件：
```bash
# 只允许特定IP访问
/mine *************(rw,sync,no_subtree_check)

# 只允许特定网段访问
/mine ***********/24(rw,sync,no_subtree_check)
```

### 启用Kerberos认证 (可选)
```bash
# 安装Kerberos支持
sudo apt install krb5-user nfs-gss-proxy

# 配置Kerberos (需要额外配置)
```

## 备份和恢复

### 配置备份
```bash
# 备份重要配置文件
sudo cp /etc/exports /etc/exports.backup
sudo cp /etc/auto.master /etc/auto.master.backup
sudo cp /etc/auto.nfs /etc/auto.nfs.backup
```

### 数据同步
```bash
# 使用rsync同步数据
rsync -av /local/data/ /mine/data/
```

## 监控和维护

### 定期检查
```bash
# 创建监控脚本
cat > /usr/local/bin/nfs-health-check.sh << 'EOF'
#!/bin/bash
if ! systemctl is-active --quiet nfs-kernel-server; then
    echo "NFS服务器异常" | mail -s "NFS Alert" <EMAIL>
fi
EOF

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/nfs-health-check.sh" | crontab -
```

## 更多信息

- 详细配置: 参考 `README.md`
- 脚本说明: 查看各脚本的 `--help` 选项
- 故障排除: 运行 `test-nfs.sh` 进行诊断

## 支持

如果遇到问题，请：
1. 运行测试脚本: `./test-nfs.sh WSL_SERVER_IP`
2. 查看系统日志: `sudo journalctl -u nfs-kernel-server`
3. 检查网络连接: `ping WSL_SERVER_IP`
