#!/bin/bash

# RK3588 NFS客户端自动安装脚本
# 用途: 在RK3588设备上自动配置NFS客户端
# 作者: Auto-generated
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
WSL_SERVER_IP=""
MOUNT_BASE="/mine"
AUTO_MOUNT=true
AUTO_MOUNT_AVAILABLE=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_help() {
    echo "用法: $0 [选项] <WSL_SERVER_IP>"
    echo
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -m, --mount-point   指定挂载基础目录 (默认: /mine)"
    echo "  --no-auto           禁用自动挂载配置"
    echo "  --test-only         仅测试连接，不进行安装"
    echo
    echo "示例:"
    echo "  $0 *************"
    echo "  $0 -m /mine *************"
    echo "  $0 --test-only *************"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -m|--mount-point)
                MOUNT_BASE="$2"
                shift 2
                ;;
            --no-auto)
                AUTO_MOUNT=false
                shift
                ;;
            --test-only)
                TEST_ONLY=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$WSL_SERVER_IP" ]]; then
                    WSL_SERVER_IP="$1"
                else
                    log_error "多余的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [[ -z "$WSL_SERVER_IP" ]]; then
        log_error "请指定WSL服务器IP地址"
        show_help
        exit 1
    fi
}

# 检查网络连接
check_network() {
    log_info "检查与WSL服务器的网络连接..."
    
    if ! ping -c 3 "$WSL_SERVER_IP" >/dev/null 2>&1; then
        log_error "无法连接到WSL服务器 $WSL_SERVER_IP"
        exit 1
    fi
    
    log_success "网络连接正常"
}

# 检查NFS服务器
check_nfs_server() {
    log_info "检查NFS服务器状态..."
    
    # 检查RPC服务
    if ! rpcinfo -p "$WSL_SERVER_IP" | grep -q nfs >/dev/null 2>&1; then
        log_error "WSL服务器上的NFS服务未运行"
        log_info "请在WSL服务器上运行: sudo systemctl start nfs-kernel-server"
        exit 1
    fi
    
    # 检查可用的导出
    if ! showmount -e "$WSL_SERVER_IP" >/dev/null 2>&1; then
        log_error "无法获取NFS导出列表"
        exit 1
    fi
    
    log_success "NFS服务器状态正常"
    
    # 显示可用的导出
    log_info "可用的NFS导出:"
    showmount -e "$WSL_SERVER_IP"
}

# 测试连接
test_connection() {
    log_info "测试NFS连接..."
    
    check_network
    check_nfs_server
    
    # 创建临时挂载点
    local temp_mount="/tmp/nfs_test_$$"
    mkdir -p "$temp_mount"
    
    # 尝试挂载
    if sudo mount -t nfs4 "${WSL_SERVER_IP}:/mine" "$temp_mount"; then
        log_success "NFS挂载测试成功"
        
        # 列出内容
        log_info "共享目录内容:"
        ls -la "$temp_mount"
        
        # 卸载
        sudo umount "$temp_mount"
        rmdir "$temp_mount"
    else
        log_error "NFS挂载测试失败"
        rmdir "$temp_mount"
        exit 1
    fi
}

# 安装NFS客户端
install_nfs_client() {
    log_info "安装NFS客户端..."

    # 清理并更新包列表
    sudo apt clean
    if ! sudo apt update; then
        log_error "包索引更新失败"
        return 1
    fi

    # 定义包列表
    local core_packages="nfs-common rpcbind"
    local optional_packages="autofs cifs-utils"

    # 安装核心NFS客户端包
    log_info "安装核心NFS客户端包..."
    for package in $core_packages; do
        log_info "安装 $package..."
        if sudo apt install -y "$package"; then
            log_success "$package 安装成功"
        else
            log_error "$package 安装失败，这是必需的包"
            return 1
        fi
    done

    # 安装可选包
    log_info "安装可选工具包..."
    for package in $optional_packages; do
        log_info "尝试安装 $package..."
        if sudo apt install -y "$package"; then
            log_success "$package 安装成功"
            if [[ "$package" == "autofs" ]]; then
                AUTO_MOUNT_AVAILABLE=true
            fi
        else
            log_warning "$package 安装失败，跳过（非必需）"
            if [[ "$package" == "autofs" ]]; then
                AUTO_MOUNT_AVAILABLE=false
                log_warning "自动挂载功能将不可用"
            fi
        fi
    done

    # 验证核心工具
    local required_commands="mount.nfs4 rpcinfo showmount"
    local missing_commands=()

    for cmd in $required_commands; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        log_error "请检查包安装是否成功"
        return 1
    fi

    log_success "NFS客户端安装完成"
}

# 创建挂载点
create_mount_points() {
    log_info "创建挂载点..."
    
    # 创建基础挂载目录
    sudo mkdir -p "$MOUNT_BASE"
    
    # 设置权限
    sudo chmod 755 "$MOUNT_BASE"
    
    log_success "挂载点创建完成: $MOUNT_BASE"
}

# 配置手动挂载
configure_manual_mount() {
    log_info "配置手动挂载..."
    
    # 创建挂载脚本
    cat > /tmp/mount-nfs.sh << EOF
#!/bin/bash
# NFS手动挂载脚本

WSL_SERVER="$WSL_SERVER_IP"
MOUNT_BASE="$MOUNT_BASE"

# 直接挂载整个/mine目录
sudo mount -t nfs4 \${WSL_SERVER}:/mine \${MOUNT_BASE}

echo "NFS挂载完成"
df -h | grep nfs
EOF
    
    # 创建卸载脚本
    cat > /tmp/umount-nfs.sh << EOF
#!/bin/bash
# NFS卸载脚本

MOUNT_BASE="$MOUNT_BASE"

# 卸载NFS挂载
sudo umount \${MOUNT_BASE}

echo "NFS卸载完成"
EOF
    
    # 安装脚本
    sudo cp /tmp/mount-nfs.sh /usr/local/bin/
    sudo cp /tmp/umount-nfs.sh /usr/local/bin/
    sudo chmod +x /usr/local/bin/mount-nfs.sh
    sudo chmod +x /usr/local/bin/umount-nfs.sh
    
    log_success "手动挂载脚本创建完成"
}

# 配置自动挂载
configure_auto_mount() {
    if [[ "$AUTO_MOUNT" != "true" ]]; then
        log_info "跳过自动挂载配置"
        return
    fi

    if [[ "$AUTO_MOUNT_AVAILABLE" != "true" ]]; then
        log_warning "autofs未安装，跳过自动挂载配置"
        return
    fi

    log_info "配置自动挂载..."

    # 备份原配置
    if [[ -f /etc/auto.master ]]; then
        sudo cp /etc/auto.master /etc/auto.master.backup.$(date +%Y%m%d_%H%M%S)
    fi

    # 检查配置是否已存在
    if grep -q "$MOUNT_BASE" /etc/auto.master 2>/dev/null; then
        log_info "自动挂载配置已存在，更新配置..."
        sudo sed -i "\|$MOUNT_BASE|d" /etc/auto.master
    fi

    # 配置auto.master
    echo "$MOUNT_BASE /etc/auto.nfs --timeout=60" | sudo tee -a /etc/auto.master

    # 配置直接挂载整个/mine目录
    sudo tee /etc/auto.nfs << EOF
# NFS自动挂载配置 - 直接挂载整个/mine目录
# 格式: 挂载点 选项 服务器:路径

* -fstype=nfs4,rw,soft,intr,timeo=14,retrans=2 $WSL_SERVER_IP:/mine
EOF

    # 启动autofs服务
    log_info "启动autofs服务..."
    if sudo systemctl enable autofs && sudo systemctl restart autofs; then
        log_success "autofs服务启动成功"
    else
        log_error "autofs服务启动失败"
        log_info "查看错误日志: sudo journalctl -u autofs -n 10"
        return 1
    fi

    log_success "自动挂载配置完成"
}

# 配置fstab挂载 (可选)
configure_fstab_mount() {
    log_info "配置fstab永久挂载 (可选)..."
    
    # 备份fstab
    sudo cp /etc/fstab /etc/fstab.backup.$(date +%Y%m%d_%H%M%S)
    
    # 添加NFS挂载项 (启用开机自动挂载)
    cat << EOF | sudo tee -a /etc/fstab

# NFS挂载配置 - 开机自动挂载
$WSL_SERVER_IP:/mine $MOUNT_BASE nfs4 defaults,soft,intr,rsize=8192,wsize=8192,_netdev 0 0
EOF
    
    log_success "fstab开机自动挂载配置已添加"
}

# 验证安装
verify_installation() {
    log_info "验证NFS客户端安装..."

    local verification_failed=false

    # 检查RPC服务状态
    if systemctl is-active --quiet rpcbind; then
        log_success "rpcbind服务运行正常"
    else
        log_error "rpcbind服务未运行"
        log_info "尝试启动: sudo systemctl start rpcbind"
        verification_failed=true
    fi

    # 检查autofs服务状态
    if [[ "$AUTO_MOUNT_AVAILABLE" == "true" ]]; then
        if systemctl is-active --quiet autofs; then
            log_success "autofs服务运行正常"
        else
            log_warning "autofs服务未运行"
            log_info "尝试启动: sudo systemctl start autofs"
        fi
    else
        log_info "autofs未安装，自动挂载功能不可用"
    fi

    # 检查挂载点
    if [[ -d "$MOUNT_BASE" ]]; then
        log_success "挂载点目录存在: $MOUNT_BASE"
    else
        log_error "挂载点目录不存在: $MOUNT_BASE"
        verification_failed=true
    fi

    # 检查必需命令
    local required_commands="mount.nfs4 rpcinfo showmount"
    local missing_commands=()

    for cmd in $required_commands; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        verification_failed=true
    else
        log_success "所有必需命令都可用"
    fi

    if [[ "$verification_failed" == "true" ]]; then
        log_error "验证失败，请检查上述错误"
        return 1
    fi

    # 测试连接
    test_connection

    log_success "NFS客户端验证完成"
}

# 显示使用说明
show_usage_info() {
    echo
    log_info "安装完成! 使用说明:"
    echo "----------------------------------------"
    echo "1. 手动挂载:"
    echo "   mount-nfs.sh                    # 挂载NFS"
    echo "   umount-nfs.sh                   # 卸载NFS"
    echo
    echo "2. 自动挂载 (如果启用):"
    echo "   访问 $MOUNT_BASE 自动挂载整个目录"
    echo
    echo "3. 管理命令:"
    echo "   showmount -e $WSL_SERVER_IP     # 查看可用导出"
    echo "   df -h | grep nfs                # 查看已挂载的NFS"
    echo "   sudo systemctl status autofs    # 查看autofs状态"
    echo
    echo "4. 故障排除:"
    echo "   sudo systemctl restart autofs   # 重启autofs"
    echo "   sudo exportfs -ra               # 在服务器端重新导出"
    echo "----------------------------------------"
}

# 主函数
main() {
    echo "========================================"
    echo "    RK3588 NFS客户端自动安装脚本"
    echo "========================================"
    
    parse_args "$@"
    
    if [[ "$TEST_ONLY" == "true" ]]; then
        test_connection
        exit 0
    fi
    
    check_network
    install_nfs_client
    check_nfs_server
    create_mount_points
    configure_manual_mount
    configure_auto_mount
    configure_fstab_mount
    verify_installation
    show_usage_info
    
    log_success "NFS客户端安装完成!"
}

# 执行主函数
main "$@"
