# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Build

on:
  push:
    branches: ["master", "main"]
  pull_request:
    branches: ["master", "main"]

permissions:
  contents: read
  pull-requests: read

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }}-${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.x"
      - run: pip install flake8

      - name: check Python syntax errors or undefined names
        run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      - name: check complexity and length # the GitHub editor is 127 chars wide
        run: flake8 . --count --max-complexity=12 --max-line-length=127 --statistics

  pypi:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.x"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py
      - name: Build package
        run: python -m build --sdist --wheel --outdir dist/

      - uses: actions/upload-artifact@v3
        with:
          name: pypi
          path: dist/
          retention-days: 5

  pyinstaller:
    strategy:
      # fail-fast: false
      matrix:
        os: [windows, macos, ubuntu]
        python-version: ["3.x"]
    runs-on: ${{ matrix.os }}-latest
    timeout-minutes: 8
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: pip install pyinstaller

      # Prepare build version and cert
      - name: Replace build version
        run: sed -i.tmp -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py && rm run.py.tmp
        shell: bash
      - name: Copy cert on ubuntu
        if: runner.os == 'Linux'
        run: cp /etc/ssl/certs/ca-certificates.crt cert.pem && export SSL_CERT_FILE=${PWD}/cert.pem

      - run: python ./run.py -h

      - name: Package binary
        run: python -O -m PyInstaller --noconfirm --clean .build/ddns.spec

      - run: ./dist/ddns || test -e config.json
      - run: ./dist/ddns -h

      # Upload build result
      - uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.os }}-py${{ matrix.python-version }}
          path: dist/
          retention-days: 7

  docker:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v3
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py
      - uses: docker/build-push-action@v3
        with:
          context: .
          load: true
          tags: ddns:test
      - name: test help command
        run: docker run --rm ddns:test -h
      - name: test config generation
        run: docker run --rm -v "$(pwd)":/config/ ddns:test -c /config/config.json || test -e config.json

  preview-pypi:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    needs: [lint, pypi]
    timeout-minutes: 3
    environment:
      name: preview
      url: https://test.pypi.org/project/ddns/
    steps:
      - uses: actions/download-artifact@v4.1.7
        with:
          name: pypi
          path: dist
      - uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
          repository_url: https://test.pypi.org/legacy/
          print_hash: true

  preview-docker:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    timeout-minutes: 5
    needs: [docker]
    environment:
      name: preview
      url: https://github.com/NewFuture/DDNS/pkgs/container/ddns/?tag=master
    permissions:
      packages: write
    steps:
      - uses: actions/checkout@v3
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py
      - uses: docker/setup-qemu-action@v2
      - uses: docker/setup-buildx-action@v2
      - uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: docker/metadata-action@v4
        id: meta
        with:
          images: |
            ghcr.io/newfuture/ddns
            newfuture/ddns
      - uses: docker/build-push-action@v3
        with:
          context: .
          platforms: linux/amd64,linux/arm,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
