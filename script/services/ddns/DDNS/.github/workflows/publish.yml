name: Publish

on:
  push:
    tags: [v*]

permissions:
  contents: read

jobs:
  publish-docker:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    environment:
      name: publish
      url: https://hub.docker.com/r/newfuture/ddns
    permissions:
      packages: write
    steps:
      - uses: actions/checkout@v3
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py
      - uses: docker/setup-qemu-action@v2
      - uses: docker/setup-buildx-action@v2
      - uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: docker/metadata-action@v4
        id: meta
        with:
          images: |
            ghcr.io/newfuture/ddns
            newfuture/ddns
      - uses: docker/build-push-action@v3
        with:
          context: .
          platforms: linux/amd64,linux/arm,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  publish-pypi:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    environment:
      name: publish
      url: https://pypi.org/project/ddns/
    steps:
      - uses: actions/checkout@v3
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py
      - uses: actions/setup-python@v4
        with:
          python-version: "3.x"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build
      - name: Build package
        run: python -m build --sdist --wheel --outdir dist/
      - uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}
          print_hash: true

  build-binary:
    strategy:
      matrix:
        os: [macos, ubuntu, windows]
    runs-on: ${{ matrix.os }}-latest
    timeout-minutes: 8
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.x"
      - name: Install dependencies
        run: pip install pyinstaller

      - name: Replace build version
        run: sed -i.tmp -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" -e "s/\${BUILD_DATE}/$(date --iso-8601=seconds)/" run.py && rm run.py.tmp
        shell: bash
      - name: Copy cert on ubuntu
        if: runner.os == 'Linux'
        run: cp /etc/ssl/certs/ca-certificates.crt cert.pem && export SSL_CERT_FILE=${PWD}/cert.pem

      - run: python -O -m PyInstaller --noconfirm --clean .build/ddns.spec
      - run: ./dist/ddns || test -e config.json
      - run: ./dist/ddns -h

      - run: mv ./dist/ddns ./dist/ddns-osx
        if: runner.os == 'macOS'
      - uses: actions/upload-artifact@v3
        with:
          name: ${{ runner.os }}
          path: dist/
          retention-days: 10

  github-release:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    environment:
      name: publish
      url: https://github.com/NewFuture/DDNS/releases/tag/${{ github.ref_name }}
    permissions:
      contents: write
    needs: [publish-docker, publish-pypi, build-binary]
    steps:
      - uses: actions/checkout@v3
      - run: sed -i -e "s#\${BUILD_VERSION}#${{ github.ref_name }}#" .release/README.md
      - uses: actions/download-artifact@v4.1.7
        with:
          path: dist
      - uses: softprops/action-gh-release@master
        with:
          draft: false
          prerelease: false
          generate_release_notes: true
          body_path: .release/README.md
          fail_on_unmatched_files: true
          append_body: true
          files: |
            .release/*.sh
            .release/*.bat
            dist/*/*
