#!/bin/bash

# =============================================================================
# 本地仓库同步核心引擎
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sync-config.json"
LOG_DIR="$SCRIPT_DIR/logs"
BACKUP_DIR="$SCRIPT_DIR/backups"
TEMP_DIR="$SCRIPT_DIR/tmp"

# 创建必要目录
mkdir -p "$LOG_DIR" "$BACKUP_DIR" "$TEMP_DIR"

# 日志文件
LOG_FILE="$LOG_DIR/sync-$(date +%Y%m%d).log"

# =============================================================================
# 工具函数
# =============================================================================

# 时间戳函数
timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local color="$3"
    
    local log_entry="[$(timestamp)] [$level] $message"
    
    # 写入日志文件
    echo "$log_entry" >> "$LOG_FILE"
    
    # 控制台输出
    if [ -n "$color" ]; then
        echo -e "${color}$log_entry${NC}"
    else
        echo "$log_entry"
    fi
}

log_info() {
    log "INFO" "$1" "$BLUE"
}

log_success() {
    log "SUCCESS" "$1" "$GREEN"
}

log_warning() {
    log "WARNING" "$1" "$YELLOW"
}

log_error() {
    log "ERROR" "$1" "$RED"
}

log_debug() {
    if [ "$DEBUG" = "true" ]; then
        log "DEBUG" "$1" "$PURPLE"
    fi
}

# JSON解析函数
get_config_value() {
    local key="$1"
    local default_value="$2"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "$default_value"
        return
    fi
    
    local value
    value=$(jq -r "$key // \"$default_value\"" "$CONFIG_FILE" 2>/dev/null)
    
    if [ "$value" = "null" ] || [ -z "$value" ]; then
        echo "$default_value"
    else
        echo "$value"
    fi
}

# 获取仓库配置
get_repo_config() {
    local repo_id="$1"
    local field="$2"
    
    jq -r ".repositories[] | select(.id == \"$repo_id\") | .$field // empty" "$CONFIG_FILE" 2>/dev/null
}

# 检查仓库是否启用
is_repo_enabled() {
    local repo_id="$1"
    local enabled
    enabled=$(get_repo_config "$repo_id" "enabled")
    [ "$enabled" = "true" ]
}

# =============================================================================
# Git操作函数
# =============================================================================

# 检查是否为Git仓库
is_git_repo() {
    local path="$1"
    [ -d "$path/.git" ] || git -C "$path" rev-parse --git-dir >/dev/null 2>&1
}

# 获取Git状态
get_git_status() {
    local path="$1"
    
    if ! is_git_repo "$path"; then
        echo "not_git_repo"
        return 1
    fi
    
    cd "$path" || return 1
    
    local status=""
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        status="${status}dirty,"
    fi
    
    # 检查是否有未推送的提交
    local unpushed
    unpushed=$(git log --oneline origin/$(git symbolic-ref --short HEAD)..HEAD 2>/dev/null | wc -l)
    if [ "$unpushed" -gt 0 ]; then
        status="${status}unpushed,"
    fi
    
    # 检查是否有未拉取的提交
    git fetch --dry-run 2>/dev/null
    local unpulled
    unpulled=$(git log --oneline HEAD..origin/$(git symbolic-ref --short HEAD) 2>/dev/null | wc -l)
    if [ "$unpulled" -gt 0 ]; then
        status="${status}behind,"
    fi
    
    # 移除末尾的逗号
    status=${status%,}
    
    if [ -z "$status" ]; then
        echo "clean"
    else
        echo "$status"
    fi
}

# 获取当前分支
get_current_branch() {
    local path="$1"
    git -C "$path" symbolic-ref --short HEAD 2>/dev/null || git -C "$path" rev-parse --short HEAD 2>/dev/null
}

# 获取最新提交哈希
get_latest_commit() {
    local path="$1"
    git -C "$path" rev-parse HEAD 2>/dev/null
}

# 执行Git stash
git_stash_save() {
    local path="$1"
    local message="$2"
    
    cd "$path" || return 1
    
    if [ -z "$(git status --porcelain)" ]; then
        log_debug "没有需要stash的更改: $path"
        return 0
    fi
    
    log_info "保存未提交更改: $path"
    git stash push -m "$message" || return 1
    
    return 0
}

# 恢复Git stash
git_stash_pop() {
    local path="$1"
    
    cd "$path" || return 1
    
    if ! git stash list | grep -q "stash@{0}"; then
        log_debug "没有stash需要恢复: $path"
        return 0
    fi
    
    log_info "恢复未提交更改: $path"
    git stash pop || return 1
    
    return 0
}

# 拉取远程更新
git_pull() {
    local path="$1"
    local remote="${2:-origin}"
    local branch="$3"
    
    cd "$path" || return 1
    
    if [ -z "$branch" ]; then
        branch=$(get_current_branch "$path")
    fi
    
    log_info "拉取远程更新: $remote/$branch -> $path"
    
    # 先fetch
    git fetch "$remote" || return 1
    
    # 检查是否需要pull
    local local_commit
    local_commit=$(git rev-parse HEAD)
    local remote_commit
    remote_commit=$(git rev-parse "$remote/$branch" 2>/dev/null)
    
    if [ "$local_commit" = "$remote_commit" ]; then
        log_info "已是最新版本，无需拉取"
        return 0
    fi
    
    # 执行pull
    git pull "$remote" "$branch" || return 1
    
    log_success "远程更新拉取完成"
    return 0
}

# 推送到远程
git_push() {
    local path="$1"
    local remote="${2:-origin}"
    local branch="$3"
    
    cd "$path" || return 1
    
    if [ -z "$branch" ]; then
        branch=$(get_current_branch "$path")
    fi
    
    log_info "推送到远程: $path -> $remote/$branch"
    
    git push "$remote" "$branch" || return 1
    
    log_success "推送完成"
    return 0
}

# =============================================================================
# 文件同步函数
# =============================================================================

# 生成排除模式参数
generate_exclude_patterns() {
    local repo_id="$1"
    local exclude_file="$TEMP_DIR/exclude_${repo_id}.txt"
    
    # 清空文件
    > "$exclude_file"
    
    # 从配置中读取排除模式
    jq -r ".repositories[] | select(.id == \"$repo_id\") | .exclude_patterns[]? // empty" "$CONFIG_FILE" >> "$exclude_file" 2>/dev/null
    
    # 如果文件不为空，返回rsync参数
    if [ -s "$exclude_file" ]; then
        echo "--exclude-from=$exclude_file"
    fi
}

# 生成包含模式参数
generate_include_patterns() {
    local repo_id="$1"
    local include_file="$TEMP_DIR/include_${repo_id}.txt"
    
    # 清空文件
    > "$include_file"
    
    # 从配置中读取包含模式
    jq -r ".repositories[] | select(.id == \"$repo_id\") | .include_patterns[]? // empty" "$CONFIG_FILE" >> "$include_file" 2>/dev/null
    
    # 如果文件不为空，返回rsync参数
    if [ -s "$include_file" ]; then
        echo "--include-from=$include_file"
    fi
}

# 计算文件校验和
calculate_checksum() {
    local file="$1"
    local method="${2:-md5}"
    
    case "$method" in
        md5)
            md5sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        sha1)
            sha1sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        sha256)
            sha256sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        *)
            log_error "不支持的校验和方法: $method"
            return 1
            ;;
    esac
}

# 比较文件
compare_files() {
    local file1="$1"
    local file2="$2"
    local method="${3:-md5}"
    
    if [ ! -f "$file1" ] || [ ! -f "$file2" ]; then
        return 1
    fi
    
    local checksum1
    local checksum2
    
    checksum1=$(calculate_checksum "$file1" "$method")
    checksum2=$(calculate_checksum "$file2" "$method")
    
    [ "$checksum1" = "$checksum2" ]
}

# 创建备份
create_backup() {
    local source="$1"
    local backup_name="$2"
    local backup_path="$BACKUP_DIR/$backup_name-$(date +%Y%m%d_%H%M%S)"
    
    log_info "创建备份: $source -> $backup_path"
    
    if [ -d "$source" ]; then
        cp -r "$source" "$backup_path" || return 1
    else
        cp "$source" "$backup_path" || return 1
    fi
    
    log_success "备份创建完成: $backup_path"
    echo "$backup_path"
}

# 文件同步核心函数
sync_files() {
    local repo_id="$1"
    local source="$2"
    local target="$3"
    local strategy="$4"
    
    log_info "开始文件同步: $repo_id ($strategy)"
    log_info "源: $source"
    log_info "目标: $target"
    
    # 检查源目录是否存在
    if [ ! -d "$source" ]; then
        log_error "源目录不存在: $source"
        return 1
    fi
    
    # 创建目标目录
    mkdir -p "$target" || return 1
    
    # 获取同步设置
    local preserve_perms
    local preserve_times
    local follow_symlinks
    local compress
    
    preserve_perms=$(get_repo_config "$repo_id" "sync_settings.preserve_permissions")
    preserve_times=$(get_repo_config "$repo_id" "sync_settings.preserve_timestamps")
    follow_symlinks=$(get_repo_config "$repo_id" "sync_settings.follow_symlinks")
    compress=$(get_repo_config "$repo_id" "sync_settings.compress_transfer")
    
    # 构建rsync选项
    local rsync_opts="-av"
    
    [ "$preserve_perms" = "true" ] && rsync_opts="${rsync_opts}p"
    [ "$preserve_times" = "true" ] && rsync_opts="${rsync_opts}t"
    [ "$follow_symlinks" = "true" ] && rsync_opts="${rsync_opts}L"
    [ "$compress" = "true" ] && rsync_opts="${rsync_opts}z"
    
    # 添加进度显示
    rsync_opts="${rsync_opts} --progress"
    
    # 生成排除和包含模式
    local exclude_args
    local include_args
    exclude_args=$(generate_exclude_patterns "$repo_id")
    include_args=$(generate_include_patterns "$repo_id")
    
    # 根据策略执行同步
    case "$strategy" in
        "unidirectional")
            log_info "执行单向同步: $source -> $target"
            eval "rsync $rsync_opts $include_args $exclude_args \"$source/\" \"$target/\""
            ;;
        "bidirectional")
            log_info "执行双向同步"
            # 先同步 source -> target
            eval "rsync $rsync_opts $include_args $exclude_args \"$source/\" \"$target/\""
            # 再同步 target -> source
            eval "rsync $rsync_opts $include_args $exclude_args \"$target/\" \"$source/\""
            ;;
        "mirror")
            log_info "执行镜像同步: $source -> $target"
            rsync_opts="${rsync_opts} --delete"
            eval "rsync $rsync_opts $include_args $exclude_args \"$source/\" \"$target/\""
            ;;
        *)
            log_error "不支持的同步策略: $strategy"
            return 1
            ;;
    esac
    
    local sync_result=$?
    
    if [ $sync_result -eq 0 ]; then
        log_success "文件同步完成"
    else
        log_error "文件同步失败 (退出码: $sync_result)"
        return 1
    fi
    
    return 0
}

# =============================================================================
# 冲突检测和处理
# =============================================================================

# 检测冲突
detect_conflicts() {
    local source="$1"
    local target="$2"
    local conflict_list="$TEMP_DIR/conflicts_$(date +%s).txt"
    
    log_info "检测同步冲突..."
    
    # 清空冲突列表
    > "$conflict_list"
    
    # 使用rsync的dry-run模式检测差异
    rsync -avun --itemize-changes "$source/" "$target/" | while read -r line; do
        # 解析rsync输出
        local change_type="${line:0:1}"
        local file_path="${line:11}"
        
        case "$change_type" in
            "c")
                echo "conflict:$file_path" >> "$conflict_list"
                ;;
            ">")
                echo "newer_source:$file_path" >> "$conflict_list"
                ;;
            "<")
                echo "newer_target:$file_path" >> "$conflict_list"
                ;;
        esac
    done
    
    if [ -s "$conflict_list" ]; then
        log_warning "检测到潜在冲突:"
        cat "$conflict_list"
        echo "$conflict_list"
        return 1
    else
        log_info "未检测到冲突"
        rm -f "$conflict_list"
        return 0
    fi
}

# 解决冲突
resolve_conflicts() {
    local conflict_file="$1"
    local strategy="$2"
    local source="$3"
    local target="$4"
    
    log_info "使用策略 '$strategy' 解决冲突"
    
    while IFS=':' read -r conflict_type file_path; do
        local source_file="$source/$file_path"
        local target_file="$target/$file_path"
        
        case "$strategy" in
            "source-wins")
                log_info "应用源文件: $file_path"
                cp "$source_file" "$target_file" 2>/dev/null || true
                ;;
            "target-wins")
                log_info "保留目标文件: $file_path"
                cp "$target_file" "$source_file" 2>/dev/null || true
                ;;
            "manual")
                log_warning "需要手动解决冲突: $file_path"
                echo "源文件: $source_file"
                echo "目标文件: $target_file"
                return 1
                ;;
            "keep-both")
                log_info "保留两个版本: $file_path"
                local timestamp
                timestamp=$(date +%Y%m%d_%H%M%S)
                cp "$source_file" "${target_file}.source.${timestamp}" 2>/dev/null || true
                cp "$target_file" "${target_file}.target.${timestamp}" 2>/dev/null || true
                ;;
            *)
                log_error "不支持的冲突解决策略: $strategy"
                return 1
                ;;
        esac
    done < "$conflict_file"
    
    rm -f "$conflict_file"
    log_success "冲突解决完成"
    return 0
}

# =============================================================================
# 钩子系统
# =============================================================================

# 执行钩子
execute_hook() {
    local hook_type="$1"
    local repo_id="$2"
    local hook_script
    
    hook_script=$(get_repo_config "$repo_id" "hooks.$hook_type")
    
    if [ -n "$hook_script" ] && [ -f "$hook_script" ]; then
        log_info "执行 $hook_type 钩子: $hook_script"
        
        # 设置环境变量
        export SYNC_REPO_ID="$repo_id"
        export SYNC_SOURCE=$(get_repo_config "$repo_id" "source")
        export SYNC_TARGET=$(get_repo_config "$repo_id" "target")
        export SYNC_STRATEGY=$(get_repo_config "$repo_id" "strategy")
        
        # 执行钩子脚本
        bash "$hook_script"
        local result=$?
        
        if [ $result -eq 0 ]; then
            log_success "$hook_type 钩子执行成功"
        else
            log_error "$hook_type 钩子执行失败 (退出码: $result)"
            return 1
        fi
    fi
    
    return 0
}

# =============================================================================
# 主同步函数
# =============================================================================

# 同步单个仓库
sync_repository() {
    local repo_id="$1"
    
    log_info "开始同步仓库: $repo_id"
    
    # 检查仓库是否启用
    if ! is_repo_enabled "$repo_id"; then
        log_warning "仓库未启用，跳过同步: $repo_id"
        return 0
    fi
    
    # 获取仓库配置
    local source
    local target
    local strategy
    local conflict_resolution
    local git_integration
    local backup_enabled
    
    source=$(get_repo_config "$repo_id" "source")
    target=$(get_repo_config "$repo_id" "target")
    strategy=$(get_repo_config "$repo_id" "strategy")
    conflict_resolution=$(get_repo_config "$repo_id" "conflict_resolution")
    git_integration=$(get_config_value ".git_integration.enabled" "false")
    backup_enabled=$(get_config_value ".global.backup_enabled" "false")
    
    # 验证配置
    if [ -z "$source" ] || [ -z "$target" ] || [ -z "$strategy" ]; then
        log_error "仓库配置不完整: $repo_id"
        return 1
    fi
    
    # 执行pre-sync钩子
    execute_hook "pre_sync" "$repo_id" || return 1
    
    # 创建备份
    if [ "$backup_enabled" = "true" ]; then
        create_backup "$target" "${repo_id}_target" >/dev/null
    fi
    
    # Git集成 - 同步前操作
    if [ "$git_integration" = "true" ]; then
        local pull_before_sync
        pull_before_sync=$(get_config_value ".git_integration.pull_before_sync" "true")
        
        if [ "$pull_before_sync" = "true" ]; then
            if is_git_repo "$source"; then
                git_stash_save "$source" "Auto stash before sync at $(timestamp)"
                git_pull "$source" || log_warning "Git pull 失败: $source"
                git_stash_pop "$source" || log_warning "Git stash pop 失败: $source"
            fi
            
            if is_git_repo "$target"; then
                git_stash_save "$target" "Auto stash before sync at $(timestamp)"
                git_pull "$target" || log_warning "Git pull 失败: $target"
                git_stash_pop "$target" || log_warning "Git stash pop 失败: $target"
            fi
        fi
    fi
    
    # 检测冲突
    local conflict_file
    if ! conflict_file=$(detect_conflicts "$source" "$target"); then
        log_warning "检测到冲突，尝试解决..."
        
        if ! resolve_conflicts "$conflict_file" "$conflict_resolution" "$source" "$target"; then
            log_error "冲突解决失败"
            execute_hook "on_conflict" "$repo_id"
            return 1
        fi
    fi
    
    # 执行文件同步
    if ! sync_files "$repo_id" "$source" "$target" "$strategy"; then
        log_error "文件同步失败"
        execute_hook "on_error" "$repo_id"
        return 1
    fi
    
    # Git集成 - 同步后操作
    if [ "$git_integration" = "true" ]; then
        local auto_commit
        local push_after_sync
        
        auto_commit=$(get_config_value ".git_integration.auto_commit" "false")
        push_after_sync=$(get_config_value ".git_integration.push_after_sync" "false")
        
        if [ "$auto_commit" = "true" ]; then
            local commit_template
            commit_template=$(get_config_value ".git_integration.commit_message_template" "Auto sync: {timestamp}")
            local commit_message="${commit_template//\{timestamp\}/$(timestamp)}"
            
            if is_git_repo "$target"; then
                cd "$target" || return 1
                git add . 2>/dev/null || true
                git commit -m "$commit_message" 2>/dev/null || true
                
                if [ "$push_after_sync" = "true" ]; then
                    git_push "$target" || log_warning "Git push 失败: $target"
                fi
            fi
        fi
    fi
    
    # 执行post-sync钩子
    execute_hook "post_sync" "$repo_id" || log_warning "post-sync钩子执行失败"
    
    log_success "仓库同步完成: $repo_id"
    return 0
}

# 同步所有仓库
sync_all_repositories() {
    log_info "开始同步所有启用的仓库"
    
    local repo_ids
    repo_ids=$(jq -r '.repositories[] | select(.enabled == true) | .id' "$CONFIG_FILE" 2>/dev/null)
    
    if [ -z "$repo_ids" ]; then
        log_warning "没有找到启用的仓库"
        return 0
    fi
    
    local success_count=0
    local total_count=0
    
    while read -r repo_id; do
        if [ -n "$repo_id" ]; then
            total_count=$((total_count + 1))
            
            if sync_repository "$repo_id"; then
                success_count=$((success_count + 1))
            fi
        fi
    done <<< "$repo_ids"
    
    log_info "同步完成: $success_count/$total_count 个仓库成功"
    
    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# 导出函数供外部使用
# =============================================================================

# 如果直接运行此脚本，执行同步所有仓库
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    if [ "$1" = "all" ]; then
        sync_all_repositories
    elif [ -n "$1" ]; then
        sync_repository "$1"
    else
        echo "用法: $0 [repo_id|all]"
        echo "示例: $0 main_backup"
        echo "示例: $0 all"
        exit 1
    fi
fi