#!/bin/bash

PORT=${PORT:-3000}

echo "🛑 停止同步配置界面..."

# 查找并停止node进程
if pgrep -f "node server.js" >/dev/null; then
    echo "📍 发现运行中的服务进程"
    pkill -f "node server.js"
    echo "⏳ 等待进程停止..."
    sleep 2
fi

# 强制清理端口
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "🔧 强制清理端口 $PORT..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    sleep 1
fi

# 验证停止状态
if ! lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "✅ 服务已成功停止"
else
    echo "❌ 服务停止失败，可能需要手动处理"
fi