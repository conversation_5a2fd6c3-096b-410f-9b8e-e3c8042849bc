#!/bin/bash

PORT=${PORT:-3000}

echo "🌐 启动同步配置界面..."

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口 $PORT 已被占用，正在清理..."
    
    # 尝试优雅停止
    pkill -f "node server.js" 2>/dev/null && sleep 2
    
    # 强制清理端口
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    
    sleep 1
    echo "✅ 端口已清理"
fi

echo "🚀 启动服务..."
echo "📍 访问地址: http://localhost:$PORT"
echo "🛑 按 Ctrl+C 停止服务"
echo ""

# 启动服务
PORT=$PORT npm start
