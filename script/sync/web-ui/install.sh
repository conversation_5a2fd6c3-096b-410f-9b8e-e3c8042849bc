#!/bin/bash

# Web UI 安装脚本

echo "🚀 正在安装同步配置界面..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 需要安装 Node.js"
    echo "请访问 https://nodejs.org/ 下载安装"
    exit 1
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ 需要安装 npm"
    exit 1
fi

echo "✅ Node.js 和 npm 已安装"

# 安装依赖
echo "📦 正在安装依赖包..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装完成"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 创建启动脚本
cat > start-ui.sh << 'EOF'
#!/bin/bash
echo "🌐 启动同步配置界面..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务"
echo ""
npm start
EOF

chmod +x start-ui.sh

echo "✅ 安装完成！"
echo ""
echo "使用方法:"
echo "  启动界面: ./start-ui.sh"
echo "  开发模式: npm run dev"
echo "  访问地址: http://localhost:3000"
echo ""