# 同步系统 Web 界面

基于Web的可视化配置界面，提供友好的图形化管理体验。

## 🚀 快速启动

```bash
# 安装依赖
./install.sh

# 启动服务 (自动处理端口冲突)
./start-ui.sh

# 停止服务
./stop-ui.sh

# 访问界面
浏览器访问: http://localhost:3000
```

### 进程管理

- **自动端口检查**: start-ui.sh会自动检查并清理端口占用
- **优雅停止**: 提供专门的停止脚本
- **多实例保护**: 防止多个实例同时运行

## 🎛️ 功能特性

- **可视化配置**: 图形化编辑同步设置
- **📁 文件夹浏览器**: 点击"浏览"按钮选择本地文件夹
- **实时监控**: 状态显示和日志查看
- **一键操作**: 启动监控、执行同步
- **响应式设计**: 支持桌面和移动设备

### 🆕 文件夹选择功能

- **直观浏览**: 可视化浏览本地文件系统
- **快速选择**: 点击文件夹直接选择路径
- **路径编辑**: 支持手动输入或浏览选择
- **安全限制**: 限制在安全的目录范围内

## 📂 文件结构

```
web-ui/
├── install.sh            # 安装脚本
├── start-ui.sh          # 启动脚本 (智能端口管理)
├── stop-ui.sh           # 停止脚本
├── server.js            # Express后端服务
├── package.json         # Node.js项目配置
└── public/              # 前端静态文件
    ├── index.html       # 主界面
    ├── styles.css       # 样式文件
    └── app.js           # 前端逻辑
```

## ⚙️ 技术栈

- **前端**: HTML5 + CSS3 + 原生JavaScript
- **后端**: Node.js + Express.js
- **集成**: 与同步脚本系统完全集成

## 🔧 自定义配置

```bash
# 自定义端口
PORT=8080 npm start

# 指定配置文件路径
CONFIG_FILE=/path/to/config.json npm start
```

## 📖 详细文档

更多详细信息请查看主项目的 [docs](../docs/) 目录。