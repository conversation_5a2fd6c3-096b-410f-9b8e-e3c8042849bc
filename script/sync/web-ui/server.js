const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '..', 'sync-config.json');
const SCRIPT_DIR = path.join(__dirname, '..');

// 加载当前配置
let currentConfig = null;

function loadCurrentConfig() {
    try {
        if (fs.existsSync(CONFIG_FILE)) {
            currentConfig = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
        }
    } catch (error) {
        console.error('加载配置失败:', error);
    }
}

// 初始化时加载配置
loadCurrentConfig();

// API路由
app.get('/api/config', (req, res) => {
    try {
        if (!fs.existsSync(CONFIG_FILE)) {
            return res.status(404).json({ error: '配置文件不存在' });
        }
        
        const config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
        res.json(config);
    } catch (error) {
        res.status(500).json({ error: '读取配置文件失败', details: error.message });
    }
});

app.post('/api/config', (req, res) => {
    try {
        const config = req.body;
        
        // 更新时间戳
        config.metadata.updated_at = new Date().toISOString();
        
        // 备份现有配置
        if (fs.existsSync(CONFIG_FILE)) {
            const backupFile = path.join(SCRIPT_DIR, 'backups', 'sync-config.json.backup.' + Date.now());
            fs.copyFileSync(CONFIG_FILE, backupFile);
        }
        
        // 写入新配置
        fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
        
        // 重新加载配置
        loadCurrentConfig();
        
        res.json({ success: true, message: '配置保存成功' });
    } catch (error) {
        res.status(500).json({ error: '保存配置文件失败', details: error.message });
    }
});

// 获取同步状态
app.get('/api/status', (req, res) => {
    // 重新加载配置以获取最新状态
    loadCurrentConfig();

    // 检查监控进程状态
    const monitorPidFile = path.join(SCRIPT_DIR, 'pids', 'monitor.pid');
    let monitorStatus = 'stopped';

    if (fs.existsSync(monitorPidFile)) {
        try {
            const pid = fs.readFileSync(monitorPidFile, 'utf8').trim();
            try {
                // 使用同步方式检查进程
                require('child_process').execSync(`ps -p ${pid}`, { stdio: 'ignore' });
                monitorStatus = 'running';
            } catch (error) {
                monitorStatus = 'stopped';
            }
        } catch (error) {
            monitorStatus = 'stopped';
        }
    }

    const activeRepos = currentConfig?.repositories?.filter(r => r.enabled).length || 0;
    
    res.json({
        status: 'running',
        monitor_status: monitorStatus,
        last_sync: new Date().toISOString(),
        active_repos: activeRepos,
        config_loaded: currentConfig !== null
    });
});

// 执行同步操作
app.post('/api/sync/:repoId', (req, res) => {
    const { repoId } = req.params;
    
    if (!currentConfig || !currentConfig.repositories) {
        return res.status(400).json({ error: '没有配置信息' });
    }
    
    const repo = currentConfig.repositories.find(r => r.id === repoId);
    if (!repo) {
        return res.status(404).json({ error: '仓库未找到' });
    }
    
    if (!repo.enabled) {
        return res.status(400).json({ error: '仓库未启用' });
    }
    
    // 确保目标目录存在
    const targetDir = path.dirname(repo.target);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // 构建rsync命令
    let rsyncOptions = ['-av', '--progress'];
    
    if (repo.strategy === 'mirror') {
        rsyncOptions.push('--delete');
    }
    
    if (repo.sync_settings?.preserve_permissions) {
        rsyncOptions.push('--perms');
    }
    
    if (repo.sync_settings?.preserve_timestamps) {
        rsyncOptions.push('--times');
    }
    
    // 添加排除模式
    if (repo.exclude_patterns && repo.exclude_patterns.length > 0) {
        repo.exclude_patterns.forEach(pattern => {
            rsyncOptions.push(`--exclude=${pattern}`);
        });
    }
    
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        repoId,
        repoName: repo.name,
        command: `rsync ${rsyncOptions.join(' ')} "${repo.source}/" "${repo.target}/"`,
        status: 'started'
    };
    
    console.log(`[${timestamp}] 开始同步: ${repo.name}`);
    console.log(`[${timestamp}] 执行命令: ${logEntry.command}`);
    
    exec(logEntry.command, (error, stdout, stderr) => {
        const endTime = new Date().toISOString();
        
        if (error) {
            console.error(`[${endTime}] 同步失败: ${repo.name} - ${error.message}`);
            return res.status(500).json({ 
                error: '同步失败', 
                details: stderr, 
                command: logEntry.command,
                timestamp: endTime,
                repository: repo.id
            });
        }
        
        console.log(`[${endTime}] 同步成功: ${repo.name}`);
        res.json({ 
            success: true, 
            output: stdout,
            command: logEntry.command,
            repository: repo.id,
            timestamp: endTime,
            log: {
                started: timestamp,
                completed: endTime,
                duration: new Date(endTime) - new Date(timestamp)
            }
        });
    });
});

// 流式同步操作（带实时日志）
app.post('/api/sync/:repoId/stream', (req, res) => {
    const { repoId } = req.params;
    
    if (!currentConfig || !currentConfig.repositories) {
        return res.status(400).json({ error: '没有配置信息' });
    }
    
    const repo = currentConfig.repositories.find(r => r.id === repoId);
    if (!repo) {
        return res.status(404).json({ error: '仓库未找到' });
    }
    
    if (!repo.enabled) {
        return res.status(400).json({ error: '仓库未启用' });
    }
    
    // 设置SSE响应头
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
    });
    
    function sendLog(type, message, data = {}) {
        const logData = {
            type,
            message,
            timestamp: new Date().toISOString(),
            repoId,
            repoName: repo.name,
            ...data
        };
        res.write(`data: ${JSON.stringify(logData)}\n\n`);
    }
    
    // 确保目标目录存在
    const targetDir = path.dirname(repo.target);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        sendLog('info', `创建目标目录: ${targetDir}`);
    }
    
    // 构建rsync命令
    let rsyncOptions = ['-av', '--progress', '--stats'];
    
    if (repo.strategy === 'mirror') {
        rsyncOptions.push('--delete');
    }
    
    if (repo.sync_settings?.preserve_permissions) {
        rsyncOptions.push('--perms');
    }
    
    if (repo.sync_settings?.preserve_timestamps) {
        rsyncOptions.push('--times');
    }
    
    // 添加排除模式
    if (repo.exclude_patterns && repo.exclude_patterns.length > 0) {
        repo.exclude_patterns.forEach(pattern => {
            rsyncOptions.push(`--exclude=${pattern}`);
        });
    }
    
    const command = ['rsync', ...rsyncOptions, `${repo.source}/`, `${repo.target}/`];
    
    sendLog('start', `开始同步: ${repo.name}`, { command: command.join(' ') });
    
    const rsyncProcess = spawn('rsync', rsyncOptions.concat([`${repo.source}/`, `${repo.target}/`]), {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    rsyncProcess.stdout.on('data', (data) => {
        const output = data.toString();
        output.split('\n').forEach(line => {
            if (line.trim()) {
                sendLog('progress', line.trim());
            }
        });
    });
    
    rsyncProcess.stderr.on('data', (data) => {
        const error = data.toString();
        error.split('\n').forEach(line => {
            if (line.trim()) {
                sendLog('error', line.trim());
            }
        });
    });
    
    rsyncProcess.on('close', (code) => {
        if (code === 0) {
            sendLog('success', `同步完成: ${repo.name}`, { exitCode: code });
        } else {
            sendLog('error', `同步失败: ${repo.name}`, { exitCode: code });
        }
        res.end();
    });
    
    rsyncProcess.on('error', (error) => {
        sendLog('error', `进程错误: ${error.message}`);
        res.end();
    });
    
    // 处理客户端断开连接
    req.on('close', () => {
        if (rsyncProcess && !rsyncProcess.killed) {
            rsyncProcess.kill();
        }
    });
});

// 启动/停止监控
app.post('/api/monitor/:action', (req, res) => {
    const { action } = req.params; // start or stop

    if (action === 'start') {
        // 启动监控需要在后台运行
        const command = `cd ${SCRIPT_DIR} && nohup ./sync-monitor.sh start > /dev/null 2>&1 &`;
        exec(command, (error, stdout, stderr) => {
            if (error) {
                return res.status(500).json({ error: `监控启动失败`, details: stderr });
            }

            // 等待一下让监控系统启动，然后修正PID文件
            setTimeout(() => {
                const fixPidCommand = `cd ${SCRIPT_DIR} && ps aux | grep "bash.*sync-monitor.sh start" | grep -v grep | head -1 | awk '{print $2}' > pids/monitor.pid`;
                exec(fixPidCommand, () => {
                    res.json({ success: true, message: '监控系统启动完成' });
                });
            }, 2000);
        });
    } else if (action === 'stop') {
        // 停止监控
        const command = `cd ${SCRIPT_DIR} && ./sync-monitor.sh stop`;
        exec(command, (error, stdout, stderr) => {
            if (error) {
                return res.status(500).json({ error: `监控停止失败`, details: stderr });
            }

            res.json({ success: true, output: stdout });
        });
    } else {
        res.status(400).json({ error: '无效的操作' });
    }
});

// 获取日志
app.get('/api/logs', (req, res) => {
    const { lines = 100 } = req.query;
    const logDir = path.join(SCRIPT_DIR, 'logs');
    
    if (!fs.existsSync(logDir)) {
        return res.json({ logs: [] });
    }
    
    const logFiles = fs.readdirSync(logDir)
        .filter(file => file.endsWith('.log'))
        .sort()
        .reverse();
    
    if (logFiles.length === 0) {
        return res.json({ logs: [] });
    }
    
    const latestLog = path.join(logDir, logFiles[0]);
    exec(`tail -n ${lines} "${latestLog}"`, (error, stdout, stderr) => {
        if (error) {
            return res.status(500).json({ error: '读取日志失败', details: stderr });
        }
        
        const logs = stdout.split('\n').filter(line => line.trim()).map(line => {
            const match = line.match(/\[(.+?)\] \[(.+?)\] (.+)/);
            if (match) {
                return {
                    timestamp: match[1],
                    level: match[2],
                    message: match[3]
                };
            }
            return { timestamp: '', level: 'INFO', message: line };
        });
        
        res.json({ logs });
    });
});

// 目录浏览API
app.get('/api/browse', (req, res) => {
    try {
        const { path: requestPath = '.' } = req.query;
        
        // 安全检查：防止目录遍历攻击
        const safePath = path.resolve(requestPath);
        const basePath = path.resolve(SCRIPT_DIR, '..');
        
        if (!safePath.startsWith(basePath) && !path.isAbsolute(requestPath)) {
            return res.status(403).json({ error: '访问被拒绝' });
        }
        
        if (!fs.existsSync(safePath)) {
            return res.status(404).json({ error: '路径不存在' });
        }
        
        const stat = fs.statSync(safePath);
        if (!stat.isDirectory()) {
            return res.status(400).json({ error: '不是一个目录' });
        }
        
        const items = fs.readdirSync(safePath).map(item => {
            const itemPath = path.join(safePath, item);
            const itemStat = fs.statSync(itemPath);
            return {
                name: item,
                path: itemPath,
                isDirectory: itemStat.isDirectory(),
                size: itemStat.isDirectory() ? null : itemStat.size,
                modified: itemStat.mtime
            };
        }).sort((a, b) => {
            // 目录排在前面，然后按名称排序
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });
        
        res.json({
            currentPath: safePath,
            parentPath: path.dirname(safePath),
            items: items
        });
        
    } catch (error) {
        res.status(500).json({ error: '浏览目录失败', details: error.message });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 同步配置界面已启动: http://localhost:${PORT}`);
    console.log(`📂 配置文件路径: ${CONFIG_FILE}`);
});