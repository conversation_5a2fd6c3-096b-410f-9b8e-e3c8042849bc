#!/bin/bash

# =============================================================================
# 测试仓库配置脚本
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 配置测试仓库...${NC}"
echo ""

# 获取当前脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sync-config.json"

# 备份现有配置
if [ -f "$CONFIG_FILE" ]; then
    echo -e "${YELLOW}📋 备份现有配置...${NC}"
    cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 创建新的测试配置
echo -e "${CYAN}⚙️  创建测试配置...${NC}"

cat > "$CONFIG_FILE" << 'EOF'
{
  "version": "1.0",
  "metadata": {
    "created_at": "2025-01-03T14:49:00Z",
    "updated_at": "2025-01-03T14:49:00Z",
    "created_by": "setup-test-repos",
    "description": "测试仓库同步配置"
  },
  "global": {
    "log_level": "INFO",
    "log_file": "logs/sync.log",
    "log_retention_days": 30,
    "log_max_size_mb": 100,
    "backup_enabled": true,
    "backup_retention": 10,
    "backup_location": "backups/",
    "temp_directory": "tmp/",
    "max_concurrent_syncs": 3,
    "timeout_seconds": 300
  },
  "repositories": [
    {
      "id": "test_project_a",
      "name": "测试项目A",
      "description": "用于单向同步测试的项目A",
      "source": "./test_projects/project_a",
      "target": "./test_backups/project_a",
      "strategy": "unidirectional",
      "conflict_resolution": "source-wins",
      "enabled": true,
      "created_at": "2025-01-03T14:49:00Z",
      "sync_settings": {
        "include_git": false,
        "include_hidden": false,
        "follow_symlinks": false,
        "preserve_permissions": true,
        "preserve_timestamps": true,
        "compress_transfer": false
      },
      "exclude_patterns": [
        "*.log",
        "*.tmp",
        ".cache/**",
        "__pycache__/**",
        "node_modules/**"
      ],
      "include_patterns": [
        "src/**",
        "config/**",
        "docs/**",
        "data/**",
        "*.md",
        "*.json"
      ],
      "schedule": {
        "enabled": false,
        "interval": "5m",
        "cron": "",
        "run_at_startup": false
      },
      "hooks": {
        "pre_sync": "",
        "post_sync": "",
        "on_error": "",
        "on_conflict": ""
      }
    },
    {
      "id": "test_project_b",
      "name": "测试项目B",  
      "description": "用于双向同步测试的项目B",
      "source": "./test_projects/project_b",
      "target": "./test_backups/project_b",
      "strategy": "bidirectional",
      "conflict_resolution": "manual",
      "enabled": false,
      "created_at": "2025-01-03T14:49:00Z",
      "sync_settings": {
        "include_git": false,
        "include_hidden": false,
        "follow_symlinks": false,
        "preserve_permissions": true,
        "preserve_timestamps": true,
        "compress_transfer": false
      },
      "exclude_patterns": [
        "*.log",
        "*.tmp", 
        ".cache/**",
        "__pycache__/**",
        "node_modules/**"
      ],
      "include_patterns": [
        "src/**",
        "config/**",
        "docs/**",
        "data/**",
        "*.md",
        "*.json",
        "*.js"
      ],
      "schedule": {
        "enabled": false,
        "interval": "10m",
        "cron": "",
        "run_at_startup": false
      },
      "hooks": {
        "pre_sync": "",
        "post_sync": "",
        "on_error": "",
        "on_conflict": ""
      }
    }
  ],
  "monitoring": {
    "enabled": true,
    "method": "inotify", 
    "debounce_seconds": 3,
    "watch_subdirectories": true,
    "excluded_events": [
      "access"
    ],
    "batch_size": 100,
    "max_queue_size": 1000
  },
  "conflict_resolution": {
    "default_strategy": "manual",
    "auto_resolve_timeout": 300,
    "keep_backup_on_conflict": true,
    "conflict_markers": {
      "start": "<<<<<<< SOURCE",
      "middle": "=======",
      "end": ">>>>>>> TARGET"
    }
  },
  "notifications": {
    "enabled": false,
    "methods": [
      "log"
    ],
    "on_success": false,
    "on_error": true,
    "on_conflict": true,
    "on_start": false,
    "on_complete": false
  },
  "security": {
    "require_confirmation": false,
    "allowed_paths": [],
    "denied_paths": [
      "/etc/**",
      "/sys/**",
      "/proc/**",
      "/dev/**"
    ],
    "max_file_size_mb": 1000,
    "check_permissions": true
  },
  "performance": {
    "parallel_transfers": true,
    "max_parallel_files": 5,
    "checksum_verification": "md5",
    "compression_enabled": false,
    "compression_level": 6,
    "cache_enabled": true,
    "cache_size_mb": 128
  }
}
EOF

echo -e "${GREEN}✅ 测试配置已创建${NC}"
echo ""
echo -e "${CYAN}📁 测试仓库结构:${NC}"
echo "  源项目A: ./test_projects/project_a"
echo "  备份A:   ./test_backups/project_a"
echo "  源项目B: ./test_projects/project_b" 
echo "  备份B:   ./test_backups/project_b"
echo ""
echo -e "${CYAN}🔧 同步配置:${NC}"
echo "  项目A: 单向同步 (source-wins) - 已启用"
echo "  项目B: 双向同步 (manual)     - 未启用"
echo ""
echo -e "${YELLOW}💡 使用建议:${NC}"
echo "1. 启动Web界面测试单向同步:"
echo "   cd web-ui && ./start-ui.sh"
echo ""
echo "2. 访问Web界面:"
echo "   http://localhost:3000"
echo ""
echo "3. 手动测试同步:"
echo "   修改 test_projects/project_a/ 中的文件"
echo "   观察 test_backups/project_a/ 的变化"
echo ""
echo "4. 启用项目B测试双向同步:"
echo "   在Web界面中启用项目B"
echo "   同时修改源和目标文件测试冲突解决"
echo ""