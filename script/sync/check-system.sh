#!/bin/bash

# =============================================================================
# 系统依赖检查脚本
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 检查同步系统依赖...${NC}"
echo ""

# 检查必需的工具
check_required() {
    local tool="$1"
    local package="$2"
    
    if command -v "$tool" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $tool${NC} - $(which $tool)"
    else
        echo -e "${RED}❌ $tool${NC} - 缺失"
        echo -e "   安装: ${YELLOW}sudo apt install $package${NC}"
        return 1
    fi
}

# 检查可选工具
check_optional() {
    local tool="$1"
    local package="$2"
    local description="$3"
    
    if command -v "$tool" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $tool${NC} - $(which $tool)"
    else
        echo -e "${YELLOW}⚠️  $tool${NC} - 可选 ($description)"
        echo -e "   安装: ${YELLOW}sudo apt install $package${NC}"
    fi
}

# 检查文件
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $description${NC} - $file"
    else
        echo -e "${RED}❌ $description${NC} - $file 不存在"
        return 1
    fi
}

# 检查目录
check_directory() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✅ $description${NC} - $dir"
    else
        echo -e "${YELLOW}⚠️  $description${NC} - $dir 不存在，将自动创建"
        mkdir -p "$dir"
    fi
}

# 检查JSON文件格式
check_json() {
    local file="$1"
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 配置文件不存在${NC} - $file"
        return 1
    fi
    
    if jq '.' "$file" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 配置文件格式正确${NC} - $file"
    else
        echo -e "${RED}❌ 配置文件格式错误${NC} - $file"
        return 1
    fi
}

failed=0

echo "🔧 必需工具检查:"
check_required "bash" "bash" || ((failed++))
check_required "jq" "jq" || ((failed++))
check_required "rsync" "rsync" || ((failed++))

echo ""
echo "⚙️  可选工具检查:"
check_optional "inotifywait" "inotify-tools" "实时文件监控"
check_optional "node" "nodejs" "Web界面运行"
check_optional "npm" "npm" "Web界面依赖管理"

echo ""
echo "📁 目录结构检查:"
check_directory "logs" "日志目录"
check_directory "backups" "备份目录"
check_directory "tmp" "临时目录"
check_directory "pids" "进程文件目录"

echo ""
echo "📄 文件检查:"
check_file "sync-config.json" "配置文件" || ((failed++))
check_file "sync-core.sh" "核心脚本" || ((failed++))
check_file "sync-monitor.sh" "监控脚本" || ((failed++))

echo ""
echo "🔍 配置文件检查:"
check_json "sync-config.json" || ((failed++))

echo ""
echo "🌐 Web界面检查:"
if [ -d "web-ui" ]; then
    echo -e "${GREEN}✅ Web界面目录存在${NC}"
    if [ -f "web-ui/package.json" ]; then
        echo -e "${GREEN}✅ Web界面配置存在${NC}"
        if [ -d "web-ui/node_modules" ]; then
            echo -e "${GREEN}✅ Web界面依赖已安装${NC}"
        else
            echo -e "${YELLOW}⚠️  Web界面依赖未安装${NC}"
            echo -e "   安装: ${YELLOW}cd web-ui && npm install${NC}"
        fi
    else
        echo -e "${RED}❌ Web界面配置缺失${NC}"
    fi
else
    echo -e "${RED}❌ Web界面目录不存在${NC}"
fi

echo ""
echo "==============================================="

if [ $failed -eq 0 ]; then
    echo -e "${GREEN}🎉 系统检查通过！所有必需依赖都已满足。${NC}"
    echo ""
    echo "启动Web界面:"
    echo -e "${BLUE}cd web-ui && ./start-ui.sh${NC}"
    echo ""
    echo "启动监控:"
    echo -e "${BLUE}./sync-monitor.sh start${NC}"
else
    echo -e "${RED}⚠️  发现 $failed 个问题，请按上述提示修复。${NC}"
fi

echo ""