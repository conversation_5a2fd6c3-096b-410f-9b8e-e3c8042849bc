# 系统架构设计

## 🏗️ 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器                                                   │
│  ├── HTML5 界面 (index.html)                                │
│  ├── CSS3 样式 (styles.css)                                │
│  └── JavaScript 逻辑 (app.js)                               │
└─────────────────────────────────────────────────────────────┘
                                │ HTTP/REST API
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   Web服务层 (Service Layer)                  │
├─────────────────────────────────────────────────────────────┤
│  Node.js + Express 服务器 (server.js)                       │
│  ├── 配置管理 API                                           │
│  ├── 状态查询 API                                           │
│  ├── 同步控制 API                                           │
│  └── 日志查看 API                                           │
└─────────────────────────────────────────────────────────────┘
                                │ 文件操作 + 进程调用
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   核心业务层 (Core Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ├── 配置管理 (sync-config.json)                            │
│  ├── 监控服务 (sync-monitor.sh)                             │
│  ├── 核心工具 (sync-core.sh)                                │
│  └── 同步引擎 (rsync + 自定义逻辑)                           │
└─────────────────────────────────────────────────────────────┘
                                │ 文件系统操作
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   存储层 (Storage Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ├── 源仓库目录                                             │
│  ├── 目标仓库目录                                           │
│  ├── 日志文件 (logs/)                                       │
│  ├── 备份文件 (backups/)                                    │
│  └── 临时文件 (tmp/, pids/)                                 │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 数据流设计

### 配置流程
```
用户操作 → Web界面 → POST /api/config → 写入JSON → 重新加载配置
```

### 同步流程
```
用户点击同步 → POST /api/sync/:id → 读取配置 → 构建rsync命令 → 执行同步 → 返回结果
```

### 监控流程
```
启动监控 → POST /api/monitor/start → 调用sync-monitor.sh → inotify监控 → 自动同步
```

### 状态查询
```
定时查询 → GET /api/status → 检查PID文件 → 返回状态信息
```

## 🧩 模块设计

### 1. Web服务器 (server.js)
```javascript
┌─ 路径配置
├─ 配置加载器 (loadCurrentConfig)
├─ API路由
│  ├─ GET  /api/config     # 获取配置
│  ├─ POST /api/config     # 保存配置
│  ├─ GET  /api/status     # 系统状态
│  ├─ POST /api/sync/:id   # 执行同步
│  ├─ POST /api/monitor/*  # 监控控制
│  └─ GET  /api/logs       # 日志查看
└─ 静态文件服务
```

### 2. 前端应用 (app.js)
```javascript
┌─ 全局状态管理
├─ UI组件控制
│  ├─ 导航切换
│  ├─ 表单处理
│  ├─ 状态显示
│  └─ 操作反馈
├─ API通信层
└─ 事件处理器
```

### 3. 配置系统 (sync-config.json)
```json
{
  "version": "配置版本",
  "metadata": "元数据信息",
  "global": "全局设置",
  "repositories": "仓库配置数组",
  "monitoring": "监控设置",
  "notifications": "通知设置",
  "security": "安全策略",
  "performance": "性能参数"
}
```

### 4. 监控服务 (sync-monitor.sh)
```bash
┌─ 进程管理
├─ inotify 文件监控
├─ 事件队列处理
├─ 防抖动机制
└─ 状态文件管理
```

## 🔧 技术选型

### 前端技术栈
- **HTML5**: 现代标记语言
- **CSS3**: Flexbox + Grid 布局
- **JavaScript**: 原生ES6+，无框架依赖
- **设计**: 响应式设计，支持移动端

### 后端技术栈
- **Node.js**: JavaScript运行时
- **Express.js**: Web框架
- **文件系统**: 原生fs模块
- **进程管理**: child_process模块

### 系统工具
- **rsync**: 文件同步核心工具
- **inotify**: Linux文件系统监控
- **bash**: Shell脚本环境
- **jq**: JSON处理工具

## 📊 性能考虑

### 1. 并发控制
- 最大并发同步数: 可配置 (默认3个)
- 队列管理: 避免资源冲突
- 超时机制: 防止进程挂起

### 2. 内存优化
- 配置热重载: 避免重启服务
- 流式处理: 大文件传输优化
- 缓存策略: 减少重复计算

### 3. 网络优化
- 增量同步: 只传输变更文件
- 压缩传输: 可选的压缩模式
- 断点续传: rsync内置支持

## 🛡️ 安全设计

### 1. 路径安全
- 相对路径: 避免绝对路径泄露
- 路径验证: 防止目录遍历攻击
- 权限检查: 确保文件访问权限

### 2. 进程安全
- PID文件管理: 防止进程冲突
- 权限隔离: 最小权限原则
- 错误处理: 完善的异常捕获

### 3. 数据安全
- 配置备份: 自动创建备份文件
- 输入验证: API参数严格验证
- 日志安全: 敏感信息过滤

## 🔌 扩展性

### 1. 插件机制
- Hook支持: pre_sync, post_sync等
- 自定义脚本: 可配置的执行脚本
- 事件通知: 邮件/Webhook集成

### 2. 协议扩展
- 多协议支持: rsync, scp, sftp
- 云存储: 可扩展云服务集成
- 版本控制: Git集成支持

### 3. 界面扩展
- 主题系统: 可自定义界面主题
- 语言国际化: 多语言支持基础
- 移动端优化: PWA特性支持

## 📈 监控指标

### 系统指标
- 同步成功率
- 同步耗时统计
- 错误频率分析
- 资源使用情况

### 业务指标
- 活跃仓库数量
- 文件传输量统计
- 用户操作频率
- 配置变更记录