# 项目架构概览

## 📁 目录结构

```
sync/                     # 同步系统根目录
├── README.md            # 主说明文档
├── sync-monitor.sh      # 👁️ 监控脚本  
├── sync-core.sh         # ⚙️ 核心功能库
├── sync-config.json     # 📋 配置文件
│
├── web-ui/              # 🌐 Web界面
│   ├── install.sh       # 安装脚本
│   ├── start-ui.sh      # 启动脚本
│   ├── server.js        # Express后端
│   └── public/          # 前端文件
│
├── docs/                # 📚 文档目录
│   ├── OVERVIEW.md      # 项目概览
│   └── Updates.md       # 更新记录
│
├── logs/                # 📝 日志文件
├── backups/             # 💾 备份文件
├── pids/                # 🔧 进程ID文件
└── tmp/                 # 🗂️ 临时文件
```

## 🔧 核心组件

### Web界面 (主要交互方式)
- **前端**: 响应式现代化界面
- **后端**: Node.js + Express API服务
- **功能**: 可视化配置、实时监控、一键操作

### 核心服务
- **sync-monitor.sh**: 文件系统监控服务 
- **sync-core.sh**: 核心功能库和工具函数
- **sync-config.json**: 统一配置文件

## 🌊 简化的工作流程

```
Web界面 → 配置管理 → 监控服务 → 自动同步
   ↓         ↓         ↓         ↓
 用户操作   JSON配置   文件监控   rsync执行
```

## 📋 主要功能

- **🎛️ 可视化配置**: 图形化编辑所有同步设置
- **📊 实时监控**: 状态面板和日志查看
- **🔄 一键同步**: 简单的操作界面
- **⚡ 自动监控**: inotify文件系统监控
- **💾 安全备份**: 自动配置备份机制

## 📖 详细文档

- **[完整架构设计](ARCHITECTURE.md)** - 技术架构和数据流设计
- **[更新记录](Updates.md)** - 版本变更历史

**推荐**: 查看 [ARCHITECTURE.md](ARCHITECTURE.md) 了解完整的技术架构设计。