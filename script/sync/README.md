# 本地仓库同步系统

专为本地Git仓库设计的双向同步系统，支持实时监控、冲突解决和自动化同步。

## 🚀 快速开始

### 1. 系统检查 (推荐第一步)

```bash
# 检查所有依赖和配置
./check-system.sh
```

### 2. Web界面 (推荐)

```bash
# 启动Web配置界面
cd web-ui
./install.sh
./start-ui.sh

# 浏览器访问
open http://localhost:3000
```

### 命令行操作

```bash
# 启动监控
./sync-monitor.sh start

# 停止监控  
./sync-monitor.sh stop

# 查看状态
./sync-monitor.sh status
```

## 📁 项目结构

```
sync/
├── README.md              # 主说明文档
├── check-system.sh        # 系统依赖检查
├── sync-monitor.sh        # 监控脚本
├── sync-core.sh          # 核心功能库
├── sync-config.json      # 配置文件
├── web-ui/               # Web界面
│   ├── install.sh        # 安装脚本
│   ├── start-ui.sh       # 启动脚本
│   └── public/           # 前端文件
├── docs/                 # 详细文档
│   ├── INSTALL.md        # 安装指南
│   ├── USAGE.md          # 使用说明
│   ├── EXAMPLES.md       # 使用示例
│   └── Updates.md        # 更新记录
├── logs/                 # 日志文件
├── backups/              # 备份文件
└── tmp/                  # 临时文件
```

## 🎛️ 主要功能

- **🔄 多种同步模式**: 单向、双向、镜像同步
- **🛡️ 智能冲突处理**: 多种冲突解决策略
- **⚡ 实时监控**: inotify文件系统监控
- **🌐 Web界面**: 可视化配置和管理
- **📊 状态监控**: 实时同步状态追踪
- **📝 完整日志**: 详细的操作记录

## 📖 文档

- **[安装指南](docs/INSTALL.md)** - 系统要求和安装步骤
- **[使用说明](docs/USAGE.md)** - 详细的使用方法
- **[使用示例](docs/EXAMPLES.md)** - 常见场景配置示例
- **[更新记录](docs/Updates.md)** - 版本更新日志

## 💡 使用场景

- **开发环境备份**: 自动备份开发代码
- **多机同步**: 工作机与笔记本同步
- **项目部署**: 开发环境到生产环境同步
- **团队协作**: 共享开发环境同步

## 🔧 技术特点

- **轻量级**: 基于Bash脚本，资源占用少
- **可靠性**: 完善的错误处理和恢复机制  
- **可扩展**: 支持自定义hooks和插件
- **跨平台**: 支持Linux、macOS、Windows(WSL)

## 📞 支持

- 问题反馈: 查看 [docs/USAGE.md](docs/USAGE.md)
- 配置示例: 查看 [docs/EXAMPLES.md](docs/EXAMPLES.md) 
- 安装问题: 查看 [docs/INSTALL.md](docs/INSTALL.md)

---

**推荐使用Web界面进行配置，图形化操作更加直观便捷！**