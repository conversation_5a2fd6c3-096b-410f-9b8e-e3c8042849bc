#!/bin/bash

# =============================================================================
# 本地仓库同步监控系统
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/sync-config.json"
LOG_DIR="$SCRIPT_DIR/logs"
PID_DIR="$SCRIPT_DIR/pids"
TEMP_DIR="$SCRIPT_DIR/tmp"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$TEMP_DIR"

# 监控相关文件
MONITOR_LOG="$LOG_DIR/monitor-$(date +%Y%m%d).log"
MONITOR_PID_FILE="$PID_DIR/monitor.pid"
QUEUE_FILE="$TEMP_DIR/sync_queue.txt"
STATUS_FILE="$TEMP_DIR/monitor_status.json"

# 加载核心功能
source "$SCRIPT_DIR/sync-core.sh"

# =============================================================================
# 监控系统配置
# =============================================================================

# 从配置文件读取监控设置
MONITORING_ENABLED=$(get_config_value ".monitoring.enabled" "true")
MONITORING_METHOD=$(get_config_value ".monitoring.method" "inotify")
DEBOUNCE_SECONDS=$(get_config_value ".monitoring.debounce_seconds" "5")
WATCH_SUBDIRS=$(get_config_value ".monitoring.watch_subdirectories" "true")
EXCLUDED_EVENTS=$(get_config_value ".monitoring.excluded_events" '["access"]')
BATCH_SIZE=$(get_config_value ".monitoring.batch_size" "100")
MAX_QUEUE_SIZE=$(get_config_value ".monitoring.max_queue_size" "1000")

# =============================================================================
# 监控状态管理
# =============================================================================

# 初始化状态文件
init_status() {
    cat > "$STATUS_FILE" << EOF
{
  "monitor_pid": null,
  "start_time": null,
  "status": "stopped",
  "watched_repositories": [],
  "sync_queue_size": 0,
  "last_sync_time": null,
  "stats": {
    "total_events": 0,
    "successful_syncs": 0,
    "failed_syncs": 0,
    "conflicts_resolved": 0
  }
}
EOF
}

# 更新状态
update_status() {
    local field="$1"
    local value="$2"
    
    if [ ! -f "$STATUS_FILE" ]; then
        init_status
    fi
    
    # 使用jq更新状态文件
    local temp_file
    temp_file=$(mktemp)
    jq "$field = $value" "$STATUS_FILE" > "$temp_file" && mv "$temp_file" "$STATUS_FILE"
}

# 增加统计计数
increment_stat() {
    local stat_name="$1"
    local temp_file
    temp_file=$(mktemp)
    jq ".stats.$stat_name += 1" "$STATUS_FILE" > "$temp_file" && mv "$temp_file" "$STATUS_FILE"
}

# 获取状态
get_status() {
    local field="$1"
    jq -r "$field // empty" "$STATUS_FILE" 2>/dev/null
}

# =============================================================================
# 队列管理
# =============================================================================

# 初始化同步队列
init_queue() {
    > "$QUEUE_FILE"
    update_status ".sync_queue_size" "0"
}

# 添加到同步队列
add_to_queue() {
    local repo_id="$1"
    local event_type="$2"
    local file_path="$3"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 检查队列大小
    local queue_size
    queue_size=$(wc -l < "$QUEUE_FILE" 2>/dev/null || echo "0")
    
    if [ "$queue_size" -ge "$MAX_QUEUE_SIZE" ]; then
        log_warning "同步队列已满，丢弃最早的条目"
        tail -n +2 "$QUEUE_FILE" > "$QUEUE_FILE.tmp" && mv "$QUEUE_FILE.tmp" "$QUEUE_FILE"
    fi
    
    # 添加到队列
    echo "$timestamp|$repo_id|$event_type|$file_path" >> "$QUEUE_FILE"
    
    # 更新状态
    queue_size=$(wc -l < "$QUEUE_FILE")
    update_status ".sync_queue_size" "$queue_size"
    
    log_debug "添加到同步队列: $repo_id ($event_type) $file_path"
}

# 从队列中获取项目
get_from_queue() {
    local batch_size="${1:-$BATCH_SIZE}"
    
    if [ ! -s "$QUEUE_FILE" ]; then
        return 1
    fi
    
    # 获取指定数量的条目
    head -n "$batch_size" "$QUEUE_FILE"
    
    # 从队列中移除已处理的条目
    tail -n +$((batch_size + 1)) "$QUEUE_FILE" > "$QUEUE_FILE.tmp" && mv "$QUEUE_FILE.tmp" "$QUEUE_FILE"
    
    # 更新状态
    local queue_size
    queue_size=$(wc -l < "$QUEUE_FILE")
    update_status ".sync_queue_size" "$queue_size"
}

# 清空队列
clear_queue() {
    init_queue
    log_info "同步队列已清空"
}

# =============================================================================
# 文件系统监控
# =============================================================================

# 检查监控工具
check_monitor_tools() {
    case "$MONITORING_METHOD" in
        "inotify")
            if ! command -v inotifywait >/dev/null 2>&1; then
                log_error "inotifywait 未安装。请安装 inotify-tools 包"
                return 1
            fi
            ;;
        "polling")
            # 轮询方式不需要额外工具
            ;;
        *)
            log_error "不支持的监控方法: $MONITORING_METHOD"
            return 1
            ;;
    esac
    
    return 0
}

# 获取inotify事件参数
get_inotify_events() {
    local events="create,delete,modify,move"
    
    # 排除指定事件
    local excluded
    excluded=$(echo "$EXCLUDED_EVENTS" | jq -r '.[]' 2>/dev/null | tr '\n' ',' | sed 's/,$//')
    
    if [ -n "$excluded" ]; then
        # 从事件列表中移除排除的事件
        events=$(echo "$events" | sed "s/,$excluded//g" | sed "s/$excluded,//g" | sed "s/$excluded//g")
    fi
    
    echo "$events"
}

# 启动inotify监控
start_inotify_monitor() {
    local repo_id="$1"
    local watch_path="$2"
    
    if [ ! -d "$watch_path" ]; then
        log_error "监控路径不存在: $watch_path"
        return 1
    fi
    
    local events
    events=$(get_inotify_events)
    
    local inotify_opts="-m"
    [ "$WATCH_SUBDIRS" = "true" ] && inotify_opts="$inotify_opts -r"
    
    log_info "启动inotify监控: $watch_path (repo: $repo_id)"
    
    # 启动inotifywait监控
    inotifywait $inotify_opts -e "$events" --format '%w%f|%e|%T' --timefmt '%Y-%m-%d %H:%M:%S' "$watch_path" 2>/dev/null | \
    while IFS='|' read -r file_path event_type timestamp; do
        # 过滤临时文件和不需要的文件
        if [[ "$file_path" =~ \.(tmp|swp|~)$ ]] || [[ "$file_path" =~ \.git/objects/ ]]; then
            continue
        fi
        
        log_debug "文件事件: $event_type $file_path"
        
        # 添加到同步队列
        add_to_queue "$repo_id" "$event_type" "$file_path"
        
        # 增加事件计数
        increment_stat "total_events"
        
        # 触发防抖处理
        trigger_debounced_sync "$repo_id"
    done &
    
    local monitor_pid=$!
    echo "$monitor_pid" > "$PID_DIR/inotify_${repo_id}.pid"
    
    return 0
}

# 启动轮询监控
start_polling_monitor() {
    local repo_id="$1"
    local watch_path="$2"
    local interval="${3:-30}"
    
    log_info "启动轮询监控: $watch_path (repo: $repo_id, interval: ${interval}s)"
    
    while true; do
        # 检查是否应该继续监控
        if [ ! -f "$MONITOR_PID_FILE" ]; then
            break
        fi
        
        # 使用find检查文件变更
        local current_state
        current_state=$(find "$watch_path" -type f -newer "$TEMP_DIR/last_poll_${repo_id}" 2>/dev/null | wc -l)
        
        if [ "$current_state" -gt 0 ]; then
            log_debug "检测到文件变更: $watch_path"
            add_to_queue "$repo_id" "modify" "$watch_path"
            increment_stat "total_events"
            trigger_debounced_sync "$repo_id"
        fi
        
        # 更新检查时间戳
        touch "$TEMP_DIR/last_poll_${repo_id}"
        
        sleep "$interval"
    done &
    
    local monitor_pid=$!
    echo "$monitor_pid" > "$PID_DIR/polling_${repo_id}.pid"
    
    return 0
}

# =============================================================================
# 防抖处理
# =============================================================================

# 防抖同步触发器
trigger_debounced_sync() {
    local repo_id="$1"
    local debounce_file="$TEMP_DIR/debounce_${repo_id}"
    
    # 记录触发时间
    date +%s > "$debounce_file"
    
    # 启动防抖检查
    (
        sleep "$DEBOUNCE_SECONDS"
        
        # 检查是否在防抖期间有新的触发
        local last_trigger
        last_trigger=$(cat "$debounce_file" 2>/dev/null || echo "0")
        local current_time
        current_time=$(date +%s)
        
        if [ $((current_time - last_trigger)) -ge "$DEBOUNCE_SECONDS" ]; then
            # 防抖期结束，执行同步
            process_sync_queue "$repo_id"
        fi
    ) &
}

# =============================================================================
# 同步处理
# =============================================================================

# 处理同步队列
process_sync_queue() {
    local repo_id="$1"
    
    log_info "处理同步队列: $repo_id"
    
    # 检查队列是否为空
    if [ ! -s "$QUEUE_FILE" ]; then
        log_debug "同步队列为空，无需处理"
        return 0
    fi
    
    # 获取待处理的条目
    local queue_items
    queue_items=$(get_from_queue "$BATCH_SIZE")
    
    if [ -z "$queue_items" ]; then
        return 0
    fi
    
    log_info "处理 $(echo "$queue_items" | wc -l) 个队列项目"
    
    # 执行同步
    if sync_repository "$repo_id"; then
        increment_stat "successful_syncs"
        update_status ".last_sync_time" "\"$(date '+%Y-%m-%d %H:%M:%S')\""
        log_success "自动同步完成: $repo_id"
    else
        increment_stat "failed_syncs"
        log_error "自动同步失败: $repo_id"
    fi
}

# =============================================================================
# 监控管理
# =============================================================================

# 启动监控
start_monitor() {
    local repo_filter="$1"
    
    log_info "启动仓库监控系统"
    
    # 检查监控工具
    if ! check_monitor_tools; then
        return 1
    fi
    
    # 检查是否已有监控进程
    if [ -f "$MONITOR_PID_FILE" ] && kill -0 "$(cat "$MONITOR_PID_FILE")" 2>/dev/null; then
        log_warning "监控系统已在运行中 (PID: $(cat "$MONITOR_PID_FILE"))"
        return 1
    fi
    
    # 初始化状态和队列
    init_status
    init_queue
    
    # 获取要监控的仓库列表
    local repo_query=".repositories[] | select(.enabled == true)"
    if [ -n "$repo_filter" ]; then
        repo_query="$repo_query | select(.id == \"$repo_filter\")"
    fi
    
    local repo_list
    repo_list=$(jq -r "$repo_query | {id: .id, source: .source}" "$CONFIG_FILE" 2>/dev/null)
    
    if [ -z "$repo_list" ]; then
        log_error "没有找到要监控的仓库"
        return 1
    fi
    
    # 更新状态
    update_status ".status" '"starting"'
    update_status ".start_time" "\"$(date '+%Y-%m-%d %H:%M:%S')\""
    
    # 启动监控进程
    (
        # 记录主监控进程PID
        echo $$ > "$MONITOR_PID_FILE"
        update_status ".monitor_pid" "$$"
        update_status ".status" '"running"'
        
        log_info "监控系统已启动 (PID: $$)"
        
        # 为每个仓库启动监控
        echo "$repo_list" | jq -r '. | "\(.id)|\(.source)"' | while IFS='|' read -r repo_id source_path; do
            if [ -n "$repo_id" ] && [ -n "$source_path" ] && [ -d "$source_path" ]; then
                case "$MONITORING_METHOD" in
                    "inotify")
                        start_inotify_monitor "$repo_id" "$source_path"
                        ;;
                    "polling")
                        start_polling_monitor "$repo_id" "$source_path" 30
                        ;;
                esac
                
                # 添加到监控列表
                local temp_file
                temp_file=$(mktemp)
                jq ".watched_repositories += [\"$repo_id\"]" "$STATUS_FILE" > "$temp_file" && mv "$temp_file" "$STATUS_FILE"
                
                log_success "开始监控仓库: $repo_id ($source_path)"
            fi
        done
        
        # 主监控循环
        while [ -f "$MONITOR_PID_FILE" ]; do
            # 定期清理和维护
            sleep 60
            
            # 清理过期的临时文件
            find "$TEMP_DIR" -name "debounce_*" -mmin +10 -delete 2>/dev/null || true
            
            # 检查子进程状态
            for pid_file in "$PID_DIR"/*.pid; do
                if [ -f "$pid_file" ]; then
                    local pid
                    pid=$(cat "$pid_file")
                    if ! kill -0 "$pid" 2>/dev/null; then
                        log_warning "子监控进程已停止: $pid_file"
                        rm -f "$pid_file"
                    fi
                fi
            done
        done
        
    ) >> "$MONITOR_LOG" 2>&1 &
    
    log_success "监控系统启动完成"
    return 0
}

# 停止监控
stop_monitor() {
    log_info "停止仓库监控系统"
    
    if [ ! -f "$MONITOR_PID_FILE" ]; then
        log_warning "监控系统未运行"
        return 0
    fi
    
    local monitor_pid
    monitor_pid=$(cat "$MONITOR_PID_FILE")
    
    # 停止主监控进程
    if kill -TERM "$monitor_pid" 2>/dev/null; then
        log_info "发送停止信号到监控进程: $monitor_pid"
        
        # 等待进程结束
        local count=0
        while kill -0 "$monitor_pid" 2>/dev/null && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # 强制结束
        if kill -0 "$monitor_pid" 2>/dev/null; then
            log_warning "强制结束监控进程"
            kill -KILL "$monitor_pid" 2>/dev/null
        fi
    fi
    
    # 停止所有子进程
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            local pid
            pid=$(cat "$pid_file")
            kill -TERM "$pid" 2>/dev/null || true
            rm -f "$pid_file"
        fi
    done
    
    # 清理PID文件
    rm -f "$MONITOR_PID_FILE"
    
    # 更新状态
    update_status ".status" '"stopped"'
    update_status ".monitor_pid" "null"
    
    log_success "监控系统已停止"
    return 0
}

# 重启监控
restart_monitor() {
    log_info "重启仓库监控系统"
    stop_monitor
    sleep 2
    start_monitor "$@"
}

# 获取监控状态
get_monitor_status() {
    if [ ! -f "$STATUS_FILE" ]; then
        echo "监控系统状态: 未初始化"
        return 1
    fi
    
    local status
    local start_time
    local watched_repos
    local queue_size
    local stats
    
    status=$(get_status ".status")
    start_time=$(get_status ".start_time")
    watched_repos=$(get_status ".watched_repositories | length")
    queue_size=$(get_status ".sync_queue_size")
    
    echo "监控系统状态:"
    echo "  状态: $status"
    echo "  启动时间: $start_time"
    echo "  监控仓库数: $watched_repos"
    echo "  队列大小: $queue_size"
    
    if [ "$status" = "running" ]; then
        local monitor_pid
        monitor_pid=$(get_status ".monitor_pid")
        echo "  监控进程PID: $monitor_pid"
        
        # 显示统计信息
        echo "  统计信息:"
        echo "    总事件数: $(get_status '.stats.total_events')"
        echo "    成功同步: $(get_status '.stats.successful_syncs')"
        echo "    失败同步: $(get_status '.stats.failed_syncs')"
        echo "    解决冲突: $(get_status '.stats.conflicts_resolved')"
    fi
}

# =============================================================================
# 命令行接口
# =============================================================================

show_help() {
    cat << EOF
本地仓库同步监控系统

用法: $0 <command> [options]

命令:
  start [repo_id]     启动监控 (可选择特定仓库)
  stop                停止监控
  restart [repo_id]   重启监控
  status              显示监控状态
  queue               队列管理
    clear             清空同步队列
    show              显示队列内容
  logs                显示监控日志

选项:
  -h, --help          显示此帮助信息
  -v, --verbose       详细输出
  -d, --daemon        后台运行

示例:
  $0 start                    # 启动所有仓库监控
  $0 start main_backup        # 只监控特定仓库
  $0 status                   # 查看监控状态
  $0 queue clear              # 清空同步队列
  $0 stop                     # 停止监控

EOF
}

# 主函数
main() {
    local command="$1"
    shift
    
    case "$command" in
        "start")
            start_monitor "$@"
            ;;
        "stop")
            stop_monitor
            ;;
        "restart")
            restart_monitor "$@"
            ;;
        "status")
            get_monitor_status
            ;;
        "queue")
            local queue_cmd="$1"
            case "$queue_cmd" in
                "clear")
                    clear_queue
                    ;;
                "show")
                    echo "同步队列内容:"
                    cat "$QUEUE_FILE" 2>/dev/null || echo "队列为空"
                    ;;
                *)
                    echo "未知队列命令: $queue_cmd"
                    echo "可用命令: clear, show"
                    exit 1
                    ;;
            esac
            ;;
        "logs")
            if [ -f "$MONITOR_LOG" ]; then
                tail -f "$MONITOR_LOG"
            else
                echo "监控日志文件不存在: $MONITOR_LOG"
            fi
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            echo "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi