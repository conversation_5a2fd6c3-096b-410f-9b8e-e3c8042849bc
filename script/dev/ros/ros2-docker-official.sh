#!/bin/bash

# ROS2 Docker 官方镜像安装脚本
# 基于官方 ROS2 镜像，添加 Oh My Zsh 和网络组件

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
IMAGE_NAME="ros2-official-enhanced"
CONTAINER_NAME="ros2-official-container"
HOSTNAME="ros2-docker-xuhui"
WORKSPACE_DIR="/mine/Code/ros2_ws"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    log_info "Docker 检查通过"
}

# 配置 X11 转发
setup_x11() {
    log_step "配置 X11 转发..."
    xhost +local:root > /dev/null 2>&1 || true
    log_info "X11 转发配置完成"
}

# 创建工作空间
create_workspace() {
    if [ ! -d "$WORKSPACE_DIR" ]; then
        log_step "创建 ROS2 工作空间: $WORKSPACE_DIR"
        mkdir -p "$WORKSPACE_DIR/src"
    fi
}

# 创建 Dockerfile
create_dockerfile() {
    log_step "创建增强版 Dockerfile..."

    # 选择镜像源
    local base_image="ros:humble-desktop"
    if [ "$USE_CHINA_MIRROR" = "true" ]; then
        log_info "使用中国镜像源..."
        base_image="registry.cn-hangzhou.aliyuncs.com/google_containers/ros:humble-desktop"
    fi

    cat > "$SCRIPT_DIR/Dockerfile.official" << 'EOF'
# 基于官方 ROS2 Humble 镜像
FROM ros:humble

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 配置时区
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 配置中科大软件源和 APT 优化
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list \
    && sed -i 's@//.*security.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list \
    && echo 'Acquire::http::Pipeline-Depth "0";' > /etc/apt/apt.conf.d/99parallel \
    && echo 'Acquire::http::No-Cache "true";' >> /etc/apt/apt.conf.d/99parallel \
    && echo 'Acquire::BrokenProxy "true";' >> /etc/apt/apt.conf.d/99parallel

# 一次性更新软件源并安装所有工具（优化网络超时）
RUN apt-get update --fix-missing -o Acquire::http::Timeout=30 -o Acquire::ftp::Timeout=30 \
    && apt-get install -y --no-install-recommends \
    # 基础工具
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    unzip \
    sudo \
    zsh \
    locales \
    # 网络工具
    openssh-client \
    openssh-server \
    telnet \
    nmap \
    netcat \
    dnsutils \
    iproute2 \
    iptables \
    net-tools \
    iputils-ping \
    traceroute \
    # 开发工具
    build-essential \
    cmake \
    python3-pip \
    python3-colcon-common-extensions \
    python3-rosdep \
    python3-argcomplete \
    && locale-gen zh_CN.UTF-8 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装额外的工具
RUN apt-get update --fix-missing -o Acquire::http::Timeout=30 -o Acquire::ftp::Timeout=30 \
    && apt-get install -y --no-install-recommends \
    autojump \
    ranger \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 使用自定义的 Oh My Zsh 安装（基于您的 install_omz.sh 逻辑）
RUN REPO=pocmon/ohmyzsh REMOTE=https://gitee.com/pocmon/ohmyzsh.git \
    sh -c "$(wget -qO- https://gitee.com/pocmon/ohmyzsh/raw/master/tools/install.sh)" "" --unattended || \
    (git clone --depth=1 https://gitee.com/pocmon/ohmyzsh.git ~/.oh-my-zsh && \
     cp ~/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc)

# 安装 Zsh 插件（优先使用 Gitee 镜像，设置超时）
RUN timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || true \
    && timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/ranger-autojump.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/ranger-autojump || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/zsh-zellij-plugin.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zellij || true

# 配置 Zsh（使用您的完整插件配置）
RUN sed -i 's/plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting autojump ranger-autojump zellij)/g' ~/.zshrc \
    && echo 'export TERM=xterm-256color' >> ~/.zshrc \
    && echo '# Autojump configuration' >> ~/.zshrc \
    && echo 'source /usr/share/autojump/autojump.sh 2>/dev/null || true' >> ~/.zshrc \
    && echo '# Custom hostname prompt' >> ~/.zshrc \
    && echo 'export PS1="%{$fg[green]%}[%n@%m]%{$reset_color%} %{$fg[blue]%}%~%{$reset_color%} $ "' >> ~/.zshrc

# 创建 xuhui 用户
RUN useradd -m -s /bin/zsh -G sudo xuhui \
    && echo 'xuhui:xuhui' | chpasswd \
    && echo 'xuhui ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# 设置 Zsh 为默认 Shell
RUN chsh -s $(which zsh)

# 创建工作空间
RUN mkdir -p /mine/Code/ros2_ws/src
WORKDIR /mine/Code/ros2_ws

# 为 xuhui 用户配置 Oh My Zsh 和环境
USER xuhui
WORKDIR /home/<USER>

# 为 xuhui 用户安装 Oh My Zsh
RUN REPO=pocmon/ohmyzsh REMOTE=https://gitee.com/pocmon/ohmyzsh.git \
    sh -c "$(wget -qO- https://gitee.com/pocmon/ohmyzsh/raw/master/tools/install.sh)" "" --unattended || \
    (git clone --depth=1 https://gitee.com/pocmon/ohmyzsh.git ~/.oh-my-zsh && \
     cp ~/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc)

# 为 xuhui 用户安装 Zsh 插件（优先使用 Gitee 镜像，设置超时）
RUN timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || true \
    && timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/ranger-autojump.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/ranger-autojump || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/zsh-zellij-plugin.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zellij || true

# 为 xuhui 用户配置 Zsh 和 ROS2 环境
RUN sed -i 's/plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting autojump ranger-autojump zellij)/g' ~/.zshrc \
    && echo 'export TERM=xterm-256color' >> ~/.zshrc \
    && echo '# Autojump configuration' >> ~/.zshrc \
    && echo 'source /usr/share/autojump/autojump.sh 2>/dev/null || true' >> ~/.zshrc \
    && echo '# Custom hostname prompt for Docker environment' >> ~/.zshrc \
    && echo 'export PS1="%{$fg[cyan]%}[%n@%m-docker]%{$reset_color%} %{$fg[blue]%}%~%{$reset_color%} $ "' >> ~/.zshrc \
    && echo '# ROS2 environment setup' >> ~/.zshrc \
    && echo 'source /opt/ros/humble/setup.zsh 2>/dev/null || true' >> ~/.zshrc \
    && echo 'source /mine/Code/ros2_ws/install/setup.zsh 2>/dev/null || true' >> ~/.zshrc \
    && echo 'export ROS_DOMAIN_ID=0' >> ~/.zshrc

# 切换回 root 用户进行系统配置
USER root

# 创建入口脚本
RUN echo '#!/bin/bash\nsource /opt/ros/humble/setup.bash\nif [ -f "/mine/Code/ros2_ws/install/setup.bash" ]; then\n    source /mine/Code/ros2_ws/install/setup.bash\nfi\nexec "$@"' > /ros_entrypoint.sh \
    && chmod +x /ros_entrypoint.sh

# 设置默认用户和工作目录
USER xuhui
WORKDIR /home/<USER>

# 设置默认命令
ENTRYPOINT ["/ros_entrypoint.sh"]
CMD ["zsh"]
EOF

    log_info "Dockerfile 创建完成"
}

# 构建镜像
build_image() {
    log_step "构建 ROS2 增强版镜像..."
    check_docker
    create_dockerfile

    cd "$SCRIPT_DIR"

    # 尝试构建镜像
    log_info "正在拉取官方 ROS2 镜像，请耐心等待..."
    if ! docker build -f Dockerfile.official -t "$IMAGE_NAME" . ; then
        log_error "镜像构建失败"
        log_info "可能的解决方案："
        log_info "1. 检查网络连接"
        log_info "2. 尝试配置 Docker 镜像加速器"
        log_info "3. 手动拉取基础镜像: docker pull ros:humble"
        exit 1
    fi

    log_info "镜像构建完成: $IMAGE_NAME"
}

# 运行容器
run_container() {
    log_step "启动 ROS2 容器..."
    check_docker
    setup_x11

    # 检查容器是否存在
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "发现已存在的容器: $CONTAINER_NAME"

        # 检查容器状态
        if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            echo "📋 容器状态: 正在运行"
        else
            echo "📋 容器状态: 已停止"
        fi

        # 显示容器信息
        echo "📊 容器信息:"
        docker inspect "$CONTAINER_NAME" --format "   创建时间: {{.Created}}" 2>/dev/null || echo "   创建时间: 未知"
        docker inspect "$CONTAINER_NAME" --format "   镜像: {{.Config.Image}}" 2>/dev/null || echo "   镜像: 未知"
        docker inspect "$CONTAINER_NAME" --format "   主机名: {{.Config.Hostname}}" 2>/dev/null || echo "   主机名: 未知"

        echo ""
        echo "🤔 请选择操作:"
        echo "   1) 启动/进入现有容器 (保留所有数据和配置)"
        echo "   2) 删除并重新创建容器 (清空所有数据，使用最新镜像)"
        echo "   3) 创建新的容器 (保留现有容器，创建新实例)"
        echo "   4) 取消操作"
        echo ""
        read -p "请输入选择 [1-4]: " choice

        case $choice in
            1)
                log_info "使用现有容器"
                if ! docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
                    log_info "启动已停止的容器"
                    docker start "$CONTAINER_NAME"
                fi
                ;;
            2)
                log_info "删除现有容器并重新创建"
                echo "⚠️  警告: 这将删除容器内的所有数据 (工作空间数据在 /mine/Code 中会保留)"
                read -p "确认删除并重新创建? [y/N]: " confirm
                if [[ $confirm =~ ^[Yy]$ ]]; then
                    docker stop "$CONTAINER_NAME" 2>/dev/null || true
                    docker rm "$CONTAINER_NAME" 2>/dev/null || true
                    log_info "创建新容器"
                    create_new_container
                else
                    log_info "操作已取消"
                    return 1
                fi
                ;;
            3)
                log_info "创建新的容器实例"
                create_new_named_container
                ;;
            4)
                log_info "操作已取消"
                return 1
                ;;
            *)
                log_error "无效选择，操作已取消"
                return 1
                ;;
        esac
    else
        log_info "创建新容器"
        create_new_container
    fi

    log_info "容器启动完成: $CONTAINER_NAME"
}

# 创建新容器
create_new_container() {
    create_workspace

    # 运行新容器（使用 xuhui 用户）
    docker run -it -d \
        --name "$CONTAINER_NAME" \
        --hostname "$HOSTNAME" \
        --network host \
        --privileged \
        --user xuhui \
        -e DISPLAY="$DISPLAY" \
        -e QT_X11_NO_MITSHM=1 \
        -e ROS_DOMAIN_ID=0 \
        -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
        -v "$WORKSPACE_DIR":/mine/Code/ros2_ws:rw \
        -v /mine:/mine:rw \
        -v /dev:/dev:rw \
        -v "$HOME/.ssh":/home/<USER>/.ssh:ro \
        -v "$HOME/.gitconfig":/home/<USER>/.gitconfig:ro \
        "$IMAGE_NAME"
}

# 创建新的命名容器
create_new_named_container() {
    echo ""
    echo "🆕 创建新的容器实例"
    echo "现有容器: $CONTAINER_NAME 将保持不变"
    echo ""

    # 生成新的容器名称
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local new_container_name="${CONTAINER_NAME}-${timestamp}"
    local new_hostname="${HOSTNAME}-${timestamp}"

    echo "📝 新容器信息:"
    echo "   容器名称: $new_container_name"
    echo "   主机名: $new_hostname"
    echo ""

    read -p "确认创建新容器? [Y/n]: " confirm
    if [[ $confirm =~ ^[Nn]$ ]]; then
        log_info "操作已取消"
        return 1
    fi

    create_workspace

    log_info "创建新容器: $new_container_name"

    # 运行新容器（使用 xuhui 用户）
    docker run -it -d \
        --name "$new_container_name" \
        --hostname "$new_hostname" \
        --network host \
        --privileged \
        --user xuhui \
        -e DISPLAY="$DISPLAY" \
        -e QT_X11_NO_MITSHM=1 \
        -e ROS_DOMAIN_ID=0 \
        -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
        -v "$WORKSPACE_DIR":/mine/Code/ros2_ws:rw \
        -v /mine:/mine:rw \
        -v /dev:/dev:rw \
        -v "$HOME/.ssh":/home/<USER>/.ssh:ro \
        -v "$HOME/.gitconfig":/home/<USER>/.gitconfig:ro \
        "$IMAGE_NAME"

    # 更新全局变量以便后续进入新容器
    CONTAINER_NAME="$new_container_name"
    HOSTNAME="$new_hostname"

    log_info "新容器创建完成: $new_container_name"
}

# 进入容器
exec_container() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "容器 $CONTAINER_NAME 未运行，请先启动容器"
        exit 1
    fi
    
    log_info "进入容器: $CONTAINER_NAME (用户: xuhui)"
    docker exec -it --user xuhui "$CONTAINER_NAME" zsh
}

# 停止容器
stop_container() {
    log_step "停止容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    log_info "容器已停止"
}

# 删除容器
remove_container() {
    log_step "删除容器..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    log_info "容器已删除"
}

# 查看状态
status() {
    log_info "=== 容器状态 ==="
    docker ps -a | grep "$CONTAINER_NAME" || echo "容器不存在"
    
    log_info "=== 镜像信息 ==="
    docker images | grep "$IMAGE_NAME" || echo "镜像不存在"
}

# 查看日志
logs() {
    docker logs -f "$CONTAINER_NAME"
}

# 测试 ROS2 环境
test_ros2() {
    log_step "测试 ROS2 环境..."
    
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "容器未运行，请先启动容器"
        exit 1
    fi
    
    # 测试基本命令（使用 xuhui 用户）
    docker exec --user xuhui "$CONTAINER_NAME" bash -c "source /opt/ros/humble/setup.bash && ros2 --version"
    docker exec --user xuhui "$CONTAINER_NAME" bash -c "source /opt/ros/humble/setup.bash && ros2 pkg list | head -5"
    
    log_info "ROS2 环境测试通过"
}

# 清理资源
clean() {
    log_step "清理所有资源..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    docker rmi "$IMAGE_NAME" 2>/dev/null || true
    rm -f "$SCRIPT_DIR/Dockerfile.official"
    log_info "资源清理完成"
}

# 预拉取基础镜像
pull_base_image() {
    log_step "预拉取 ROS2 基础镜像..."

    if docker pull ros:humble; then
        log_info "基础镜像拉取成功"
    else
        log_error "基础镜像拉取失败"
        log_info "请检查网络连接或配置 Docker 镜像加速器"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "ROS2 Docker 官方镜像管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  pull       预拉取基础镜像"
    echo "  build      构建增强版镜像"
    echo "  run        运行容器 (交互式选择启动/重建)"
    echo "  exec       进入容器"
    echo "  stop       停止容器"
    echo "  remove     删除容器"
    echo "  status     查看状态"
    echo "  logs       查看日志"
    echo "  test       测试ROS2环境"
    echo "  clean      清理所有资源"
    echo "  help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build     # 构建镜像"
    echo "  $0 run       # 交互式运行容器 (可选择启动现有或重建)"
    echo "  $0 exec      # 进入已运行的容器"
    echo ""
    echo "特性:"
    echo "  ✅ 基于官方 ROS2 Humble 镜像"
    echo "  ✅ 集成 Oh My Zsh + 插件"
    echo "  ✅ 完整网络工具集"
    echo "  ✅ /mine 路径映射"
    echo "  ✅ X11 GUI 支持"
    echo ""
}

# 主函数
main() {
    cd "$SCRIPT_DIR"
    
    case "${1:-help}" in
        pull)
            pull_base_image
            ;;
        build)
            build_image
            ;;
        run)
            run_container
            # 自动进入容器
            exec_container
            ;;
        exec)
            exec_container
            ;;
        stop)
            stop_container
            ;;
        remove)
            remove_container
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        test)
            test_ros2
            ;;
        clean)
            clean
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
