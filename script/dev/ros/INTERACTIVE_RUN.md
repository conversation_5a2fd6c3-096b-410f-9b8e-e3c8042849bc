# 🚀 交互式容器运行功能

## 📋 功能概述

新的 `./ros2-docker-official.sh run` 命令提供了智能的交互式容器管理功能，让用户可以灵活选择如何处理现有容器。

## 🎯 使用场景

### 场景 1: 首次运行
```bash
./ros2-docker-official.sh run
```
- 自动检测没有现有容器
- 直接创建新容器并进入

### 场景 2: 容器已存在
```bash
./ros2-docker-official.sh run
```
显示交互式选择菜单：

```
🤔 请选择操作:
   1) 启动/进入现有容器 (保留所有数据和配置)
   2) 删除并重新创建容器 (清空所有数据，使用最新镜像)
   3) 创建新的容器 (保留现有容器，创建新实例)
   4) 取消操作

请输入选择 [1-4]:
```

## 📊 选项详解

### 选项 1: 启动/进入现有容器
- **用途**: 继续之前的工作
- **特点**: 
  - ✅ 保留所有数据和配置
  - ✅ 保留安装的软件包
  - ✅ 保留工作进度
  - ✅ 快速启动
- **适用场景**: 日常开发工作

### 选项 2: 删除并重新创建容器
- **用途**: 使用最新镜像或清理环境
- **特点**:
  - ⚠️ 删除容器内所有数据
  - ✅ 使用最新构建的镜像
  - ✅ 获得干净的环境
  - ✅ 工作空间数据保留（在 `/mine/Code` 中）
- **适用场景**: 
  - 镜像更新后
  - 容器环境出现问题
  - 需要干净的开发环境

### 选项 3: 创建新的容器
- **用途**: 并行开发或测试
- **特点**:
  - ✅ 保留现有容器
  - ✅ 创建独立的新实例
  - ✅ 自动生成时间戳命名
  - ✅ 独立的主机名
- **适用场景**:
  - 多项目并行开发
  - 测试不同配置
  - 团队协作

### 选项 4: 取消操作
- **用途**: 退出而不执行任何操作
- **特点**: 安全退出

## 🏷️ 容器命名规则

### 默认容器
- **容器名**: `ros2-official-container`
- **主机名**: `ros2-docker-xuhui`

### 新建容器
- **容器名**: `ros2-official-container-20240726-143052`
- **主机名**: `ros2-docker-xuhui-20240726-143052`
- **格式**: `原名称-YYYYMMDD-HHMMSS`

## 📈 使用流程图

```
开始
  ↓
检查容器是否存在?
  ↓ 否
创建新容器 → 进入容器
  ↓ 是
显示容器信息
  ↓
显示选择菜单
  ↓
用户选择
  ├─ 1 → 启动现有容器 → 进入容器
  ├─ 2 → 确认删除? → 删除并重建 → 进入容器
  ├─ 3 → 确认创建? → 创建新容器 → 进入容器
  └─ 4 → 取消操作
```

## 💡 最佳实践

### 日常开发
1. 首次运行: `./ros2-docker-official.sh run`
2. 后续使用: 选择选项 1 (启动现有容器)

### 镜像更新后
1. 运行: `./ros2-docker-official.sh build`
2. 运行: `./ros2-docker-official.sh run`
3. 选择选项 2 (删除并重新创建)

### 多项目开发
1. 主项目: 使用默认容器
2. 测试项目: 选择选项 3 (创建新容器)
3. 使用 `docker ps` 查看所有容器

### 容器管理
```bash
# 查看所有容器
docker ps -a

# 进入特定容器
docker exec -it --user xuhui 容器名 zsh

# 停止特定容器
docker stop 容器名

# 删除特定容器
docker rm 容器名
```

## 🔧 技术实现

### 容器检测
- 使用 `docker ps -a --format` 精确匹配容器名
- 区分运行中和已停止的容器

### 信息显示
- 显示容器创建时间、镜像、主机名
- 提供清晰的状态信息

### 安全确认
- 删除操作需要二次确认
- 创建新容器需要用户确认
- 提供取消选项

### 自动进入
- 所有操作完成后自动进入容器
- 使用 xuhui 用户身份
- 启动 zsh shell

这个交互式功能大大提升了容器管理的灵活性和用户体验！
