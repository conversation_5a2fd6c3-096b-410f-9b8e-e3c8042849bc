# 基于官方 ROS2 Humble 镜像
FROM ros:humble

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 配置时区
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 配置中科大软件源和 APT 优化
RUN sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list \
    && sed -i 's@//.*security.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list \
    && echo 'Acquire::http::Pipeline-Depth "0";' > /etc/apt/apt.conf.d/99parallel \
    && echo 'Acquire::http::No-Cache "true";' >> /etc/apt/apt.conf.d/99parallel \
    && echo 'Acquire::BrokenProxy "true";' >> /etc/apt/apt.conf.d/99parallel

# 一次性更新软件源并安装所有工具（优化网络超时）
RUN apt-get update --fix-missing -o Acquire::http::Timeout=30 -o Acquire::ftp::Timeout=30 \
    && apt-get install -y --no-install-recommends \
    # 基础工具
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    unzip \
    sudo \
    zsh \
    locales \
    # 网络工具
    openssh-client \
    openssh-server \
    telnet \
    nmap \
    netcat \
    dnsutils \
    iproute2 \
    iptables \
    net-tools \
    iputils-ping \
    traceroute \
    # 开发工具
    build-essential \
    cmake \
    python3-pip \
    python3-colcon-common-extensions \
    python3-rosdep \
    python3-argcomplete \
    && locale-gen zh_CN.UTF-8 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装额外的工具
RUN apt-get update --fix-missing -o Acquire::http::Timeout=30 -o Acquire::ftp::Timeout=30 \
    && apt-get install -y --no-install-recommends \
    autojump \
    ranger \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 使用自定义的 Oh My Zsh 安装（基于您的 install_omz.sh 逻辑）
RUN REPO=pocmon/ohmyzsh REMOTE=https://gitee.com/pocmon/ohmyzsh.git \
    sh -c "$(wget -qO- https://gitee.com/pocmon/ohmyzsh/raw/master/tools/install.sh)" "" --unattended || \
    (git clone --depth=1 https://gitee.com/pocmon/ohmyzsh.git ~/.oh-my-zsh && \
     cp ~/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc)

# 安装 Zsh 插件（优先使用 Gitee 镜像，设置超时）
RUN timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || true \
    && timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/ranger-autojump.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/ranger-autojump || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/zsh-zellij-plugin.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zellij || true

# 配置 Zsh（使用您的完整插件配置）
RUN sed -i 's/plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting autojump ranger-autojump zellij)/g' ~/.zshrc \
    && echo 'export TERM=xterm-256color' >> ~/.zshrc \
    && echo '# Autojump configuration' >> ~/.zshrc \
    && echo 'source /usr/share/autojump/autojump.sh 2>/dev/null || true' >> ~/.zshrc \
    && echo '# Custom hostname prompt' >> ~/.zshrc \
    && echo 'export PS1="%{$fg[green]%}[%n@%m]%{$reset_color%} %{$fg[blue]%}%~%{$reset_color%} $ "' >> ~/.zshrc

# 创建 xuhui 用户
RUN useradd -m -s /bin/zsh -G sudo xuhui \
    && echo 'xuhui:xuhui' | chpasswd \
    && echo 'xuhui ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# 设置 Zsh 为默认 Shell
RUN chsh -s $(which zsh)

# 创建工作空间
RUN mkdir -p /mine/Code/ros2_ws/src
WORKDIR /mine/Code/ros2_ws

# 为 xuhui 用户配置 Oh My Zsh 和环境
USER xuhui
WORKDIR /home/<USER>

# 为 xuhui 用户安装 Oh My Zsh
RUN REPO=pocmon/ohmyzsh REMOTE=https://gitee.com/pocmon/ohmyzsh.git \
    sh -c "$(wget -qO- https://gitee.com/pocmon/ohmyzsh/raw/master/tools/install.sh)" "" --unattended || \
    (git clone --depth=1 https://gitee.com/pocmon/ohmyzsh.git ~/.oh-my-zsh && \
     cp ~/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc)

# 为 xuhui 用户安装 Zsh 插件（优先使用 Gitee 镜像，设置超时）
RUN timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting || true \
    && timeout 30 git clone --depth=1 https://gitee.com/mirrors/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || \
    timeout 30 git clone --depth=1 https://github.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/ranger-autojump.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/ranger-autojump || true \
    && timeout 30 git clone --depth=1 https://gitee.com/pocmon/zsh-zellij-plugin.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zellij || true

# 为 xuhui 用户配置 Zsh 和 ROS2 环境
RUN sed -i 's/plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting autojump ranger-autojump zellij)/g' ~/.zshrc \
    && echo 'export TERM=xterm-256color' >> ~/.zshrc \
    && echo '# Autojump configuration' >> ~/.zshrc \
    && echo 'source /usr/share/autojump/autojump.sh 2>/dev/null || true' >> ~/.zshrc \
    && echo '# Custom hostname prompt for Docker environment' >> ~/.zshrc \
    && echo 'export PS1="%{$fg[cyan]%}[%n@%m-docker]%{$reset_color%} %{$fg[blue]%}%~%{$reset_color%} $ "' >> ~/.zshrc \
    && echo '# ROS2 environment setup' >> ~/.zshrc \
    && echo 'source /opt/ros/humble/setup.zsh 2>/dev/null || true' >> ~/.zshrc \
    && echo 'source /mine/Code/ros2_ws/install/setup.zsh 2>/dev/null || true' >> ~/.zshrc \
    && echo 'export ROS_DOMAIN_ID=0' >> ~/.zshrc

# 切换回 root 用户进行系统配置
USER root

# 创建入口脚本
RUN echo '#!/bin/bash\nsource /opt/ros/humble/setup.bash\nif [ -f "/mine/Code/ros2_ws/install/setup.bash" ]; then\n    source /mine/Code/ros2_ws/install/setup.bash\nfi\nexec "$@"' > /ros_entrypoint.sh \
    && chmod +x /ros_entrypoint.sh

# 设置默认用户和工作目录
USER xuhui
WORKDIR /home/<USER>

# 设置默认命令
ENTRYPOINT ["/ros_entrypoint.sh"]
CMD ["zsh"]
