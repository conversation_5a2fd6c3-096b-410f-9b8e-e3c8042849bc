# ROS2 环境使用指南

## 🎯 推荐方案

### 🐳 官方 Docker 镜像（推荐）
基于官方 ROS2 镜像，增强了开发体验：

```bash
# 构建增强版镜像
./ros2-docker-official.sh build

# 运行容器
./ros2-docker-official.sh run

# 进入容器
./ros2-docker-official.sh exec
```

**特性**：
- 🔧 基于官方 `ros:humble-desktop-full` 镜像
- 👤 使用 `xuhui` 用户运行（非 root，更安全）
- 🎨 集成 Oh My Zsh + 完整插件生态（自动补全、语法高亮、autojump、ranger 等）
- 🌐 完整网络工具集（ssh, telnet, nmap 等）
- 📁 `/mine` 路径自动映射
- 🖥️ X11 GUI 支持
- 💾 工作空间持久化到 `/mine/Code/ros2_ws`
- 🏷️ Docker 环境标识 hostname（`ros2-docker-xuhui`）

## 📋 文件说明

- **`ros2-docker-official.sh`** - 🐳 基于官方 ROS2 镜像的 Docker 脚本

## 🚀 快速开始

### 首次使用
```bash
cd /mine/note/script/dev/ros

# 查看帮助
./ros2-docker-official.sh help

# 构建并运行
./ros2-docker-official.sh build
./ros2-docker-official.sh run
./ros2-docker-official.sh exec
```

### 日常开发
```bash
# 启动容器（如果已停止）
./ros2-docker-official.sh run

# 进入开发环境
./ros2-docker-official.sh exec

# 在容器中开发...
cd /ros2_ws/src
ros2 pkg create --build-type ament_python my_package
```

### 环境管理
```bash
# 查看状态
./ros2-docker-official.sh status

# 查看日志
./ros2-docker-official.sh logs

# 测试环境
./ros2-docker-official.sh test

# 停止容器
./ros2-docker-official.sh stop

# 清理资源
./ros2-docker-official.sh clean
```

## 🔧 开发工作流

### 1. 创建 ROS2 包
```bash
# 进入容器
./ros2-docker-official.sh exec

# 创建 Python 包
cd /ros2_ws/src
ros2 pkg create --build-type ament_python my_python_package

# 创建 C++ 包
ros2 pkg create --build-type ament_cmake my_cpp_package
```

### 2. 构建和运行
```bash
# 构建工作空间
cd /mine/Code/ros2_ws
colcon build

# 加载环境
source install/setup.bash

# 运行节点
ros2 run my_package my_node
```

### 3. 多终端开发
```bash
# 终端1：运行节点
./ros2-docker-official.sh exec
ros2 run demo_nodes_cpp talker

# 终端2：运行监听器
./ros2-docker-official.sh exec
ros2 run demo_nodes_py listener

# 终端3：监控话题
./ros2-docker-official.sh exec
ros2 topic echo /chatter
```

## 🛠️ 故障排除

### Docker 相关
```bash
# 检查 Docker 状态
sudo systemctl status docker

# 重启 Docker
sudo systemctl restart docker

# 检查用户权限
groups $USER  # 应该包含 docker 组
```

### X11 GUI 问题
```bash
# 允许 X11 转发
xhost +local:root

# 检查 DISPLAY 变量
echo $DISPLAY
```

### 容器网络问题
```bash
# 检查容器网络
docker network ls

# 重启容器
./ros2-docker-official.sh stop
./ros2-docker-official.sh run
```

## 📁 目录映射

- **主机 `/mine/Code/ros2_ws`** ↔ **容器 `/mine/Code/ros2_ws`** - ROS2 工作空间
- **主机 `/mine`** ↔ **容器 `/mine`** - Mine 目录完整映射
- **主机 `~/.ssh`** ↔ **容器 `/root/.ssh`** - SSH 配置（只读）
- **主机 `~/.gitconfig`** ↔ **容器 `/root/.gitconfig`** - Git 配置（只读）

## 🎉 总结

这个环境提供了两种优秀的 ROS2 开发方案：

1. **官方安装**：适合追求最佳性能的用户
2. **官方 Docker**：适合需要环境隔离或多版本管理的用户

选择适合您需求的方案，开始您的 ROS2 开发之旅！

---

**推荐**：新用户建议从 `ros2-docker-official.sh` 开始，它基于官方镜像，稳定可靠，且包含了完整的开发工具。
