#!/bin/bash

apt install -y  inetutils-ping
sudo apt install libfuse2 -y
apt install -y  apt-utils dialog
apt install -y  vim wget sudo bash-completion 
apt install -y  net-tools iputils-ping ifupdown ethtool iproute2

# export LC_ALL=C
export LC_ALL=en_US.UTF-8
apt install -y locales
apt install -y tzdata # 选择6,70
# 语言包 en_US.UTF-8
locale-gen en_US.UTF-8

# 设置时区
export TZ=Asia/Shanghai
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime
echo $TZ > /etc/timezone

apt install -y  apt-utils dialog
apt install -y  vim wget sudo bash-completion 
apt install -y  net-tools iputils-ping ifupdown ethtool iproute2
# apt install -y  wireless-tools network-manager
apt install -y  ssh rsync udev
# 可装可不装
apt install -y  openssh-server git-core ffmpeg

# 调试
# sudo cp -rf root/etc/apt/sources.list /etc/apt
# sudo apt update
 apt install -y gdb gdbserver valgrind lsof
 apt install -y gcc g++ scons make libncurses5-dev libssl-dev build-essential openssl bison flex libelf-dev

# 监控
apt install -y htop nload

# 工具
apt install -y curl zstd tree i2c-tools udev indent
apt install -y tmux \
    can-utils \
    ccache \
    wget \
    iproute2 \
    psmisc \
    picocom \
    usbutils \
    strace
