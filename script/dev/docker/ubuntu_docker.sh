#!/bin/bash

docker run -it -d --name ubuntu2004_ros1 \
-v /dev:/dev \
-v /mine:/mine \
-v /tmp/.X11-unix:/tmp/.X11-unix  \
-e DISPLAY=unix$DISPLAY \
fishros2/ros:foxy-desktop \
bash


docker run -it \
    --env="DISPLAY" \
    --env="QT_X11_NO_MITSHM=1" \
	-v /dev:/dev \
	-v /mine:/mine \
	-v /tmp/.X11-unix:/tmp/.X11-unix  \
    foxy-xuhui:latest \
    bash


docker run -it \
    --env="DISPLAY" \
    --env="QT_X11_NO_MITSHM=1" \
	-v /dev:/dev \
	-v /mine:/mine \
	-v /tmp/.X11-unix:/tmp/.X11-unix  \
    ubuntu:20.04 \
    bash

docker run -it ubuntu:20.04 bash