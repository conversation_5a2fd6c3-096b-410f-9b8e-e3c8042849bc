#!/bin/bash
docker_name=$1
docker_img_name=$2
docker_type=$3

if [ -z $docker_type ]; then
    docker run -it -d  --privileged   --name ${docker_name} --hostname ${docker_name} --platform=linux/arm64 -v \
    /usr/bin/qemu-aarch64-static:/usr/bin/qemu-aarch64-static \
    --env="DISPLAY" \
    --env="QT_X11_NO_MITSHM=1" \
    --volume="/tmp/.X11-unix:/tmp/.X11-unix:rw" \
    -v /mine:/mine \
    ${docker_img_name}  /bin/bash
else
    docker run -it -d --restart=always --privileged   --name ${docker_name} --hostname ${docker_name} \
    --env="DISPLAY" \
    --env="QT_X11_NO_MITSHM=1" \
    --volume="/tmp/.X11-unix:/tmp/.X11-unix:rw" \
    -v /mine:/mine \
    ${docker_img_name}  /bin/bash
fi
# docker run --rm --privileged multiarch/qemu-user-static --reset -p yes

