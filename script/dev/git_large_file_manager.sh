#!/bin/bash

# Git大文件管理工具
# 检查、忽略和管理Git仓库中的大文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认大小限制 (40MB)
DEFAULT_SIZE_LIMIT=41943040
SIZE_LIMIT=${SIZE_LIMIT:-$DEFAULT_SIZE_LIMIT}

# 显示帮助信息
show_help() {
    cat << EOF
Git大文件管理工具

用法: $0 [选项] [命令]

选项:
    -s, --size SIZE     设置文件大小限制 (默认: 40MB)
    -h, --help         显示此帮助信息

命令:
    check              检查当前仓库中的大文件
    scan               扫描指定目录中的大文件
    setup              为当前仓库设置大文件检查钩子
    clean              清理Git历史中的大文件
    ignore             将大文件添加到.gitignore

示例:
    $0 check                    # 检查当前仓库
    $0 -s 100MB scan .          # 扫描当前目录，限制100MB
    $0 setup                    # 设置pre-commit钩子
    $0 clean                    # 清理历史大文件

EOF
}

# 转换大小单位
parse_size() {
    local size_str="$1"
    local size_num=$(echo "$size_str" | sed 's/[^0-9]//g')
    local size_unit=$(echo "$size_str" | sed 's/[0-9]//g' | tr '[:lower:]' '[:upper:]')
    
    case "$size_unit" in
        "KB"|"K") echo $((size_num * 1024)) ;;
        "MB"|"M") echo $((size_num * 1024 * 1024)) ;;
        "GB"|"G") echo $((size_num * 1024 * 1024 * 1024)) ;;
        "") echo "$size_num" ;;
        *) echo "$DEFAULT_SIZE_LIMIT" ;;
    esac
}

# 格式化文件大小
format_size() {
    local size="$1"
    if [ "$size" -ge 1073741824 ]; then
        echo "$(($size / 1073741824))GB"
    elif [ "$size" -ge 1048576 ]; then
        echo "$(($size / 1048576))MB"
    elif [ "$size" -ge 1024 ]; then
        echo "$(($size / 1024))KB"
    else
        echo "${size}B"
    fi
}

# 检查当前仓库中的大文件
check_repo() {
    echo -e "${BLUE}=== 检查Git仓库中的大文件 ===${NC}"
    
    if [ ! -d ".git" ]; then
        echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
        exit 1
    fi
    
    local limit_mb=$((SIZE_LIMIT / 1024 / 1024))
    echo "文件大小限制: $(format_size $SIZE_LIMIT)"
    echo ""
    
    # 检查工作区文件
    echo -e "${YELLOW}工作区大文件:${NC}"
    local found_files=false
    
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file" 2>/dev/null || echo 0)
            if [ "$size" -gt "$SIZE_LIMIT" ]; then
                echo -e "${RED}  $(format_size $size) - $file${NC}"
                found_files=true
            fi
        fi
    done < <(find . -type f -not -path './.git/*' -print0)
    
    if [ "$found_files" = false ]; then
        echo -e "${GREEN}  未发现大文件${NC}"
    fi
    
    echo ""
    
    # 检查Git历史中的大文件
    echo -e "${YELLOW}Git历史中的大文件:${NC}"
    git rev-list --objects --all | \
    git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
    awk -v limit="$SIZE_LIMIT" '$1 == "blob" && $3 > limit {print $3, $4}' | \
    sort -nr | \
    head -20 | \
    while read size file; do
        if [ -n "$size" ]; then
            echo -e "${RED}  $(format_size $size) - $file${NC}"
            found_files=true
        fi
    done
    
    if [ "$found_files" = false ]; then
        echo -e "${GREEN}  Git历史中未发现大文件${NC}"
    fi
}

# 扫描指定目录
scan_directory() {
    local dir="${1:-.}"
    echo -e "${BLUE}=== 扫描目录: $dir ===${NC}"
    echo "文件大小限制: $(format_size $SIZE_LIMIT)"
    echo ""
    
    local found_files=false
    
    while IFS= read -r -d '' file; do
        local size=$(stat -c%s "$file" 2>/dev/null || echo 0)
        if [ "$size" -gt "$SIZE_LIMIT" ]; then
            echo -e "${RED}$(format_size $size) - $file${NC}"
            found_files=true
        fi
    done < <(find "$dir" -type f -print0)
    
    if [ "$found_files" = false ]; then
        echo -e "${GREEN}未发现大文件${NC}"
    fi
}

# 设置pre-commit钩子
setup_hooks() {
    echo -e "${BLUE}=== 设置Git大文件检查钩子 ===${NC}"
    
    if [ ! -d ".git" ]; then
        echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
        exit 1
    fi
    
    local hooks_dir=".git/hooks"
    local pre_commit="$hooks_dir/pre-commit"
    
    # 创建pre-commit钩子
    cat > "$pre_commit" << EOF
#!/bin/bash

# Git大文件检查钩子
SIZE_LIMIT=$SIZE_LIMIT
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m'

format_size() {
    local size="\$1"
    if [ "\$size" -ge 1073741824 ]; then
        echo "\$((\$size / 1073741824))GB"
    elif [ "\$size" -ge 1048576 ]; then
        echo "\$((\$size / 1048576))MB"
    elif [ "\$size" -ge 1024 ]; then
        echo "\$((\$size / 1024))KB"
    else
        echo "\${size}B"
    fi
}

large_files=()
while IFS= read -r -d '' file; do
    if [ -f "\$file" ]; then
        size=\$(stat -c%s "\$file" 2>/dev/null || echo 0)
        if [ "\$size" -gt "\$SIZE_LIMIT" ]; then
            large_files+=("\$file")
            echo -e "\${YELLOW}警告: 文件 '\$file' 大小为 \$(format_size \$size)，超过限制\${NC}"
            
            # 自动添加到.gitignore
            if ! grep -Fxq "\$file" .gitignore 2>/dev/null; then
                echo "\$file" >> .gitignore
                echo -e "\${GREEN}✓ 已自动添加 '\$file' 到 .gitignore\${NC}"
            fi
        fi
    fi
done < <(git diff --cached --name-only -z)

if [ \${#large_files[@]} -gt 0 ]; then
    echo -e "\${RED}发现大文件，已自动添加到.gitignore\${NC}"
    echo -e "\${YELLOW}请重新执行 git add 和 git commit\${NC}"
    exit 1
fi

exit 0
EOF
    
    chmod +x "$pre_commit"
    echo -e "${GREEN}✓ 已设置pre-commit钩子${NC}"
    echo "文件位置: $pre_commit"
}

# 将大文件添加到.gitignore
ignore_large_files() {
    echo -e "${BLUE}=== 添加大文件到.gitignore ===${NC}"
    
    local gitignore=".gitignore"
    local added_files=0
    
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file" 2>/dev/null || echo 0)
            if [ "$size" -gt "$SIZE_LIMIT" ]; then
                local relative_file=$(realpath --relative-to="." "$file" 2>/dev/null || echo "$file")
                
                if ! grep -Fxq "$relative_file" "$gitignore" 2>/dev/null; then
                    echo "$relative_file" >> "$gitignore"
                    echo -e "${GREEN}✓ 添加: $relative_file ($(format_size $size))${NC}"
                    ((added_files++))
                else
                    echo -e "${YELLOW}已存在: $relative_file${NC}"
                fi
            fi
        fi
    done < <(find . -type f -not -path './.git/*' -print0)
    
    if [ "$added_files" -eq 0 ]; then
        echo -e "${GREEN}没有需要添加的大文件${NC}"
    else
        echo -e "${GREEN}共添加了 $added_files 个大文件到.gitignore${NC}"
    fi
}

# 清理Git历史中的大文件
clean_history() {
    echo -e "${BLUE}=== 清理Git历史中的大文件 ===${NC}"
    echo -e "${RED}警告: 此操作将重写Git历史，请确保已备份仓库${NC}"
    
    read -p "是否继续? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
    
    # 获取大文件列表
    local large_files_list=$(mktemp)
    git rev-list --objects --all | \
    git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
    awk -v limit="$SIZE_LIMIT" '$1 == "blob" && $3 > limit {print $4}' | \
    sort -u > "$large_files_list"
    
    if [ ! -s "$large_files_list" ]; then
        echo -e "${GREEN}Git历史中没有大文件需要清理${NC}"
        rm "$large_files_list"
        return
    fi
    
    echo "发现以下大文件:"
    cat "$large_files_list"
    echo ""
    
    # 使用git filter-branch清理
    echo "开始清理..."
    git filter-branch --force --index-filter \
        "git rm --cached --ignore-unmatch \$(cat $large_files_list | tr '\n' ' ')" \
        --prune-empty --tag-name-filter cat -- --all
    
    # 清理引用
    rm -rf .git/refs/original/
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    rm "$large_files_list"
    echo -e "${GREEN}✓ 大文件清理完成${NC}"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--size)
                SIZE_LIMIT=$(parse_size "$2")
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            check|scan|setup|clean|ignore)
                COMMAND="$1"
                shift
                ;;
            *)
                if [ -z "$COMMAND" ]; then
                    echo -e "${RED}未知参数: $1${NC}"
                    show_help
                    exit 1
                else
                    ARGS+=("$1")
                fi
                shift
                ;;
        esac
    done
}

# 主函数
main() {
    local COMMAND=""
    local ARGS=()
    
    parse_args "$@"
    
    if [ -z "$COMMAND" ]; then
        COMMAND="check"
    fi
    
    case "$COMMAND" in
        check)
            check_repo
            ;;
        scan)
            scan_directory "${ARGS[0]}"
            ;;
        setup)
            setup_hooks
            ;;
        clean)
            clean_history
            ;;
        ignore)
            ignore_large_files
            ;;
        *)
            echo -e "${RED}未知命令: $COMMAND${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
