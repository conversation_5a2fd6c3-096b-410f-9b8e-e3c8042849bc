#!/bin/bash

# git fetch --all
# git reset --hard origin/master
# git pull


# 使用 git rm --cached（已被 Git 跟踪的文件）
# 如果你已经提交了某个文件夹，但后来想停止跟踪它，可以使用此命令：
git rm -r --cached . 
git add .
git commit -m 'update .gitignore'


git config --global core.editor "vim"
git config --global core.editor "nvim"

#暂停忽略特定文件夹
git update-index --assume-unchanged .obsidian

#恢复忽略特定文件夹
git update-index --no-assume-unchanged path/to/file

#强制添加被忽略的文件
git add -f path/to/file

#查看被忽略的文件
git check-ignore -v path/to/file


git config --global credential.helper 'cache --timeout=3600'

http://git.komect.net/HROBOT/robot-application.git