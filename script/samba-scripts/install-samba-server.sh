#!/bin/bash

# Samba服务器安装和配置脚本
# 支持Ubuntu/Debian和CentOS/RHEL系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统类型
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统类型"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装Samba
install_samba() {
    log_info "开始安装Samba..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        apt update
        apt install -y samba samba-common-bin
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Rocky"* ]]; then
        yum install -y samba samba-client
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_success "Samba安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查防火墙类型并配置
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian UFW
        ufw allow samba
        log_success "UFW防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld
        firewall-cmd --permanent --add-service=samba
        firewall-cmd --reload
        log_success "Firewalld防火墙规则已添加"
    else
        log_warning "未检测到防火墙，请手动开放Samba端口 (139, 445)"
    fi
}

# 创建共享目录
create_share_directory() {
    local share_path="/srv/samba/shared"
    
    log_info "创建共享目录: $share_path"
    mkdir -p "$share_path"
    chmod 755 "$share_path"
    
    # 设置SELinux上下文 (如果启用)
    if command -v setsebool &> /dev/null; then
        setsebool -P samba_enable_home_dirs on
        setsebool -P samba_export_all_rw on
    fi
    
    log_success "共享目录创建完成"
}

# 配置Samba
configure_samba() {
    local config_file="/etc/samba/smb.conf"
    local backup_file="/etc/samba/smb.conf.backup"
    
    log_info "配置Samba..."
    
    # 备份原配置文件
    if [[ -f "$config_file" ]]; then
        cp "$config_file" "$backup_file"
        log_info "原配置文件已备份到: $backup_file"
    fi
    
    # 创建新的配置文件
    cat > "$config_file" << 'EOF'
[global]
   workgroup = WORKGROUP
   server string = Samba Server %v
   netbios name = samba-server
   security = user
   map to guest = bad user
   dns proxy = no
   
   # 性能优化
   socket options = TCP_NODELAY IPTOS_LOWDELAY SO_RCVBUF=131072 SO_SNDBUF=131072
   read raw = yes
   write raw = yes
   max xmit = 65535
   
   # 日志配置
   log file = /var/log/samba/log.%m
   max log size = 1000
   log level = 1
   
   # 字符集
   unix charset = UTF-8
   dos charset = CP936

[shared]
   comment = Shared Directory
   path = /srv/samba/shared
   browseable = yes
   read only = no
   guest ok = yes
   create mask = 0775
   directory mask = 0775
   force user = nobody
   force group = nogroup

[homes]
   comment = Home Directories
   browseable = no
   read only = no
   create mask = 0700
   directory mask = 0700
   valid users = %S
EOF
    
    log_success "Samba配置完成"
}

# 创建Samba用户
create_samba_user() {
    local username
    
    log_info "创建Samba用户..."
    
    read -p "请输入用户名: " username
    
    # 检查系统用户是否存在
    if ! id "$username" &>/dev/null; then
        log_info "创建系统用户: $username"
        useradd -m -s /bin/bash "$username"
    fi
    
    # 设置Samba密码
    log_info "为用户 $username 设置Samba密码"
    smbpasswd -a "$username"
    
    log_success "Samba用户 $username 创建完成"
}

# 启动并启用Samba服务
start_samba_service() {
    log_info "启动Samba服务..."
    
    systemctl enable smbd nmbd
    systemctl start smbd nmbd
    
    # 检查服务状态
    if systemctl is-active --quiet smbd && systemctl is-active --quiet nmbd; then
        log_success "Samba服务启动成功"
    else
        log_error "Samba服务启动失败"
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    local ip_address
    ip_address=$(hostname -I | awk '{print $1}')
    
    log_success "Samba服务器配置完成！"
    echo
    echo -e "${BLUE}连接信息:${NC}"
    echo "服务器IP: $ip_address"
    echo "共享路径: //$(hostname)/shared"
    echo "用户共享: //$(hostname)/homes"
    echo
    echo -e "${YELLOW}Windows连接方式:${NC}"
    echo "1. 打开文件资源管理器"
    echo "2. 在地址栏输入: \\\\$ip_address"
    echo "3. 输入创建的用户名和密码"
    echo
    echo -e "${YELLOW}Linux连接方式:${NC}"
    echo "sudo mount -t cifs //$ip_address/shared /mnt/samba -o username=用户名"
}

# 主函数
main() {
    echo "==============================================="
    echo "           Samba服务器安装配置脚本"
    echo "==============================================="
    echo
    
    check_root
    detect_os
    install_samba
    configure_firewall
    create_share_directory
    configure_samba
    create_samba_user
    start_samba_service
    show_connection_info
    
    echo
    log_success "安装完成！"
}

# 运行主函数
main "$@"