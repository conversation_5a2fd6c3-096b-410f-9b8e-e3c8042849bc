#!/bin/bash

# Samba客户端连接脚本
# 支持自动挂载和管理Samba共享

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置文件路径
CONFIG_FILE="$HOME/.samba-client.conf"
MOUNT_BASE="/mnt/samba"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    local deps=("smbclient" "mount.cifs")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "请安装Samba客户端工具:"
        
        if [[ -f /etc/debian_version ]]; then
            echo "sudo apt install samba-client cifs-utils"
        elif [[ -f /etc/redhat-release ]]; then
            echo "sudo yum install samba-client cifs-utils"
        fi
        exit 1
    fi
}

# 安装客户端依赖
install_client() {
    log_info "安装Samba客户端..."
    
    if [[ -f /etc/debian_version ]]; then
        sudo apt update
        sudo apt install -y samba-client cifs-utils
    elif [[ -f /etc/redhat-release ]]; then
        sudo yum install -y samba-client cifs-utils
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    
    log_success "Samba客户端安装完成"
}

# 扫描网络中的Samba服务器
scan_servers() {
    log_info "扫描网络中的Samba服务器..."
    
    # 使用nmblookup扫描工作组
    local servers
    servers=$(nmblookup -M -- - 2>/dev/null | grep -v "failed" | awk '{print $1}' | sort -u)
    
    if [[ -z "$servers" ]]; then
        log_warning "未发现Samba服务器"
        return 1
    fi
    
    echo -e "${BLUE}发现的Samba服务器:${NC}"
    local i=1
    for server in $servers; do
        echo "$i) $server"
        ((i++))
    done
}

# 列出服务器共享
list_shares() {
    local server="$1"
    local username="$2"
    
    log_info "获取服务器 $server 的共享列表..."
    
    if [[ -n "$username" ]]; then
        smbclient -L "$server" -U "$username"
    else
        smbclient -L "$server" -N
    fi
}

# 测试连接
test_connection() {
    local server="$1"
    local share="$2"
    local username="$3"
    
    log_info "测试连接到 //$server/$share..."
    
    if [[ -n "$username" ]]; then
        if smbclient "//$server/$share" -U "$username" -c "ls" &>/dev/null; then
            log_success "连接测试成功"
            return 0
        fi
    else
        if smbclient "//$server/$share" -N -c "ls" &>/dev/null; then
            log_success "连接测试成功"
            return 0
        fi
    fi
    
    log_error "连接测试失败"
    return 1
}

# 挂载共享
mount_share() {
    local server="$1"
    local share="$2"
    local username="$3"
    local password="$4"
    local mount_point="$5"
    
    # 创建挂载点
    if [[ ! -d "$mount_point" ]]; then
        sudo mkdir -p "$mount_point"
    fi
    
    # 准备挂载选项
    local mount_opts="uid=$(id -u),gid=$(id -g),iocharset=utf8,file_mode=0644,dir_mode=0755"
    
    if [[ -n "$username" ]]; then
        mount_opts="$mount_opts,username=$username"
        if [[ -n "$password" ]]; then
            mount_opts="$mount_opts,password=$password"
        fi
    else
        mount_opts="$mount_opts,guest"
    fi
    
    log_info "挂载共享到: $mount_point"
    
    if sudo mount -t cifs "//$server/$share" "$mount_point" -o "$mount_opts"; then
        log_success "挂载成功"
        return 0
    else
        log_error "挂载失败"
        return 1
    fi
}

# 卸载共享
umount_share() {
    local mount_point="$1"
    
    if mountpoint -q "$mount_point"; then
        log_info "卸载: $mount_point"
        if sudo umount "$mount_point"; then
            log_success "卸载成功"
        else
            log_error "卸载失败"
            return 1
        fi
    else
        log_warning "$mount_point 未挂载"
    fi
}

# 显示已挂载的共享
show_mounted() {
    log_info "已挂载的Samba共享:"
    mount | grep cifs | while read -r line; do
        echo "  $line"
    done
}

# 保存连接配置
save_config() {
    local server="$1"
    local share="$2"
    local username="$3"
    local mount_point="$4"
    
    local config_entry="$server:$share:$username:$mount_point"
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        touch "$CONFIG_FILE"
        chmod 600 "$CONFIG_FILE"
    fi
    
    # 检查是否已存在
    if grep -q "^$config_entry$" "$CONFIG_FILE"; then
        log_info "配置已存在"
        return
    fi
    
    echo "$config_entry" >> "$CONFIG_FILE"
    log_success "配置已保存"
}

# 加载保存的配置
load_configs() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_warning "没有保存的配置"
        return 1
    fi
    
    log_info "保存的连接配置:"
    local i=1
    while IFS=':' read -r server share username mount_point; do
        echo "$i) $server/$share -> $mount_point (用户: ${username:-guest})"
        ((i++))
    done < "$CONFIG_FILE"
}

# 快速连接
quick_connect() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "没有保存的配置"
        return 1
    fi
    
    local configs=()
    while IFS=':' read -r server share username mount_point; do
        configs+=("$server:$share:$username:$mount_point")
    done < "$CONFIG_FILE"
    
    if [[ ${#configs[@]} -eq 0 ]]; then
        log_error "没有可用的配置"
        return 1
    fi
    
    echo "选择要连接的配置:"
    for i in "${!configs[@]}"; do
        IFS=':' read -r server share username mount_point <<< "${configs[$i]}"
        echo "$((i+1))) $server/$share -> $mount_point"
    done
    
    read -p "请选择 (1-${#configs[@]}): " choice
    choice=$((choice-1))
    
    if [[ $choice -ge 0 && $choice -lt ${#configs[@]} ]]; then
        IFS=':' read -r server share username mount_point <<< "${configs[$choice]}"
        
        if [[ -n "$username" ]]; then
            read -s -p "请输入密码: " password
            echo
        fi
        
        mount_share "$server" "$share" "$username" "$password" "$mount_point"
    else
        log_error "无效选择"
    fi
}

# 交互式连接
interactive_connect() {
    echo "=== 交互式Samba连接 ==="
    
    read -p "服务器IP或主机名: " server
    read -p "共享名: " share
    read -p "用户名 (留空为匿名): " username
    
    if [[ -n "$username" ]]; then
        read -s -p "密码: " password
        echo
    fi
    
    local mount_point="$MOUNT_BASE/${server}_${share}"
    read -p "挂载点 [$mount_point]: " custom_mount
    if [[ -n "$custom_mount" ]]; then
        mount_point="$custom_mount"
    fi
    
    # 测试连接
    if test_connection "$server" "$share" "$username"; then
        # 挂载
        if mount_share "$server" "$share" "$username" "$password" "$mount_point"; then
            # 询问是否保存配置
            read -p "是否保存此连接配置？ (y/n): " save_choice
            if [[ "$save_choice" == "y" || "$save_choice" == "Y" ]]; then
                save_config "$server" "$share" "$username" "$mount_point"
            fi
        fi
    fi
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
Samba客户端连接脚本

用法: ./samba-client.sh [选项]

选项:
    install         安装Samba客户端依赖
    scan            扫描网络中的Samba服务器
    list <server>   列出服务器的共享
    connect         交互式连接
    quick           使用保存的配置快速连接
    configs         显示保存的配置
    mounted         显示已挂载的共享
    umount <path>   卸载指定路径
    umount-all      卸载所有Samba共享
    help            显示此帮助信息

示例:
    ./samba-client.sh scan
    ./samba-client.sh list 192.168.1.100
    ./samba-client.sh connect
    ./samba-client.sh quick
EOF
}

# 卸载所有Samba共享
umount_all() {
    log_info "卸载所有Samba共享..."
    
    local mounted_shares
    mounted_shares=$(mount | grep cifs | awk '{print $3}')
    
    if [[ -z "$mounted_shares" ]]; then
        log_info "没有已挂载的Samba共享"
        return
    fi
    
    for share in $mounted_shares; do
        umount_share "$share"
    done
}

# 主函数
main() {
    case "${1:-help}" in
        "install")
            install_client
            ;;
        "scan")
            check_dependencies
            scan_servers
            ;;
        "list")
            check_dependencies
            if [[ -z "$2" ]]; then
                log_error "请指定服务器地址"
                exit 1
            fi
            read -p "用户名 (留空为匿名): " username
            list_shares "$2" "$username"
            ;;
        "connect")
            check_dependencies
            interactive_connect
            ;;
        "quick")
            check_dependencies
            quick_connect
            ;;
        "configs")
            load_configs
            ;;
        "mounted")
            show_mounted
            ;;
        "umount")
            if [[ -z "$2" ]]; then
                log_error "请指定挂载点"
                exit 1
            fi
            umount_share "$2"
            ;;
        "umount-all")
            umount_all
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"