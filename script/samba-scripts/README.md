# Samba配置脚本使用指南

本文件夹包含用于安装和配置Samba服务器及客户端的自动化脚本。

## 📁 文件结构

```
samba-scripts/
├── install-samba-server.sh    # Samba服务器安装配置脚本
├── samba-client.sh            # Samba客户端连接脚本
└── README.md                  # 使用说明文档
```

## 🚀 快速开始

### 1. Samba服务器安装

在需要作为文件服务器的机器上运行：

```bash
sudo ./install-samba-server.sh
```

**脚本功能：**
- 自动检测操作系统 (Ubuntu/Debian/CentOS/RHEL)
- 安装Samba服务器软件包
- 配置防火墙规则
- 创建共享目录 (`/srv/samba/shared`)
- 生成优化的Samba配置文件
- 创建Samba用户账户
- 启动并启用Samba服务
- 显示连接信息

### 2. Samba客户端使用

在需要访问共享文件的机器上使用：

```bash
# 安装客户端依赖
./samba-client.sh install

# 扫描网络中的Samba服务器
./samba-client.sh scan

# 交互式连接
./samba-client.sh connect

# 快速连接（使用保存的配置）
./samba-client.sh quick
```

## 📋 详细功能说明

### Samba服务器脚本 (install-samba-server.sh)

#### 支持的操作系统
- Ubuntu/Debian 系列
- CentOS/RHEL/Rocky Linux 系列

#### 主要功能
1. **自动安装** - 根据系统类型安装相应的Samba包
2. **防火墙配置** - 自动配置UFW或firewalld规则
3. **共享目录** - 创建 `/srv/samba/shared` 公共共享目录
4. **优化配置** - 包含性能优化和字符集配置
5. **用户管理** - 创建系统用户和Samba用户
6. **服务管理** - 启动并设置开机自启

#### 默认共享配置
- **shared** - 公共共享目录，所有用户可读写
- **homes** - 用户家目录共享，仅用户本人访问

### Samba客户端脚本 (samba-client.sh)

#### 主要命令

| 命令 | 说明 |
|------|------|
| `install` | 安装Samba客户端依赖 |
| `scan` | 扫描网络中的Samba服务器 |
| `list <server>` | 列出指定服务器的共享 |
| `connect` | 交互式连接向导 |
| `quick` | 使用保存的配置快速连接 |
| `configs` | 显示保存的连接配置 |
| `mounted` | 显示已挂载的共享 |
| `umount <path>` | 卸载指定挂载点 |
| `umount-all` | 卸载所有Samba共享 |

#### 配置文件
客户端配置保存在 `~/.samba-client.conf`，包含：
- 服务器地址
- 共享名称
- 用户名
- 挂载点路径

## 🔧 使用示例

### 服务器端设置

1. **安装Samba服务器**
```bash
sudo ./install-samba-server.sh
```

2. **按提示创建用户**
```
请输入用户名: alice
为用户 alice 设置Samba密码
New SMB password: [输入密码]
Retype new SMB password: [再次输入密码]
```

3. **记录连接信息**
```
服务器IP: *************
共享路径: //server-name/shared
用户共享: //server-name/homes
```

### 客户端连接

1. **安装客户端工具**
```bash
./samba-client.sh install
```

2. **扫描服务器**
```bash
./samba-client.sh scan
```

3. **交互式连接**
```bash
./samba-client.sh connect
```
按提示输入：
- 服务器IP：`*************`
- 共享名：`shared`
- 用户名：`alice`
- 密码：`[用户密码]`

4. **使用保存的配置快速连接**
```bash
./samba-client.sh quick
```

## 🌐 不同平台连接方式

### Windows客户端
1. 打开文件资源管理器
2. 在地址栏输入：`\\*************`
3. 输入用户名和密码
4. 访问共享文件夹

### macOS客户端
1. 打开Finder
2. 按 `Cmd + K` 或选择"前往" -> "连接服务器"
3. 输入：`smb://*************`
4. 输入用户名和密码

### Linux图形界面
- **Nautilus (GNOME)**：在地址栏输入 `smb://*************`
- **Dolphin (KDE)**：在地址栏输入 `smb://*************`
- **Thunar (XFCE)**：在地址栏输入 `smb://*************`

## 🔒 安全注意事项

1. **用户权限**
   - 使用专门的Samba用户，避免使用root
   - 定期更换密码
   - 启用用户账户锁定策略

2. **网络安全**
   - 仅在受信任的网络中使用
   - 考虑使用VPN连接远程访问
   - 定期更新Samba软件

3. **文件权限**
   - 合理设置共享目录权限
   - 避免共享系统敏感目录
   - 定期审查共享配置

## 🛠️ 故障排除

### 常见问题

1. **无法连接服务器**
   - 检查防火墙设置
   - 确认Samba服务运行状态：`systemctl status smbd`
   - 验证网络连通性：`ping 服务器IP`

2. **权限被拒绝**
   - 确认用户名和密码正确
   - 检查共享配置中的用户权限
   - 验证文件系统权限

3. **中文文件名乱码**
   - 服务器端配置已包含UTF-8字符集设置
   - 客户端挂载时使用 `iocharset=utf8` 选项

4. **性能问题**
   - 配置文件已包含性能优化参数
   - 考虑调整网络MTU大小
   - 检查网络带宽和延迟

### 日志查看

**服务器端日志**
```bash
# 查看Samba日志
sudo tail -f /var/log/samba/log.smbd

# 查看系统日志
sudo journalctl -u smbd -f
```

**客户端诊断**
```bash
# 测试连接
smbclient -L //服务器IP -U 用户名

# 查看挂载信息
mount | grep cifs
```

## 📚 参考资料

- [Samba官方文档](https://www.samba.org/samba/docs/)
- [CIFS/SMB协议说明](https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-smb2/)
- [Linux CIFS客户端](https://wiki.samba.org/index.php/LinuxCIFS_utils)

## 🤝 贡献与反馈

如果您在使用过程中遇到问题或有改进建议，请：
1. 检查日志文件获取详细错误信息
2. 确认系统环境和网络配置
3. 提供具体的错误信息和操作步骤

---

*最后更新时间：2024年*