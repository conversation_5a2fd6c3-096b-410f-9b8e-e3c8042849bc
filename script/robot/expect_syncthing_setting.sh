#!/usr/bin/expect

# 设置超时时间（秒），0表示永不超时
set timeout 4

# 获取传递的参数
set user [lindex $argv 0]
set host [lindex $argv 1]
set port [lindex $argv 2]
set password [lindex $argv 3]

set config_file_path "/home/<USER>/.local/state/syncthing/config.xml"

set cmd "sed -i 's|<address>127.0.0.1:8384</address>|<address>0.0.0.0:8384</address>|g' '$config_file_path'"

# 启动SSH连接
spawn ssh $user@$host -p $port

# 等待密码提示
expect {
    -re ".*ros:foxy.*" {
        puts "匹配成功: $expect_out(0,string)"
        send "1\r"
        exp_continue
    }
    "password:" {
        send "$password\r"
        exp_continue
    }
    "*yes/no*" {
        send "yes\r"
        exp_continue
    }
    "*Y/n*" {
        send "Y\r"
        exp_continue
    }
    "foxy"{
        send "1\r"
    }
    "$ " {
        puts "ok"
    }
    "*~ " {
        puts "ok"
    }
    "*# " {
        puts "ok"
    }
    "%" {
        puts "ok"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

set timeout 10
# 在这里可以添加你想要执行的命令
send "cd syncthing && sudo cp ./syncthing /usr/bin/\r"

expect {
    "password:" {
        send "$password\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "sudo cp -rf ./linux-systemd/* /etc/systemd/\r"
    }
    -re "#.*" {
        send "sudo cp -rf ./linux-systemd/* /etc/systemd/\r"
    }
    "$ " {
        send "sudo cp -rf ./linux-systemd/* /etc/systemd/\r"
    }
    "~ " {
        send "sudo cp -rf ./linux-systemd/* /etc/systemd/\r"
    }
    "*# " {
        send "sudo cp -rf ./linux-systemd/* /etc/systemd/\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    "password:" {
        send "$password\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "sudo systemctl enable syncthing@$user\r"
    }
    -re "#.*" {
        send "sudo systemctl enable syncthing@$user\r"
    }
    "$ " {
        send "sudo systemctl enable syncthing@$user\r"
    }
    "~ " {
        send "sudo systemctl enable syncthing@$user\r"
    }
    "*# " {
        send "sudo systemctl enable syncthing@$user\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    -re ".*assword.*" {
        send "$password\r"
        exp_continue
    }
    -re ".*Choose identity to authenticate.*" {
        send "2\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "sudo systemctl start syncthing@$user\r"
    }
    -re "#.*" {
        send "sudo systemctl start syncthing@$user\r"
    }
    "$ " {
        send "sudo systemctl start syncthing@$user\r"
    }
    "~ " {
        send "sudo systemctl start syncthing@$user\r"
    }
    "*# " {
        send "sudo systemctl start syncthing@$user\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    -re ".*assword.*" {
        send "$password\r"
        exp_continue
    }
    -re ".*Choose identity to authenticate.*" {
        send "2\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "sleep 5\r"
    }
    -re "#.*" {
        send "sleep 5\r"
    }
    "$ " {
        send "sleep 5\r"
    }
    "~ " {
        send "sleep 5\r"
    }
    "*# " {
        send "sleep 5\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    "password:" {
        send "$password\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "$cmd\r"
    }
    -re "#.*" {
        send "$cmd\r"
    }
    "$ " {
        send "$cmd\r"
    }
    "~ " {
        send "$cmd\r"
    }
    "*# " {
        send "$cmd\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    -re ".*assword.*" {
        send "$password\r"
        exp_continue
    }
    -re ".*Choose identity to authenticate.*" {
        send "2\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "sudo systemctl restart syncthing@$user\r"
    }
    -re "#.*" {
        send "sudo systemctl restart syncthing@$user\r"
    }
    "$ " {
        send "sudo systemctl restart syncthing@$user\r"
    }
    "~ " {
        send "sudo systemctl restart syncthing@$user\r"
    }
    "*# " {
        send "sudo systemctl restart syncthing@$user\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect {
    -re ".*assword.*" {
        send "exit\r"
        exp_continue
    }
    -re ".*Choose identity to authenticate.*" {
        send "exit\r"
        exp_continue
    }
    -re ".*\u279C.*syncthing" {
        send "exit\r"
    }
    "$ " {
        send "exit\r"
    }
    "~ " {
        send "exit\r"
    }
    "*# " {
        send "exit\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}



expect eof
# 等待退出完成

# catch wait puts architecture

# exit $architecture
# 返回架构
# puts "$architecture"