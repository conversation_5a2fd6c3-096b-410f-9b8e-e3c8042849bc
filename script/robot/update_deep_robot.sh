#!/bin/bash

# 定义颜色常量
RESET="\033[0m"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33m"
BLUE="\033[34m"
MAGENTA="\033[35m"
CYAN="\033[36m"
WHITE="\033[37m"


# 定义源目录
SOURCE_DIR="/mine/robot/robot-application"

# 服务器列表及其对应的远程路径和SSH端口
declare -A SERVERS=(
    ["43022.nevin.email"]="/mine/:43022"
    ["45022.nevin.email"]="/mine/:45022"
    ["47022.nevin.email"]="/mine/:47022"
    ["49022.nevin.email"]="/mine/:49022"
    ["51022.nevin.email"]="/mine/:51022"
    ["53022.nevin.email"]="/mine/:53022"
)

# 定义一个数组来保存后台进程的PID
pids=()

ssh_user=root

# 遍历服务器列表
for server in "${!SERVERS[@]}"; do
    IFS=':' read -r remote_path port <<< "${SERVERS[$server]}"
    
    # 构建 rsync 命令
    # rsync_command="rsync -avz --progress $SOURCE_DIR ${ssh_user}@${server}:${remote_path}"
    # rsync_command="rsync -avz --progress -e 'ssh -p $port' $SOURCE_DIR ${ssh_user}@${server}:${remote_path}"
    
    # 执行 rsync 命令
    echo -e "${BLUE}Syncing to $server:$remote_path using SSH port $port${RESET} "
	
	# 使用 expect 自动输入 yes
    expect -c "
        set timeout 5
        spawn rsync -avz --progress -e \"ssh -p ${port}\"  ${SOURCE_DIR} ${ssh_user}@${server}:${remote_path}
        expect {
            \"yes/no/\" { send \"yes\\r\"; exp_continue }
            eof { exit 0 }
        }
    "
    # eval "$rsync_command &"
	pids+=("$!")
done

# 等待所有后台进程完成
echo "Waiting for all rsync processes to complete..."
wait

echo "All rsync processes have completed."