#!/usr/bin/expect

# #!/bin/bash

# USER1="cat"
# USER2="cat"
# HOST1="*************"
# HOST2="*************"
# PORT="21422"

# if [ -n "$1" ]; then
#     PORT=$1
# fi
# if [ -n "$2" ]; then
#     USER2=$2
# fi

SCRIPT_PATH=$(readlink -f "$0")
echo "$SCRIPT_PATH"
SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

# ubuntu_software_path=$SCRIPT_DIR
# rsyncthing_bin="syncthing-linux-arm64-v1.29.0/syncthing"
# rsyncthing_config="syncthing-linux-arm64-v1.29.0/etc/linux-systemd/"
# config_file_path="/home/<USER>/.local/state/syncthing/config.xml"

# # 定义 scp 传输函数
# rsc_transfer() {
#     # 检查参数数量
#     if [ "$#" -ne 4 ]; then
#         echo "Usage: scp_transfer <source_file> <target_host> <target_path>"
#         return 1
#     fi

#     local source_file=$1
#     local target_host=$2
#     local target_port=$3
#     local target_path=$4

#     # 检查源文件是否存在
#     if [ ! -f "$source_file" ]; then
#         if [ ! -d "$source_file" ]; then
#             echo "Error: Source file $source_file is not a regular file."
#             return 1
#         fi
#     fi

#     # 执行 scp 命令
#     rsync -rvz --progress -e "ssh -p  ${target_port}"  $source_file   --exclude-from=$SCRIPT_DIR/exclude.txt   $target_host:$target_path
#     if [ $? -eq 0 ]; then
#         echo "File $source_file successfully transferred to $target_host:$target_path"
#     else
#         echo "Error: Failed to transfer file $source_file to $target_host:$target_path"
#         return 1
#     fi
# }

# rsc_transfer ${ubuntu_software_path}/${rsyncthing_bin} ${USER2}@${HOST2} ${PORT} /usr/bin/
# rsc_transfer ${ubuntu_software_path}/${rsyncthing_config} ${USER2}@${HOST2} ${PORT} /etc/systemd/



# 设置超时时间（秒），0表示永不超时
set timeout 5

# 定义变量
set user "root"
# set user "cat"
set host "nevin.email"
# set host "***********"
set password "'"

spawn scp -r .ssh  ${user}@${host}:~/

# 启动SSH连接
spawn ssh $user@$host

# 等待密码提示
expect {
    "password:" {
        send "$password\r"
        exp_continue
    }
    "*yes/no*" {
        send "yes\r"
        exp_continue
    }
    "*Y/n*" {
        send "Y\r"
        exp_continue
    }
}

# 等待登录成功
expect "$ "

# 在这里可以添加你想要执行的命令
send "ls -la\r"

# 等待命令执行完成
expect "$ "

# 退出SSH会话
send "exit\r"

# 等待退出完成
expect eof



# # 自动登录并执行命令
# sshpass -p "your_password" ssh -o StrictHostKeyChecking=no $USER2@$HOST2 -p $PORT << EOF
# echo "开始执行"

# systemctl enable syncthing@$USER2
# systemctl start syncthing@$USER2
# sleep 5
# sed -i 's|<address>127.0.0.1:8384</address>|<address>0.0.0.0:8384</address>|g' "$config_file_path"
# systemctl restart syncthing@$USER2
# echo "修改完成"

# EOF
