#!/bin/bash

KEY=2448c1137ca178909153ddef36576838

# https://restapi.amap.com/v3/direction/walking?origin=39.90909,116.434307&destination=39.90816,116.434446&key=2448c1137ca178909153ddef36576838

ORIGIN_LONGITUDE=$1
ORIGIN_LATITUDE=$2
DESTINATION_LONGITUDE=$3
DESTINATION_LATITUDE=$4

# 检查参数数量
if [ "$#" -lt 4 ]; then
    echo "Usage: $0 <origin_longitude> <origin_latitude> <destination_longitude> <destination_latitude>"
    exit 1
fi

API_URL="https://restapi.amap.com/v3/direction/walking?origin=${ORIGIN_LONGITUDE},${ORIGIN_LATITUDE}&destination=${DESTINATION_LONGITUDE},${DESTINATION_LATITUDE}&key=${KEY}"

curl -s "$API_URL"


https://restapi.amap.com/v3/direction/walking?origin=116.434307,39.90909&destination=116.434446,39.90816&key=2448c1137ca178909153ddef36576838
