[common]
server_addr=127.0.0.1:8024
conn_type=tcp
vkey=123
auto_reconnection=true
max_conn=1000
flow_limit=1000
rate_limit=1000
basic_username=11
basic_password=3
web_username=user
web_password=1234
crypt=true
compress=true
#pprof_addr=0.0.0.0:9999
disconnect_timeout=60

[health_check_test1]
health_check_timeout=1
health_check_max_failed=3
health_check_interval=1
health_http_url=/
health_check_type=http
health_check_target=127.0.0.1:8083,127.0.0.1:8082

[health_check_test2]
health_check_timeout=1
health_check_max_failed=3
health_check_interval=1
health_check_type=tcp
health_check_target=127.0.0.1:8083,127.0.0.1:8082

[web]
host=c.o.com
target_addr=127.0.0.1:8083,127.0.0.1:8082

[tcp]
mode=tcp
target_addr=127.0.0.1:8080
server_port=10000

[socks5]
mode=socks5
server_port=19009
multi_account=multi_account.conf

[file]
mode=file
server_port=19008
local_path=/Users/<USER>/Downloads
strip_pre=/web/

[http]
mode=httpProxy
server_port=19004

[udp]
mode=udp
server_port=12253
target_addr=***************:53

[ssh_secret]
mode=secret
password=ssh2
target_addr=*************:22

[ssh_p2p]
mode=p2p
password=ssh3

[secret_ssh]
local_port=2001
password=ssh2

[p2p_ssh]
local_port=2002
password=ssh3
target_addr=*************:22