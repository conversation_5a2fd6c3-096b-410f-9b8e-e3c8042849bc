#!/bin/bash

# 定义用户名
USERNAME="xuhui"
if [ -n "$1" ]; then
    USERNAME=$1
fi

# 检查是否以 root 用户运行脚本
if [ "$EUID" -ne 0 ]; then
  echo "请以 root 用户运行此脚本。"
  exit 1
fi

# 检查用户是否存在
if ! id "$USERNAME" &>/dev/null; then
  echo "用户 $USERNAME 不存在。请先创建用户。"
  exit 1
fi

# 将用户添加到 sudo 组
usermod -aG sudo "$USERNAME"
echo "用户 $USERNAME 已添加到 sudo 组。"

# 备份 sudoers 文件
cp /etc/sudoers /etc/sudoers.bak
echo "备份 /etc/sudoers 文件到 /etc/sudoers.bak"

# 使用 visudo 添加免密 sudo 规则
echo "$USERNAME ALL=(ALL) NOPASSWD: ALL" | EDITOR='tee -a' visudo
echo "配置用户 $USERNAME 免密 sudo 完成。"

# 验证配
echo "验证配置..."
su - "$USERNAME" -c "sudo whoami"
if [ $? -eq 0 ]; then
  echo "用户 $USERNAME 可以免密使用 sudo。"
else
  echo "配置失败，请检查 /etc/sudoers 文件。"
  exit 1
fi