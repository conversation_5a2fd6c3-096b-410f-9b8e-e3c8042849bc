#!/bin/bash

# 定义远程服务器配置
declare -A SERVERS=(
    ["45022"]="<EMAIL>:45022"
    ["46022"]="<EMAIL>:46022"
    ["51022"]="<EMAIL>:51022"
    ["52022"]="<EMAIL>:52022"
    ["53022"]="<EMAIL>:53022"
    ["54022"]="<EMAIL>:54022"
)

SSH_CONNECT_TIMEOUT=10
# 定义要清理的目录和文件匹配模式
TARGET_DIR="/mine/robot-application"
FILE_PATTERN="*.sync-conflict*"  # 例如：删除所有.log文件
# 可选：定义文件保留时间（天）
DAYS_TO_KEEP=0

# 清理函数
cleanup_server() {
    local SERVER_NAME=$1
    local SERVER_CONFIG=$2
    
    # 解析服务器配置
    IFS=':' read -r SSH_HOST SSH_PORT <<< "$SERVER_CONFIG"
    
    echo "正在清理 $SERVER_NAME ($SSH_HOST) ..."
    
    # SSH连接并执行删除命令
    ssh -o ConnectTimeout=$SSH_CONNECT_TIMEOUT  -p "$SSH_PORT" "$SSH_HOST" << EOF
        # 删除指定天数以前的文件
        # find $TARGET_DIR -name "$FILE_PATTERN" -type f -mtime +$DAYS_TO_KEEP -exec rm -f {} \;
        find $TARGET_DIR -name "$FILE_PATTERN" -type f -exec rm -f {} \;
        
        # 输出删除后的状态
        echo "$SERVER_NAME 清理完成"
        echo "剩余文件数量："
        find $TARGET_DIR -name "$FILE_PATTERN" -type f | wc -l
EOF

    if [ $? -eq 0 ]; then
        echo "$SERVER_NAME 清理成功"
    else
        echo "$SERVER_NAME 清理失败"
    fi
}

# 主函数：清理所有服务器
cleanup_all_servers() {
    for SERVER_NAME in "${!SERVERS[@]}"; do
        cleanup_server "$SERVER_NAME" "${SERVERS[$SERVER_NAME]}"
    done
}

# 执行清理
cleanup_all_servers