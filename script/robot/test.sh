#!/bin/bash

# 定义源目录
SOURCE_DIR="/mine/robot/robot-application"

# 目标服务器及其对应的远程路径
TARGET_SERVER="<EMAIL>"
REMOTE_PATH="/mine/"

# 构建 rsync 命令
# shellcheck disable=SC2037
# rsync_command=rsync -avz --progress $SOURCE_DIR $TARGET_SERVER:$REMOTE_PATH

# 使用 expect 自动输入 yes
expect -c "
    set timeout 30
    spawn rsync -avz --progress $SOURCE_DIR $TARGET_SERVER:$REMOTE_PATH
    expect {
        \"(y/n)\" { send \"y\\r\"; exp_continue }
        \"Do you want to continue?\" { send \"y\\r\"; exp_continue }
        eof { exit 0 }
    }
"