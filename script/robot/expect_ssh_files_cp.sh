#!/usr/bin/expect

# 设置超时时间（秒），0表示永不超时
set timeout 3

# 获取传递的参数
set user [lindex $argv 0]
set host [lindex $argv 1]
set port [lindex $argv 2]
set password [lindex $argv 3]
set ssh_files_dir [lindex $argv 4]

# 输出调试信息
puts "User: $user"
puts "Host: $host"
puts "Port: $port"
puts "Password: $password"
puts "SSH Directory: $ssh_files_dir"

# 启动SSH连接
spawn scp -P ${port} -r ${ssh_files_dir}  ${user}@${host}:~/

# 等待密码提示
expect {
    "password" {
        send "$password\r"
        exp_continue
    }
    "*yes/no*" {
        send "yes\r"
        exp_continue
    }
    "*Y/n*" {
        send "Y\r"
        exp_continue
    }
}