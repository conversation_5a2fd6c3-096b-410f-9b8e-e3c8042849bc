#!/usr/bin/expect

# 设置超时时间（秒），0表示永不超时
set timeout 3

# 获取传递的参数
set user [lindex $argv 0]
set host [lindex $argv 1]
set port [lindex $argv 2]
set password [lindex $argv 3]

# 启动SSH连接
spawn ssh $user@$host -p $port

# 等待密码提示
expect {
    -re ".*ros:foxy.*" {
        puts "匹配成功: $expect_out(0,string)"
        send "1\r"
        exp_continue
    }
    -re "\$|>|#|~|% " {
        puts "Login successful."
    }
    "password:" {
        puts "Password:$password"
        send "$password\r"
        exp_continue
    }
    "yes/no" {
        send "yes\r"
        exp_continue
    }
    "Y/n" {
        send "Y\r"
        exp_continue
    }
    "ros\:foxy"{
        send "1\r"
        exp_continue
    }
    "$" {
        puts "Login successful."
    }
    "#" {
        puts "Login successful."
    }
    "~" {
        puts "Login successful."
    }
    "%" {
        puts "Login successful."
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

# # 获取远程服务器的架构
# send "uname -m\r"
# expect {
#     -re {([a-zA-Z0-9_]+)\r\n} {
#         set architecture $expect_out(1,string)
#         puts "$architecture"
#     }
#     timeout {
#         puts "Timeout occurred while waiting for architecture."
#         exit 1
#     }
#     eof {
#         puts "Unexpected EOF while waiting for architecture."
#         exit 1
#     }
# }

# 在这里可以添加你想要执行的命令
send "chown $user:$user ~/.ssh -R \r"

# 等待命令执行完成
# 退出SSH会话
expect {
    "$ " {
        send "exit\r"
    }
    "*# " {
        send "exit\r"
    }
    "~" {
        send "exit\r"
    }
    "%" {
        send "exit\r"
    }
    timeout {
        puts "22Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "33Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect eof
# 等待退出完成

# catch wait puts architecture

# exit $architecture
# 返回架构
# puts "$architecture"