#!/bin/bash

# 定义变量
USER="ysc"
# USER="ubuntu"
HOST="*************"
# HOST="nevin.email"
PORT="22"
PASSWORD="'"
# PASSWORD="1"
NPC_VKEY="rm9oy5d2fl8atyhu"

# 特定网卡的名称
INTERFACE="eth0"
# 文件名
FILE="devices.txt"

if [ -n "$1" ]; then
    HOST=$1
	if [ "$HOST" = "*************" ]; then
        USER="ysc"
		PASSWORD="'"
	elif [ "$HOST" = "*************" ]; then
		USER="cat"
		PASSWORD="temppwd"
	elif [ "$HOST" = "*************" ]; then
        USER="nvidia"
		PASSWORD="nvidia"
    fi
fi
if [ -n "$2" ]; then
    PORT=$2
fi


if [ "$USER" = "root" ]; then
	USER_DIR="/$USER"
else
	USER_DIR="/home/<USER>"
fi

# 定义 scp 传输函数
rsc_transfer() {
    # 检查参数数量
    if [ "$#" -ne 4 ]; then
        echo "Usage: scp_transfer <source_file> <target_host> <target_path>"
        return 1
    fi

    local source_file=$1
    local target_host=$2
    local target_port=$3
    local target_path=$4

    # 检查源文件是否存在
    if [ ! -f "$source_file" ]; then
        if [ ! -d "$source_file" ]; then
            echo "Error: Source file $source_file is not a regular file."
            return 1
        fi
    fi

    # 执行 scp 命令
    rsync -rvz --progress -e "ssh -p  ${target_port}"  $source_file   --exclude-from=./exclude.txt   $target_host:$target_path
    if [ $? -eq 0 ]; then
        echo "File $source_file successfully transferred to $target_host:$target_path"
    else
        echo "Error: Failed to transfer file $source_file to $target_host:$target_path"
        return 1
    fi
}

SCRIPT_PATH=$(readlink -f "$0")
echo "当前运行的文件:$SCRIPT_PATH"
SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "当前文件夹路径：$SCRIPT_DIR"

SSH_DIR="${SCRIPT_DIR}/.ssh"

# # 调用 expect 脚本并传递参数
expect expect_ssh_files_cp.sh $USER $HOST $PORT $PASSWORD $SSH_DIR

expect expect_ssh_chown.sh $USER $HOST $PORT $PASSWORD

# ARCHITECTURE=$(expect -f expect_ssh_chown.exp $USER $HOST $PORT $PASSWORD)
# 输出捕获的架构信息
# echo "Captured Architecture: ${ARCHITECTURE}"


ARCHITECTURE=$(ssh -p ${PORT} ${USER}@${HOST} "uname -m")
if [ $? -ne 0 ]; then
    echo "Error: Failed to get architecture from remote server."
    exit 1
fi
echo "Captured Architecture: ${ARCHITECTURE}"




# 使用 SSH 获取特定网卡的 MAC 地址
# MAC_ADDRESS=$(ssh -p ${PORT} ${USER}@${HOST} "ip link show ${INTERFACE} | awk '/ether/ {print \$2}' | tr -d ':'")
MAC_ADDRESS=$(ssh -p ${PORT} ${USER}@${HOST} "ip link show ${INTERFACE} | awk '/ether/ {print \$2}' ")

# 输出 MAC 地址
echo "The MAC address of ${INTERFACE} on ${REMOTE_IP} is: ${MAC_ADDRESS}"



# 读取文件并通过 MAC 地址匹配获取 SN 和 VKEY
IFS=',' read -r SN NPC_VKEY < <(awk -F, -v mac="$MAC_ADDRESS" '$1 == mac {print $2","$3}' "$FILE")

# 检查是否找到匹配的 SN 和 NPC_VKEY
if [ -z "$SN" ] || [ -z "$NPC_VKEY" ]; then
    echo "No SN or NPC_VKEY found for MAC address $MAC_ADDRESS"
	exit
else
    echo "The SN for MAC address $MAC_ADDRESS is: $SN"
    echo "The NPC_VKEY for MAC address $MAC_ADDRESS is: $NPC_VKEY"
fi


if [ "$USER" = "root" ]; then
    USER_DIR="/$USER"
else
    USER_DIR="/home/<USER>"
fi

OTHER_BIN_PATH="${SCRIPT_DIR}/bin/${ARCHITECTURE}/"
NPC_PATH="${SCRIPT_DIR}/npc/${ARCHITECTURE}/"
ZSH_PATH="${SCRIPT_DIR}/zsh"
SYNCTHING_PATH="${SCRIPT_DIR}/syncthing/${ARCHITECTURE}/"

rsc_transfer ${OTHER_BIN_PATH} ${USER}@${HOST} ${PORT} ${USER_DIR}/bin/
rsc_transfer ${NPC_PATH} ${USER}@${HOST} ${PORT} ${USER_DIR}/npc
rsc_transfer ${ZSH_PATH} ${USER}@${HOST} ${PORT} ${USER_DIR}/
rsc_transfer ${SYNCTHING_PATH} ${USER}@${HOST} ${PORT} ${USER_DIR}/syncthing

expect expect_sudoer_setting.sh $USER $HOST $PORT $PASSWORD
expect expect_other_bin_setting.sh $USER $HOST $PORT $PASSWORD
expect expect_zsh_setting.sh $USER $HOST $PORT $PASSWORD
expect expect_npc_setting.sh $USER $HOST $PORT $PASSWORD $NPC_VKEY
expect expect_syncthing_setting.sh $USER $HOST $PORT $PASSWORD






