#!/usr/bin/expect

# 设置超时时间（秒），0表示永不超时
set timeout 5

# 获取传递的参数
set user [lindex $argv 0]
set host [lindex $argv 1]
set port [lindex $argv 2]
set password [lindex $argv 3]

puts "Password: $password"

# 启动SSH连接
spawn ssh $user@$host -p $port

# 等待密码提示
expect {
    -re ".*ros:foxy.*" {
        puts "匹配成功: $expect_out(0,string)"
        send "1\r"
        exp_continue
    }
    "*password" {
        send "$password\r"
        exp_continue
    }
    "*yes/no*" {
        send "yes\r"
        exp_continue
    }
    "*Y/n*" {
        send "Y\r"
        exp_continue
    }
    "foxy"{
        send "1\r"
        put "1"
        exp_continue
    }
    "$ " {
        puts "ok"
    }
    "*~ " {
        puts "ok"
    }
    "*# " {
        puts "ok"
    }
    "%" {
        puts "ok"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

# set timeout 10
# 在这里可以添加你想要执行的命令
send "cd zsh && sudo ./sudoer.sh $user\r"

expect {
    -re ".*password.*" {
        puts "匹配成功: $expect_out(0,string)"
        send "$password\r"
        exp_continue
    }
    -re ".*ysc.*" {
        puts "发送单引号"
        send "$password\r"
        sleep 1
        exp_continue
    }
    -re ".*nvidia.*" {
        puts "发送单引号"
        send "$password\r"
        sleep 1
        exp_continue
    }
    -re "\u279C.*" {
        send "exit\r"
    }
    "$" {
        send "exit\r"
    }
    "~" {
        send "exit\r"
    }
    "#" {
        send "exit\r"
    }
    timeout {
        puts "Timeout occurred while waiting for login prompt."
        exit 1
    }
    eof {
        puts "Unexpected EOF while waiting for login prompt."
        exit 1
    }
}

expect eof
# 等待退出完成

# catch wait puts architecture

# exit $architecture
# 返回架构
# puts "$architecture"