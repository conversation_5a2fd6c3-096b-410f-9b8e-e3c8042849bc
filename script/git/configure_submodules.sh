#!/bin/bash

# Git子模块配置脚本 - 隐藏dirty状态
set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}=== Git子模块配置脚本 ===${NC}"
echo "配置Git忽略子模块的dirty状态"
echo ""

# 1. 全局配置
echo -e "${YELLOW}[1/4] 设置全局Git配置...${NC}"
git config --global diff.ignoreSubmodules dirty
git config --global status.submoduleSummary false
echo "✅ 全局配置完成"

# 2. 本地仓库配置
echo -e "${YELLOW}[2/4] 设置本地仓库配置...${NC}"
git config diff.ignoreSubmodules dirty
git config status.submoduleSummary false
git config submodule.recurse false
echo "✅ 本地配置完成"

# 3. 检查.gitmodules文件
echo -e "${YELLOW}[3/4] 检查.gitmodules配置...${NC}"
if [ -f ".gitmodules" ]; then
    echo "找到.gitmodules文件"
    
    # 检查是否已有ignore配置
    if grep -q "ignore = dirty" .gitmodules; then
        echo "✅ .gitmodules已配置ignore = dirty"
    else
        echo "⚠️  .gitmodules未配置ignore，已通过编辑器修改"
    fi
    
    # 显示子模块列表
    echo ""
    echo "当前子模块:"
    git submodule status | head -10
else
    echo "⚠️  未找到.gitmodules文件"
fi

# 4. 验证配置
echo -e "${YELLOW}[4/4] 验证配置效果...${NC}"

echo "当前Git配置:"
echo "  diff.ignoreSubmodules: $(git config diff.ignoreSubmodules || echo '未设置')"
echo "  status.submoduleSummary: $(git config status.submoduleSummary || echo '未设置')"
echo "  submodule.recurse: $(git config submodule.recurse || echo '未设置')"

echo ""
echo "测试git status输出:"
git status --porcelain | head -5 || echo "无变更"

echo ""
echo -e "${GREEN}=== 配置完成 ===${NC}"
echo ""
echo "📝 配置说明:"
echo "  - diff.ignoreSubmodules = dirty: 忽略子模块的dirty状态"
echo "  - status.submoduleSummary = false: 不显示子模块摘要"
echo "  - submodule.recurse = false: 不递归处理子模块"
echo "  - .gitmodules中添加了ignore = dirty配置"
echo ""
echo "🎯 效果:"
echo "  - git status不再显示子模块的-dirty状态"
echo "  - git diff不再显示子模块的dirty变更"
echo "  - 子模块的未提交更改将被忽略"
echo ""
echo "🔄 如需恢复显示子模块状态:"
echo "  git config --unset diff.ignoreSubmodules"
echo "  git config --unset status.submoduleSummary"
echo ""

# 额外的有用命令
echo "💡 有用的子模块命令:"
echo "  查看子模块状态: git submodule status"
echo "  更新所有子模块: git submodule update --remote"
echo "  初始化子模块: git submodule update --init --recursive"
echo "  清理子模块: git submodule foreach --recursive git clean -fd"
